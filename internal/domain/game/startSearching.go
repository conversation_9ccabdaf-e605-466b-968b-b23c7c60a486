package game

import (
	"context"
	"fmt"
	"math/rand/v2"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"matiksOfficial/matiks-server-go/internal/constants"
	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"
	"matiksOfficial/matiks-server-go/internal/domain/game/utils/botutils"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/middleware"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/zap"
)

const (
	MAX_WAITING_TIME_FOR_USER = 12 * time.Second
	BOT_AFTER_SEC             = 9 * time.Second
)

type botMatchResult struct {
	matchedUsers []*models.User
}

func (s *service) StartSearching(ctx context.Context, gameConfig *models.GameConfigInput) (*bool, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get user ID from context: %w", err)
	}

	zlog.Debug(ctx, "Start Searching")

	if err := s.validateGameConfig(gameConfig); err != nil {
		return nil, fmt.Errorf("invalid game configuration: %w", err)
	}

	currentUser, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	config, _, err := gameutils.GetNewGameConfigFields(gameConfig)
	if err != nil || config == nil {
		return nil, fmt.Errorf("failed to get game config: %w", err)
	}

	matchedUsers, err := s.playersQueue.AddUser(ctx, currentUser, config)
	if err != nil {
		return nil, fmt.Errorf("failed to add user to waiting queue: %w", err)
	}

	if matchedUsers == nil {
		searchCtx, cancel := context.WithTimeout(utils.DeriveContextWithoutCancel(ctx), MAX_WAITING_TIME_FOR_USER+2*time.Second)
		defer cancel()

		botMatchResultChan := make(chan *botMatchResult, 1)
		go s.handleSearchTimeout(searchCtx, currentUser, config, botMatchResultChan)

		select {
		case res := <-botMatchResultChan:
			if res != nil {
				game, err := s.startGameWithBot(ctx, res.matchedUsers, config)
				if err != nil {
					zlog.Error(ctx, "Failed to start game with bot", err)
					return nil, err
				}

				s.notifyMatchedPlayers(ctx, game, res.matchedUsers)

				s.scheduleEndGame(ctx, game)

			} else {
				zlog.Debug(ctx, "Game not found, skipping notifications")
			}
		case <-ctx.Done():
			zlog.Debug(ctx, "Parent context cancelled")
			if err := s.coreService.PublishUserEvent(context.Background(), userID, &models.SearchTimeoutEvent{Event: constants.UserEventEnum.SEARCH_TIMEOUT.String()}); err != nil {
				zlog.Error(ctx, "Failed to publish search timeout event", err)
			}
			return utils.AllocPtr(false), nil
		}

		success := true
		return &success, nil
	}

	gameType := gameutils.GetGameTypeFromGameConfig(config)
	// Create game with matched users
	players := s.getPlayersFromUsers(matchedUsers, gameType)
	game, err := s.createGameWithPlayers(ctx, players, config)
	if err != nil {
		return nil, fmt.Errorf("failed to create game: %w", err)
	}

	// Notify matched players
	s.notifyMatchedPlayers(ctx, game, matchedUsers)

	s.scheduleEndGame(ctx, game)

	zlog.Debug(ctx, "Game created successfully", zap.String("gameID", game.ID.Hex()))

	success := true
	return &success, nil
}

func (s *service) handleSearchTimeout(ctx context.Context, user *models.User, gameConfig *models.GameConfig, resultChan chan<- *botMatchResult) {
	zlog.Debug(ctx, "Entering handleSearchTimeout", zap.Any("gameConfig", gameConfig))

	isShadowBanned := false
	if user.IsShadowBanned != nil {
		isShadowBanned = *user.IsShadowBanned
	}

	botWaitTime := BOT_AFTER_SEC
	if isShadowBanned {
		botWaitTime = 1500 * time.Millisecond
		zlog.Debug(ctx, "Shadow banned user - using shorter bot match time",
			zap.Duration("duration", botWaitTime),
			zap.String("userID", user.ID.Hex()))
	}

	botTimer := time.NewTimer(botWaitTime)
	defer botTimer.Stop()

	zlog.Debug(ctx, "Starting bot timer", zap.Duration("duration", botWaitTime))

	select {
	case <-botTimer.C:
		zlog.Debug(ctx, "Bot timer completed")
		if isShadowBanned {
			zlog.Debug(ctx, "Shadow banned user - matching directly with bot")
			res := s.handleBotGame(ctx, user, gameConfig, isShadowBanned)
			resultChan <- res
			zlog.Debug(ctx, "Bot game handling completed for shadow banned user",
				zap.Bool("matchFound", res != nil),
				zap.String("userID", user.ID.Hex()))
			return
		}
		zlog.Debug(ctx, "Attempting to start game with human bot")
		res := s.handleHumanBotGame(ctx, user, gameConfig)
		if res != nil {
			resultChan <- res
			zlog.Debug(ctx, "Exiting handleSearchTimeout after successful human bot match")
			return
		}
		zlog.Debug(ctx, "No suitable human bot found, proceeding with regular bot")
		zlog.Debug(ctx, "Attempting to handle regular bot game")
		res = s.handleBotGame(ctx, user, gameConfig, isShadowBanned)
		resultChan <- res
		zlog.Debug(ctx, "Regular bot game handling completed", zap.Bool("matchFound", res != nil))
		return

	case <-ctx.Done():
		// TODO: notify user to search again or add retry logic @deepak
		zlog.Debug(ctx, "Search context cancelled before bot attempt", zap.Error(ctx.Err()))
		resultChan <- nil
		zlog.Debug(ctx, "Exiting handleSearchTimeout due to context cancellation")
		return
	}
}

func (s *service) handleBotGame(ctx context.Context, user *models.User, gameConfig *models.GameConfig, isShadowBanned bool) *botMatchResult {
	zlog.Debug(ctx, "Entering handleBotGame", zap.Any("gameConfig", gameConfig))

	if ok, err := s.playersQueue.IsUserPresentInWaitingList(ctx, user); err != nil {
		zlog.Error(ctx, "Failed to check if user is in waiting list", err)
		return nil
	} else if ok || isShadowBanned {
		zlog.Debug(ctx, "User found in waiting list")

		zlog.Debug(ctx, "Attempting to get bot user")
		botUser, err := botutils.GetBotUser(ctx, user, s.userRepo, s.userService, gameConfig)
		if err != nil {
			zlog.Error(ctx, "Failed to get bot user", err)
			return nil
		}
		if botUser == nil {
			zlog.Debug(ctx, "No suitable bot user found")
			return nil
		}
		zlog.Debug(ctx, "Bot user found", zap.String("botUserID", botUser.ID.Hex()))

		matchedUsers := []*models.User{user, botUser}
		zlog.Debug(ctx, "Matched users", zap.String("botUserID", botUser.ID.Hex()))

		numPlayers := gameutils.GetNumPlayersFromGameConfig(gameConfig)

		if numPlayers == nil {
			return nil
		}

		if len(matchedUsers) != *numPlayers {
			zlog.Warn(ctx, "Incorrect number of players for bot game", zap.Int("matchedUsers", len(matchedUsers)), zap.Int("requiredPlayers", *numPlayers))
			return nil
		}

		zlog.Debug(ctx, "Removing user from waiting list")
		_, err = s.playersQueue.RemoveUser(ctx, user.ID.Hex())
		if err != nil {
			zlog.Error(ctx, "Failed to remove user from queue", err)
			return nil
		}
		zlog.Debug(ctx, "User successfully removed from waiting list")

		zlog.Debug(ctx, "Exiting handleBotGame with successful match", zap.String("botUserID", botUser.ID.Hex()))
		return &botMatchResult{matchedUsers}
	}

	zlog.Debug(ctx, "User not in waiting list, skipping bot game")
	zlog.Debug(ctx, "Exiting handleBotGame without match")
	return nil
}

func (s *service) handleHumanBotGame(ctx context.Context, user *models.User, gameConfig *models.GameConfig) *botMatchResult {
	zlog.Debug(ctx, "Entering handleHumanBotGame", zap.Any("gameConfig", gameConfig))
	if ok, err := s.playersQueue.IsUserPresentInWaitingList(ctx, user); err != nil {
		zlog.Error(ctx, "Failed to check if user is in waiting list", err)
		return nil
	} else if ok {
		zlog.Debug(ctx, "User found in waiting list")

		probHumanBot := rand.Float64()
		if probHumanBot >= 0.4 {
			zlog.Debug(ctx, "Random skip human bot game")
			return nil
		}

		if gameConfig.GameType != models.GameTypePlayOnline {
			zlog.Debug(ctx, "Skipping human bot game")
			return nil
		}

		zlog.Debug(ctx, "Attempting to match human bot", zap.Int("userRating", *user.Rating))
		botUser, err := botutils.MatchHumanBot(ctx, s.userRepo, s.cache, *user.Rating, user.ID)
		if err != nil {
			zlog.Warn(ctx, "Failed to match human bot", zap.Error(err))
			return nil
		}

		if botUser == nil {
			zlog.Debug(ctx, "No suitable human bot found")
			return nil
		}

		zlog.Debug(ctx, "Human bot matched successfully", zap.String("botUserID", botUser.ID.Hex()))

		matchedUsers := []*models.User{user, botUser}

		numPlayers := gameutils.GetNumPlayersFromGameConfig(gameConfig)

		if numPlayers == nil {
			return nil
		}

		if len(matchedUsers) != *numPlayers {
			zlog.Warn(ctx, "Incorrect number of players for human bot game", zap.Int("matchedUsers", len(matchedUsers)), zap.Int("requiredPlayers", *numPlayers))
			return nil
		}

		zlog.Debug(ctx, "User still in waiting list, removing")
		_, err = s.playersQueue.RemoveUser(ctx, user.ID.Hex())
		if err != nil {
			zlog.Error(ctx, "Failed to remove user from queue", err)
			return nil
		}
		zlog.Debug(ctx, "User successfully removed from waiting list")

		zlog.Debug(ctx, "Exiting handleHumanBotGame with successful match", zap.String("botUserID", botUser.ID.Hex()))
		return &botMatchResult{matchedUsers}
	}
	zlog.Debug(ctx, "User not in waiting list, skipping bot game")
	zlog.Debug(ctx, "Exiting handleHumanBotGame without match")
	return nil
}

func (s *service) startGameWithBot(ctx context.Context, matchedUsers []*models.User, gameConfig *models.GameConfig) (*models.Game, error) {
	zlog.Debug(ctx, "Entering startGameWithBot", zap.Int("numUsers", len(matchedUsers)), zap.Any("gameConfig", gameConfig))

	gameType := gameutils.GetGameTypeFromGameConfig(gameConfig)
	fmt.Println("RITESH : Game Type ", gameType)
	players := s.getPlayersFromUsers(matchedUsers, gameType)
	zlog.Debug(ctx, "Players created", zap.Int("numPlayers", len(players)))

	game, err := s.createGameWithPlayers(ctx, players, gameConfig)
	if err != nil {
		zlog.Error(ctx, "Failed to create game with bot", err)
		return nil, fmt.Errorf("failed to create game with bot: %w", err)
	}
	zlog.Debug(ctx, "Game created successfully", zap.String("gameID", game.ID.Hex()))

	var botUser *models.User
	for _, u := range matchedUsers {
		if (u.IsHumanBot != nil && *u.IsHumanBot) || (u.IsBot != nil && *u.IsBot) {
			botUser = u
			break
		}
	}

	if botUser == nil {
		zlog.Debug(ctx, "No bot user found in matched users", zap.String("gameID", game.ID.Hex()))
		return nil, fmt.Errorf("no bot user found")
	}

	zlog.Debug(ctx, "Bot user identified", zap.String("botUserID", botUser.ID.Hex()), zap.Bool("isHumanBot", botUser.IsHumanBot != nil && *botUser.IsHumanBot))

	zlog.Debug(ctx, "Starting game with bot", zap.String("botUserID", botUser.ID.Hex()))

	// Run the bot
	go func() {
		traceCtx, span := middleware.WithSpan(utils.DeriveContextWithoutCancel(ctx), "startGameWithBot.runBot")
		defer span.End()
		zlog.Debug(traceCtx, "Starting bot goroutine", zap.String("botUserID", botUser.ID.Hex()))
		err := botutils.RunBot(traceCtx, game, botUser, s, s.cache)
		if err != nil {
			zlog.Error(traceCtx, "Failed to run bot", err, zap.String("botUserID", botUser.ID.Hex()))
		}
		zlog.Debug(traceCtx, "Bot goroutine completed", zap.String("botUserID", botUser.ID.Hex()))
	}()

	zlog.Debug(ctx, "Exiting startGameWithBot", zap.String("gameID", game.ID.Hex()), zap.String("botUserID", botUser.ID.Hex()))
	return game, nil
}

func (s *service) validateGameConfig(config *models.GameConfigInput) error {
	if config == nil {
		return fmt.Errorf("game configuration is required")
	}
	var timeLimit *int
	if config.CategorySpecificConfig != nil {
		if config.CategorySpecificConfig.Blitz != nil {
			timeLimit = config.CategorySpecificConfig.Blitz.TimeLimit
		} else if config.CategorySpecificConfig.Classical != nil {
			timeLimit = config.CategorySpecificConfig.Classical.TimeLimit
		} else if config.CategorySpecificConfig.Memory != nil {
			timeLimit = config.CategorySpecificConfig.Memory.TimeLimit
		} else if config.CategorySpecificConfig.Puzzle != nil {
			timeLimit = config.CategorySpecificConfig.Puzzle.TimeLimit
		}
	} else {
		timeLimit = utils.AllocPtr(config.TimeLimit)
	}

	if timeLimit != nil && *timeLimit < 30 {
		return fmt.Errorf("time limit must be at least 30 seconds")
	}

	if timeLimit != nil && *timeLimit > 900 {
		return fmt.Errorf("time limit must be less than 900 seconds")
	}

	var numPlayers *int
	if config.ModeSpecificConfig != nil {
		if config.ModeSpecificConfig.OnlineSearch != nil {
			numPlayers = config.ModeSpecificConfig.OnlineSearch.NumPlayers
		} else if config.ModeSpecificConfig.OnlineChallenge != nil {
			numPlayers = config.ModeSpecificConfig.OnlineChallenge.NumPlayers
		} else if config.ModeSpecificConfig.Practice != nil {
			numPlayers = config.ModeSpecificConfig.Practice.NumPlayers
		} else if config.ModeSpecificConfig.PlayViaLink != nil {
			numPlayers = config.ModeSpecificConfig.PlayViaLink.NumPlayers
		} else if config.ModeSpecificConfig.RushWithTime != nil {
			numPlayers = config.ModeSpecificConfig.RushWithTime.NumPlayers
		} else if config.ModeSpecificConfig.RushWithoutTime != nil {
			numPlayers = config.ModeSpecificConfig.RushWithoutTime.NumPlayers
		} else if config.ModeSpecificConfig.SurvivalSaturday != nil {
			numPlayers = config.ModeSpecificConfig.SurvivalSaturday.NumPlayers
		} else if config.ModeSpecificConfig.SumdayShowdown != nil {
			numPlayers = utils.AllocPtr(2)
		}
	} else {
		numPlayers = utils.AllocPtr(config.NumPlayers)
	}

	if numPlayers != nil && *numPlayers < 1 {
		return fmt.Errorf("number of players must be at least 2")
	}

	if numPlayers != nil && *numPlayers > 50 {
		return fmt.Errorf("number of players must be less than 4")
	}

	return nil
}

func (s *service) getPlayersFromUsers(users []*models.User, gameType models.GameType) []*models.Player {
	Players := make([]*models.Player, len(users))
	for i, user := range users {
		rating := utils.AllocPtr(gameutils.GetPlayerRatingByGameType(user, gameType))
		Players[i] = &models.Player{
			UserID:      user.ID,
			Status:      models.PlayerStatusAccepted,
			Rating:      rating,
			StatikCoins: user.StatikCoins,
		}
	}

	return Players
}

func (s *service) createGameWithPlayers(ctx context.Context, players []*models.Player, config *models.GameConfig) (*models.Game, error) {
	zlog.Debug(ctx, "Creating game with players", zap.Int("numPlayers", len(players)), zap.Any("players", players))

	gameCategory, gameType, gameMode := gameutils.GetGameCategoryTypeAndMode(config)

	game := &models.Game{
		ID:           utils.AllocPtr(primitive.NewObjectID()),
		Config:       config,
		GameCategory: gameCategory,
		GameMode:     gameMode,
		GameType:     gameType,
		CreatedBy:    players[0].UserID,
		GameStatus:   models.GameStatusStarted,
		Players:      players,
		LeaderBoard:  make([]*models.LeaderBoardEntry, len(players)),
		Questions:    []*models.GameQuestion{},
		StartTime:    utils.AllocPtr(time.Now().Add(6 * time.Second)),
	}

	if len(players) > 0 {
		generatedQuestions, err := gameutils.GetQuestionsForGame(ctx, game)
		if err != nil {
			return nil, fmt.Errorf("failed to generate questions: %w", err)
		}
		game.Questions = generatedQuestions

		err = s.addEncryptedQuestionsInGame(game)
		if err != nil {
			return nil, err
		}

		game.LeaderBoard = make([]*models.LeaderBoardEntry, len(players))
		for i, player := range players {
			game.LeaderBoard[i] = &models.LeaderBoardEntry{
				UserID:      &player.UserID,
				Correct:     utils.AllocPtr(0),
				Incorrect:   utils.AllocPtr(0),
				TotalPoints: utils.AllocPtr(0.0),
			}
		}
	}
	createdGame, err := s.gameRepo.CreateGame(ctx, game)
	if err != nil {
		return nil, fmt.Errorf("failed to create game: %w", err)
	}

	err = s.gameCache.SetGame(ctx, createdGame)
	if err != nil {
		zlog.Error(ctx, "Failed to cache game", err)
	}

	return createdGame, nil
}

func (s *service) notifyMatchedPlayers(ctx context.Context, game *models.Game, players []*models.User) {
	for _, player := range players {
		opponent := players[0]
		if opponent.ID == player.ID {
			opponent = players[1]
		}

		zlog.Debug(ctx, "Notifying matched players", zap.String("user", player.ID.Hex()), zap.String("opponent", opponent.ID.Hex()))

		userEvent := &models.SearchSubscriptionOutput{
			Game:     game,
			Event:    utils.AllocPtr(constants.UserEventEnum.USER_MATCHED.String()),
			Opponent: opponent,
		}
		err := s.coreService.PublishUserEvent(ctx, player.ID, userEvent)
		if err != nil {
			zlog.Error(ctx, "Failed to publish USER_MATCHED event", err)
		}
	}
}
