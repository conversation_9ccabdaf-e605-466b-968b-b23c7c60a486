package game

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"
	"time"

	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"

	"github.com/redis/go-redis/v9"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/scheduler"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/domain/utils/encryption"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func mapGameModelToAnswerResponseModel(game *models.Game, answerModel *models.AnswerResponseModel) {
	answerModel.ID = game.ID
	answerModel.Config = game.Config
	answerModel.GameStatus = game.GameStatus
	answerModel.LeaderBoard = game.LeaderBoard
	answerModel.EndTime = game.EndTime
	answerModel.Players = game.Players

	if game.GameType == models.GameTypeGroupPlay {
		var userSubmissionWithQue []*models.UserSubmissionsWithQuestion
		for _, que := range game.Questions {
			var submissions []*models.UserSubmissions
			for _, submission := range que.Submissions {
				submissions = append(submissions, &models.UserSubmissions{
					UserID: submission.UserID,
					Points: submission.Points,
				})
			}
			userSubmissionWithQue = append(userSubmissionWithQue, &models.UserSubmissionsWithQuestion{
				QuestionId:  que.Question.ID,
				Submissions: submissions,
			})
		}
		answerModel.UserSubmissionsWithQuestion = userSubmissionWithQue
	}
}

func (s *service) UserRepo() repository.UserRepository {
	return s.userRepo
}

func (s *service) publishSubmitAnswerGameEvent(ctx context.Context, game *models.Game, eventType string) error {
	var answerResponseModel models.AnswerResponseModel
	mapGameModelToAnswerResponseModel(game, &answerResponseModel)
	channel := fmt.Sprintf("%s_%s_%s", constants.SubscriptionPrefixEnum.GAME_EVENT, game.ID.Hex(), constants.SocketV2)
	message := &models.SubmitAnswerEvent{
		Event: utils.AllocPtr(eventType),
		Game:  &answerResponseModel,
	}
	err := s.ws.Publish(ctx, channel, message)
	if err != nil {
		return err
	}

	channel = fmt.Sprintf("%s_%s", constants.SubscriptionPrefixEnum.GAME_EVENT, game.ID.Hex())
	message2 := &models.SubscriptionOutput{
		Event: utils.AllocPtr(eventType),
		Game:  game,
	}
	return s.ws.Publish(ctx, channel, message2)
}

func (s *service) publishGameEvent(ctx context.Context, game *models.Game, eventType string) error {
	channel := fmt.Sprintf("%s_%s_%s", constants.SubscriptionPrefixEnum.GAME_EVENT, game.ID.Hex(), constants.SocketV2)
	message := &models.SubscriptionOutput{
		Event: utils.AllocPtr(eventType),
		Game:  redactQuestions(game),
	}

	err := s.ws.Publish(ctx, channel, message)
	if err != nil {
		return err
	}

	channel = fmt.Sprintf("%s_%s", constants.SubscriptionPrefixEnum.GAME_EVENT, game.ID.Hex())
	return s.ws.Publish(ctx, channel, message)
}

func redactQuestions(game *models.Game) *models.Game {
	gameCopy := *game
	gameCopy.Questions = nil
	return &gameCopy
}

func (s *service) getShowdownParticipant(ctx context.Context, showdownId primitive.ObjectID, userId string) (*models.ShowdownParticipant, error) {
	participant, err := s.showdownCache.GetShowdownParticipant(ctx, showdownId, userId)
	if err == nil && participant != nil {
		return participant, nil
	}
	userIdHex, err := primitive.ObjectIDFromHex(userId)
	if err != nil {
		return nil, err
	}
	participant, err = s.showdownParticipantRepo.GetByID(ctx, userIdHex)
	if err != nil {
		return nil, err
	}
	return participant, nil
}

func (s *service) addEncryptedQuestionsInGame(game *models.Game) error {
	encryptedQuestions := make([]*string, 0, len(game.Questions))
	for _, question := range game.Questions {
		encQ, err := encryption.EncryptObject(question)
		if err != nil {
			return err
		}
		encryptedQuestions = append(encryptedQuestions, &encQ)
	}
	game.EncryptedQuestions = encryptedQuestions
	return nil
}

type platformStats struct {
	Num         int64     `json:"num"`
	LastUpdated time.Time `json:"lastUpdated"`
}

func (s *service) GetPlatformStats(ctx context.Context) (*models.PlatformStats, error) {
	var users, signedInUsers, games platformStats

	bytes, err := s.cache.Get(ctx, constants.TotalUsersKey)
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, err
	}
	err = json.Unmarshal(bytes, &users)
	if err != nil || users.Num == 0 || users.LastUpdated.IsZero() || time.Since(users.LastUpdated) > 30*time.Minute {
		numUsers, err := s.userRepo.EstimatedDocumentCount(ctx)
		if err != nil {
			return nil, err
		}
		users.Num = numUsers
		users.LastUpdated = time.Now()
		byteData, err := json.Marshal(users)
		if err != nil {
			return nil, err
		}
		err = s.cache.Set(ctx, constants.TotalUsersKey, byteData, 1*time.Hour)
		if err != nil {
			return nil, err
		}
	}

	bytes, err = s.cache.Get(ctx, constants.TotalSignedInUsersKey)
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, err
	}
	err = json.Unmarshal(bytes, &signedInUsers)
	if err != nil || signedInUsers.Num == 0 || signedInUsers.LastUpdated.IsZero() || time.Since(signedInUsers.LastUpdated) > 30*time.Minute {
		numSignedInUsers, err := s.userRepo.CountDocuments(ctx, bson.M{"isGuest": false})
		if err != nil {
			return nil, err
		}
		signedInUsers.Num = numSignedInUsers
		signedInUsers.LastUpdated = time.Now()
		byteData, err := json.Marshal(signedInUsers)
		if err != nil {
			return nil, err
		}
		err = s.cache.Set(ctx, constants.TotalSignedInUsersKey, byteData, 1*time.Hour)
		if err != nil {
			return nil, err
		}
	}

	bytes, err = s.cache.Get(ctx, constants.TotalGamesKey)
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, err
	}
	err = json.Unmarshal(bytes, &games)
	if err != nil || games.Num == 0 || games.LastUpdated.IsZero() || time.Since(games.LastUpdated) > 30*time.Minute {
		numGames, err := s.gameRepo.EstimatedDocumentCount(ctx)
		if err != nil {
			return nil, err
		}
		games.Num = numGames
		games.LastUpdated = time.Now()
		byteData, err := json.Marshal(games)
		if err != nil {
			return nil, err
		}
		err = s.cache.Set(ctx, constants.TotalGamesKey, byteData, 1*time.Hour)
		if err != nil {
			return nil, err
		}
	}

	return &models.PlatformStats{
		TotalUsers:         int(users.Num),
		TotalGames:         int(games.Num),
		TotalSignedInUsers: int(signedInUsers.Num),
	}, nil
}

func (s *service) scheduleEndGame(ctx context.Context, game *models.Game) {
	timeLimit := gameutils.GetTimeLimitFromGameConfig(game.Config)
	gameType := gameutils.GetGameTypeFromGameConfig(game.Config)

	if gameType == models.GameTypeFlashAnzan {
		return
	}

	if timeLimit != 0 {
		startTime := *game.StartTime
		gameEndTime := startTime.Add(time.Duration(timeLimit)*time.Second + 250*time.Millisecond)
		actionType := models.EndGame
		if game.GameType == models.GameTypeSumdayShowdown {
			actionType = models.EndGameForShowdown
		}
		err := scheduler.Schedule(ctx, s.sortedSet, &models.SchedulerPayload{
			Type: actionType,
			Action: models.EndGameActionPayload{
				GameID:     *game.ID,
				StartTime:  startTime,
				GameConfig: game.Config,
			},
			ContextMap: utils.GetContextValuesMap(ctx),
		}, gameEndTime)
		if err != nil {
			zlog.Error(ctx, "Failed to schedule end game", err)
			return
		}
	}
}

func (s *service) handleUsersGameLockedStatus(ctx context.Context, currentSessionUser *models.User, game *models.Game) error {
	gamesPlayedByUser := utils.GetNumberOfGamesPlayedByUser(currentSessionUser)

	hasUnlockedAllGames := false

	if currentSessionUser.Additional != nil && currentSessionUser.Additional.HasUnlockedAllGames {
		hasUnlockedAllGames = true
	}

	if hasUnlockedAllGames {
		return nil
	}

	if game != nil && game.GameType == models.GameTypePlayWithFriend {
		err := s.userRepo.UpdateHasUnlockedAllGames(ctx, currentSessionUser.ID, true)
		if err != nil {
			return err
		}
		return nil
	}

	if gamesPlayedByUser+1 >= constants.MinGamesToUnlockAllGames {
		err := s.userRepo.UpdateHasUnlockedAllGames(ctx, currentSessionUser.ID, true)
		if err != nil {
			return err
		}
	}

	return nil
}
