package gameutils

import "matiksOfficial/matiks-server-go/internal/models"

func GetUserRatingByGameCategory(gameCategory models.GameCategory, user *models.User) int {
	switch gameCategory {
	case models.GameCategoryClassical:
		return getUserDefaultGlobalRatingWithFallback(user)
	case models.GameCategoryBlitz:
		return getUserFlashAnzanRatingWithFallback(user)
	case models.GameCategoryPuzzle:
		return getUserDefaultPuzzleRatingWithFallback(user)
	default:
		return getUserDefaultGlobalRatingWithFallback(user)
	}
}
