package game

import (
	"context"
	"fmt"
	"math"
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"
	"matiksOfficial/matiks-server-go/internal/domain/game/utils/botutils"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/zap"
)

func (s *service) StartGame(ctx context.Context, startGameInput *models.StartGameInput) (*models.Game, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	zlog.Debug(ctx, "Starting game", zap.String("userID", userID.Hex()), zap.Any("startGameInput", startGameInput))

	gameId := startGameInput.GameID
	if gameId == nil {
		return nil, fmt.Errorf("gameID is nil")
	}

	var game *models.Game
	game, err = s.GetGameByID(ctx, gameId)
	if err != nil {
		return nil, fmt.Errorf("game not found")
	}

	if game.Config == nil {
		return nil, fmt.Errorf("game config is nil")
	}
	_, _, gameMode := gameutils.GetGameCategoryTypeAndMode(game.Config)

	if gameMode == models.GameModeSumdayShowdown {
		return s.StartGameForShowdown(ctx, userID, *gameId)
	}
	if game.GameStatus == models.GameStatusStarted {
		zlog.Debug(ctx, "Game is already started", zap.String("gameID", gameId.Hex()))
		return game, nil
	}

	currPlayer := findPlayerByID(game.Players, userID)
	if currPlayer == nil {
		zlog.Debug(ctx, "current player found nil")
		return nil, fmt.Errorf("you cannot start the game as you are not a player of this game")
	}

	if gameMode == models.GameModeGroupPlay {
		game.GameStatus = models.GameStatusReady
	}

	if game.GameStatus != models.GameStatusReady {
		zlog.Info(ctx, "game cannot be started, current status is", zap.Any("gameStatus", game.GameStatus))
		return game, fmt.Errorf("game cannot be started, current status is %s", game.GameStatus)
	}

	game.GameStatus = models.GameStatusStarted

	game.LeaderBoard = make([]*models.LeaderBoardEntry, len(game.Players))
	for i, player := range game.Players {
		leaderboardEntry := gameutils.GetDefaultUserLeaderBoardStand(player.UserID)
		game.LeaderBoard[i] = &leaderboardEntry
	}

	if game.GameType != models.GameTypeFlashAnzan {

		generatedQuestions, err := gameutils.GetQuestionsForGame(ctx, game)
		if err != nil {
			return nil, fmt.Errorf("failed to generate questions: %w", err)
		}

		timeLimit := gameutils.GetTimeLimitFromGameConfig(game.Config)
		maxTimePerQuestion := gameutils.GetMaxTimePerQuestionFromGameConfig(game.Config)
		maxQuestion := math.Ceil(float64(timeLimit) / float64(maxTimePerQuestion))
		generatedQuestions = generatedQuestions[0 : int(maxQuestion)+2]
		for _, que := range generatedQuestions {
			que.Question.MaxTimeLimit = &maxTimePerQuestion
		}

		game.Questions = generatedQuestions
		timeAfter5sec := time.Now().Add(5 * time.Second)
		game.StartTime = &timeAfter5sec

		err = s.addEncryptedQuestionsInGame(game)
		if err != nil {
			return nil, err
		}
	} else {
		game.StartTime = utils.AllocPtr(time.Now())
	}

	err = s.publishGameEvent(ctx, game, constants.GameEventEnum.GAME_STARTED.String())
	if err != nil {
		return nil, err
	}

	ctx = context.WithValue(ctx, constants.GameIDKey, game.ID.Hex())

	if game.GameType != models.GameTypeFlashAnzan {
		s.scheduleEndGame(ctx, game)
	}

	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		return nil, err
	}
	for _, player := range game.Players {
		user, err := s.userService.GetUserByID(ctx, player.UserID)
		if err != nil {
			return nil, err
		}
		if (user.IsBot != nil && *user.IsBot) || (user.IsHumanBot != nil && *user.IsHumanBot) {
			go func() {
				err := botutils.RunBot(utils.DeriveContextWithoutCancel(ctx), game, user, s, s.cache)
				if err != nil {
					zlog.Error(ctx, "Error running bot after Accept Challenge", err)
				}
			}()
		}
	}

	return game, nil
}
