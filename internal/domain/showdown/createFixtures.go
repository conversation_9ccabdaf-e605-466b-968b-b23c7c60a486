package showdown

import (
	"context"
	"fmt"
	"math/rand/v2"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) CreateFixtures(ctx context.Context, showdownId primitive.ObjectID, currentRound int) error {
	zlog.Info(ctx, "Creating Fixtures for", zap.String("showdownID", showdownId.Hex()), zap.Any("round", currentRound))

	showdown, err := s.getShowdownById(ctx, showdownId)
	if err != nil {
		zlog.Error(ctx, "Failed to get showdown", err, zap.String("showdownID", showdownId.Hex()))
		return err
	}
	if showdown == nil {
		zlog.Error(ctx, "Failed to create fixtures registration count", nil, zap.String("showdownID", showdownId.Hex()))
		return fmt.Errorf("showdown null")
	}

	if currentRound < 1 {
		currentRound = 1
	}

	if currentRound == 1 {
		err = s.UpdateShowdownStatus(ctx, showdownId, models.ShowdownContestStatusLive)
		if err != nil {
			zlog.Error(ctx, "Failed to update Showdown status", nil, zap.String("showdownID", showdownId.Hex()))
			return err
		}
	}

	fixtures, byeParticipantId, err := s.createFixturesWithParticipantsDetails(ctx, showdownId, currentRound, showdown)
	if err != nil {
		zlog.Error(ctx, "Failed to create fixtures", err, zap.String("showdownID", showdownId.Hex()))
		return err
	}

	if err = s.fixtureRepo.BulkInsert(ctx, fixtures); err != nil {
		zlog.Error(ctx, "Failed to insert fixtures", err, zap.String("showdownID", showdownId.Hex()))
		return err
	}

	if err = s.scheduleNextRoundAndNotifications(ctx, showdown, currentRound, showdownId, byeParticipantId); err != nil {
		zlog.Error(ctx, "Failed to schedule next round and notifications", err, zap.String("showdownID", showdownId.Hex()))
		return err
	}
	zlog.Info(ctx, "Finished creating fixtures for", zap.String("showdownID", showdownId.Hex()), zap.Any("round", currentRound))

	return nil
}

func (s *service) createFixturesWithParticipantsDetails(ctx context.Context,
	showdownID primitive.ObjectID,
	round int,
	showdown *models.Showdown,
) ([]*models.Fictures, *primitive.ObjectID, error) {
	pageSize := constants.SHOWDOWN_PARTICIPANT_BATCH_SIZE
	currentPage := 1
	var (
		byeParticipantId *primitive.ObjectID
		allFixtures      []*models.Fictures
		participantsLeft []*models.ShowdownParticipantDetail
	)
	totalParticipants, err := s.showdownParticipantRepo.CountByShowdown(ctx, showdownID)
	if err != nil {
		return nil, nil, err
	}

	hasBye := false
	if totalParticipants%2 == 1 {
		hasBye = true
	}
	zlog.Info(ctx, "total participants", zap.Any("total", totalParticipants))
	totalPages := totalParticipants / int64(pageSize)
	if totalParticipants%int64(pageSize) != 0 {
		totalPages += 1
	}
	for currentPage <= int(totalPages) {
		participants, err := s.getParticipantsForFixtures(ctx, showdownID, round, currentPage, pageSize)
		if err != nil {
			return nil, nil, err
		}
		if participants == nil {
			return nil, nil, fmt.Errorf("participants is nil")
		}
		participantsLen := len(participants)
		if participantsLen == 0 {
			zlog.Info(ctx, "no participants found")
			return allFixtures, byeParticipantId, nil
		}
		if currentPage == 1 && hasBye {
			byeParticipant := models.ShowdownParticipant{}
			if participantsLen == 1 {
				byeParticipant = participants[0].ShowdownParticipant
			} else if round == 1 {
				byeParticipant = participants[0].ShowdownParticipant
				participants = participants[1:]
			} else {
				foundByeUser := false
				for i, participant := range participants {
					if participant == nil {
						return nil, nil, fmt.Errorf("participant is nil")
					}
					if !participant.ShowdownParticipant.HadABye {
						participant.ShowdownParticipant.HadABye = true
						byeParticipant = participant.ShowdownParticipant
						if i == 0 && (i+1) < len(participants) {
							participants = participants[1:]
						} else if (i + 1) < len(participants) {
							participants = append(participants[:i], participants[i+1:]...)
						} else if (i + 1) == participantsLen {
							participants = participants[:i]
						}
						foundByeUser = true
						break
					}
				}
				if !foundByeUser {
					indexOfByeUser := rand.IntN(participantsLen - 1)
					byeParticipant = participants[indexOfByeUser].ShowdownParticipant
					if indexOfByeUser == 0 {
						participants = participants[1:]
					} else if (indexOfByeUser + 1) == participantsLen {
						participants = participants[:indexOfByeUser]
					} else {
						participants = append(participants[:indexOfByeUser], participants[indexOfByeUser+1:]...)
					}
				}
			}
			if byeParticipant.ID != nil {
				byeParticipantId = &byeParticipant.UserID
				byeParticipant.HadABye = true
				byeParticipant.TotalScore += 1
				byeParticipant.Rounds = append(byeParticipant.Rounds, &models.ShowdownRound{
					Score:        1,
					PlayerStatus: models.RoundPlayerStatusBye,
					Round:        round,
					HasJoined:    true,
				})
				_, err := s.showdownCache.SetShowdownParticipant(ctx, &byeParticipant)
				if err != nil {
					return nil, nil, err
				}
				if len(participants) == 1 {
					return []*models.Fictures{}, nil, nil
				}
			}
		}

		participants = append(participants, participantsLeft...)
		participantsLeft = []*models.ShowdownParticipantDetail{}
		fixtures, participantsLeftFromMatching, err := s.matchParticipants(ctx, showdownID, participants, round, showdown)
		if err != nil {
			return nil, nil, err
		}

		participantsLeft = append(participantsLeft, participantsLeftFromMatching...)
		if fixtures != nil {
			allFixtures = append(allFixtures, fixtures...)
		}
		currentPage += 1
	}
	if len(participantsLeft) > 1 {
		fixtures, err := s.matchRemainingParticipants(ctx, showdownID, participantsLeft, round, showdown)
		if err != nil {
			return nil, nil, err
		}
		allFixtures = append(allFixtures, fixtures...)
	}
	return allFixtures, byeParticipantId, nil
}

func (s *service) matchRemainingParticipants(ctx context.Context,
	showdownID primitive.ObjectID,
	participants []*models.ShowdownParticipantDetail,
	round int,
	showdown *models.Showdown,
) ([]*models.Fictures, error) {
	if showdown == nil {
		return nil, fmt.Errorf("showdown cannot be nil")
	}

	if len(participants) == 0 {
		return nil, fmt.Errorf("no participants provided")
	}

	var (
		fixtures []*models.Fictures
		games    []*models.Game
	)
	for i := 0; (i + 1) < len(participants); i += 2 {
		candidate1 := participants[i]
		candidate2 := participants[i+1]
		fixture, matchedGames, err := s.createFixtureAndGames(
			ctx,
			showdownID,
			candidate1,
			candidate2,
			round,
			showdown,
		)
		if err != nil {
			zlog.Error(ctx, "Failed to create fixture and games", err)
			continue
		}

		fixtures = append(fixtures, fixture)
		games = append(games, matchedGames...)

		updateRecentOpponents(candidate1, candidate2)
	}
	if len(games) > 0 {
		if err := s.batchInsertGames(ctx, games, showdownID, round); err != nil {
			zlog.Error(ctx, "failed to batch create games and update rounds", err)
			return nil, err
		}
	}

	return fixtures, nil
}

func (s *service) matchParticipants(
	ctx context.Context,
	showdownID primitive.ObjectID,
	participants []*models.ShowdownParticipantDetail,
	round int,
	showdown *models.Showdown,
) ([]*models.Fictures, []*models.ShowdownParticipantDetail, error) {
	if showdown == nil {
		return nil, nil, fmt.Errorf("showdown cannot be nil")
	}

	if len(participants) == 0 {
		return nil, nil, fmt.Errorf("no participants provided")
	}

	fixtures, games, unmatchedParticipants, err := s.findMatches(ctx, participants, showdownID, round, showdown)
	if err != nil {
		return nil, nil, err
	}

	if len(games) > 0 {
		if err := s.batchInsertGames(ctx, games, showdownID, round); err != nil {
			zlog.Error(ctx, "failed to batch create games and update rounds", err)
			return nil, nil, err
		}
	}

	return fixtures, unmatchedParticipants, nil
}

func (s *service) findMatches(ctx context.Context, remainingParticipants []*models.ShowdownParticipantDetail, showdownID primitive.ObjectID, round int, showdown *models.Showdown) ([]*models.Fictures, []*models.Game, []*models.ShowdownParticipantDetail, error) {
	var fixtures []*models.Fictures
	var games []*models.Game
	var unmatchedParticipants []*models.ShowdownParticipantDetail

	matched := make(map[primitive.ObjectID]bool)

	for i := 0; i < len(remainingParticipants); i++ {
		if remainingParticipants[i] == nil {
			zlog.Error(ctx, "participant found nil while matching", nil, zap.String("showdownID", showdownID.Hex()))
		}
		if matched[remainingParticipants[i].ShowdownParticipant.UserID] {
			continue
		}

		candidate1 := remainingParticipants[i]
		bestMatch := -1

		for j := i + 1; j < len(remainingParticipants); j++ {
			if matched[remainingParticipants[j].ShowdownParticipant.UserID] {
				continue
			}

			candidate2 := remainingParticipants[j]
			if !hasRecentOpponent(candidate1, candidate2) {
				bestMatch = j
				break
			}
		}

		if bestMatch != -1 {
			candidate2 := remainingParticipants[bestMatch]
			fixture, matchedGames, err := s.createFixtureAndGames(
				ctx,
				showdownID,
				candidate1,
				candidate2,
				round,
				showdown,
			)
			if err != nil {
				return nil, nil, nil, fmt.Errorf("failed to create fixture and games: %w", err)
			}

			fixtures = append(fixtures, fixture)
			games = append(games, matchedGames...)

			updateRecentOpponents(candidate1, candidate2)
			_, err = s.showdownCache.SetShowdownParticipant(ctx, &candidate1.ShowdownParticipant)
			if err != nil {
				return nil, nil, nil, fmt.Errorf("failed to update showdown participant: %w", err)
			}
			_, err = s.showdownCache.SetShowdownParticipant(ctx, &candidate2.ShowdownParticipant)
			if err != nil {
				return nil, nil, nil, fmt.Errorf("failed to update showdown participant: %w", err)
			}
			matched[candidate1.ShowdownParticipant.UserID] = true
			matched[candidate2.ShowdownParticipant.UserID] = true
		}
	}

	for _, p := range remainingParticipants {
		if !matched[p.ShowdownParticipant.UserID] {
			unmatchedParticipants = append(unmatchedParticipants, p)
		}
	}

	return fixtures, games, unmatchedParticipants, nil
}

func hasRecentOpponent(p1, p2 *models.ShowdownParticipantDetail) bool {
	for _, opponent := range p1.ShowdownParticipant.RecentOpponents {
		if opponent == p2.ShowdownParticipant.UserID {
			return true
		}
	}
	return false
}

func updateRecentOpponents(p1, p2 *models.ShowdownParticipantDetail) {
	p1.ShowdownParticipant.RecentOpponents = append(
		p1.ShowdownParticipant.RecentOpponents,
		p2.ShowdownParticipant.UserID,
	)
	p2.ShowdownParticipant.RecentOpponents = append(
		p2.ShowdownParticipant.RecentOpponents,
		p1.ShowdownParticipant.UserID,
	)
}

func (s *service) createFixtureAndGames(
	ctx context.Context,
	showdownID primitive.ObjectID,
	p1, p2 *models.ShowdownParticipantDetail,
	round int,
	showdown *models.Showdown,
) (*models.Fictures, []*models.Game, error) {
	fixture := &models.Fictures{
		ShowdownID:   showdownID,
		Users:        []*models.ShowdownParticipantDetail{p1, p2},
		Round:        round,
		Participants: []primitive.ObjectID{p1.ShowdownParticipant.UserID, p2.ShowdownParticipant.UserID},
	}

	players := models.Players{
		&p1.ShowdownParticipant.UserID,
		&p2.ShowdownParticipant.UserID,
	}

	playerGames, err := s.gameService.CreateGameForShowdown(ctx, &models.ShowdownConfig{
		ShowdownId: showdownID,
		TotalGames: showdown.RoundConfig.NumOfGames,
		GameConfig: models.GameConfig{
			NumPlayers: utils.AllocPtr(constants.SHOWDOWN_PLAYERS),
			TimeLimit:  utils.AllocPtr(showdown.RoundConfig.GameDuration),
			GameType:   showdown.RoundConfig.GameType,
		},
		Players: players,
		Round:   round,
	})
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create games for showdown: %w", err)
	}

	return fixture, playerGames, nil
}

func (s *service) batchInsertGames(ctx context.Context, games []*models.Game, showdownId primitive.ObjectID, round int) error {
	gamesAndPlayers := map[primitive.ObjectID]*models.ShowdownRoundDto{}

	err := s.gameRepo.CreateManyGames(ctx, games)
	if err != nil {
		zlog.Error(ctx, "Failed to get insert batch of users for create game", err)
		return err
	}
	for _, game := range games {
		if game == nil || game.ID == nil || len(game.Players) != 2 {
			zlog.Error(ctx, "game is invalid", nil, zap.String("showdownID", showdownId.Hex()))
			continue
		}
		if game.Players[0] == nil || game.Players[1] == nil {
			zlog.Error(ctx, "game is invalid", nil, zap.String("showdownID", showdownId.Hex()))
			continue
		}
		user1 := game.Players[0]
		user2 := game.Players[1]
		user1RoundData, ok := gamesAndPlayers[user1.UserID]
		if ok && user1RoundData != nil {
			user1RoundData.Round.Games = append(user1RoundData.Round.Games, *game.ID)
		} else {
			gamesAndPlayers[user1.UserID] = &models.ShowdownRoundDto{
				UserId: user1.UserID,
				Round: models.ShowdownRound{
					Opponent:     user2.UserID,
					PlayerStatus: models.RoundPlayerStatusPendingJoin,
					Round:        round,
					Score:        0,
					Games:        []models.ObjectID{*game.ID},
				},
			}
		}
		user2RoundData, ok := gamesAndPlayers[user1.UserID]
		if ok && user1RoundData != nil {
			user2RoundData.Round.Games = append(user2RoundData.Round.Games, *game.ID)
		} else {
			gamesAndPlayers[user2.UserID] = &models.ShowdownRoundDto{
				UserId: user2.UserID,
				Round: models.ShowdownRound{
					Opponent:     user1.UserID,
					PlayerStatus: models.RoundPlayerStatusPendingJoin,
					Round:        round,
					Score:        0,
					Games:        []models.ObjectID{*game.ID},
				},
			}
		}
	}
	err = s.updateShowdownRoundBatch(ctx, gamesAndPlayers, showdownId)
	if err != nil {
		return err
	}
	return nil
}

func (s *service) getParticipantsForFixtures(ctx context.Context, showdownID primitive.ObjectID, round, currentPage, pageSize int) ([]*models.ShowdownParticipantDetail, error) {
	if round == 1 {
		participantsInfo, err := s.showdownParticipantRepo.GetShowdownParticipantsPaginated(
			ctx,
			showdownID,
			currentPage,
			pageSize,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to get paginated participants: %v", err)
		}
		shuffleParticipants(participantsInfo)

		var participants []*models.ShowdownParticipantDetail
		participants, err = s.convertToShowdownParticipantDetail(participantsInfo)
		if err != nil {
			return nil, fmt.Errorf("failed to convert users: %v", err)
		}
		return participants, nil
	} else {
		participants, err := s.getParticipantsFromLeaderboard(
			ctx,
			showdownID,
			currentPage,
			pageSize,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to get paginated participants: %v", err)
		}
		return participants, nil
	}
}
