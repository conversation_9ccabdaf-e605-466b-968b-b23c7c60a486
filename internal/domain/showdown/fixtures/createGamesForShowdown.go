package showdownFixtures

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

func (s *ShowdownFixtureService) CreateGamesForShowdown(ctx context.Context, fixtures []*models.Fictures) error {
	if len(fixtures) == 0 {
		return nil
	}
	var games []*models.Game
	for _, fixture := range fixtures {
		if fixture == nil {
			return fmt.Errorf("fixture found nil while getting games")
		}
		if len(fixture.Participants) != 2 {
			continue
		}
		players := models.Players{
			&fixture.Participants[0],
			&fixture.Participants[1],
		}

		newGame, err := s.gameService.CreateGameForShowdown(ctx, &models.ShowdownConfig{
			ShowdownId: s.showdownID,
			TotalGames: s.showdown.RoundConfig.NumOfGames,
			GameConfig: models.GameConfig{
				NumPlayers: utils.AllocPtr(constants.SHOWDOWN_PLAYERS),
				TimeLimit:  utils.AllocPtr(s.showdown.RoundConfig.GameDuration),
				GameType:   s.showdown.RoundConfig.GameType,
			},
			Players: players,
			Round:   fixture.Round,
		})
		if err != nil {
			return err
		}
		games = append(games, newGame...)
		if len(games) >= constants.SHOWDOWN_PARTICIPANT_BATCH_SIZE {
			err := s.batchInsertGames(ctx, games)
			if err != nil {
				return err
			}
			games = []*models.Game{}
		}
	}

	if len(games) > 0 {
		err := s.batchInsertGames(ctx, games)
		if err != nil {
			return err
		}
		games = []*models.Game{}
	}

	return nil
}
