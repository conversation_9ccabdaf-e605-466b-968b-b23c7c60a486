// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package generated

import (
	"bytes"
	"context"
	"errors"
	"matiksOfficial/matiks-server-go/internal/models"
	"sync/atomic"
	"time"

	"github.com/99designs/gqlgen/graphql"
	"github.com/99designs/gqlgen/graphql/introspection"
	gqlparser "github.com/vektah/gqlparser/v2"
	"github.com/vektah/gqlparser/v2/ast"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewExecutableSchema creates an ExecutableSchema from the ResolverRoot interface.
func NewExecutableSchema(cfg Config) graphql.ExecutableSchema {
	return &executableSchema{
		schema:     cfg.Schema,
		resolvers:  cfg.Resolvers,
		directives: cfg.Directives,
		complexity: cfg.Complexity,
	}
}

type Config struct {
	Schema     *ast.Schema
	Resolvers  ResolverRoot
	Directives DirectiveRoot
	Complexity ComplexityRoot
}

type ResolverRoot interface {
	Mutation() MutationResolver
	Query() QueryResolver
	Subscription() SubscriptionResolver
}

type DirectiveRoot struct {
	Auth func(ctx context.Context, obj any, next graphql.Resolver) (res any, err error)
}

type ComplexityRoot struct {
	ActivitySummaryEntry struct {
		Activity func(childComplexity int) int
		Coins    func(childComplexity int) int
	}

	AllPlayedPresetsOutput struct {
		Count   func(childComplexity int) int
		Presets func(childComplexity int) int
	}

	Announcement struct {
		CTAs             func(childComplexity int) int
		CreatedAt        func(childComplexity int) int
		Description      func(childComplexity int) int
		ExpiresAt        func(childComplexity int) int
		ID               func(childComplexity int) int
		ImageURL         func(childComplexity int) int
		MediaURL         func(childComplexity int) int
		Priority         func(childComplexity int) int
		PublishedAt      func(childComplexity int) int
		RiveAnimationURL func(childComplexity int) int
		Title            func(childComplexity int) int
		Type             func(childComplexity int) int
	}

	AnnouncementMutationResponse struct {
		Message func(childComplexity int) int
		Success func(childComplexity int) int
	}

	Attachment struct {
		Type func(childComplexity int) int
		URL  func(childComplexity int) int
	}

	AwardsAndAchievements struct {
		Description func(childComplexity int) int
		ImageURL    func(childComplexity int) int
		Link        func(childComplexity int) int
		Title       func(childComplexity int) int
	}

	BadgeAssignedEvent struct {
		InitialBadge func(childComplexity int) int
		NewBadge     func(childComplexity int) int
	}

	BotDetectionResult struct {
		ChallengeID   func(childComplexity int) int
		IsBotBehavior func(childComplexity int) int
		UserID        func(childComplexity int) int
	}

	CallToAction struct {
		ActionType func(childComplexity int) int
		Style      func(childComplexity int) int
		Target     func(childComplexity int) int
		Text       func(childComplexity int) int
	}

	Cell struct {
		IsVisible func(childComplexity int) int
		Type      func(childComplexity int) int
		Value     func(childComplexity int) int
	}

	ChallengeOutput struct {
		ChallengedBy func(childComplexity int) int
		CreatedAt    func(childComplexity int) int
		GameConfig   func(childComplexity int) int
		GameID       func(childComplexity int) int
		Opponent     func(childComplexity int) int
		Status       func(childComplexity int) int
	}

	ChallengeResult struct {
		Rank  func(childComplexity int) int
		Score func(childComplexity int) int
		User  func(childComplexity int) int
	}

	Club struct {
		BannerImage        func(childComplexity int) int
		Category           func(childComplexity int) int
		ChatRoomID         func(childComplexity int) int
		ClubEventsCount    func(childComplexity int) int
		CreatedAt          func(childComplexity int) int
		CreatedBy          func(childComplexity int) int
		Description        func(childComplexity int) int
		ForumID            func(childComplexity int) int
		HasRequestedToJoin func(childComplexity int) int
		ID                 func(childComplexity int) int
		IsAdmin            func(childComplexity int) int
		IsClubMember       func(childComplexity int) int
		LogoImage          func(childComplexity int) int
		MembersCount       func(childComplexity int) int
		Name               func(childComplexity int) int
		UpdatedAt          func(childComplexity int) int
		Visibility         func(childComplexity int) int
	}

	ClubAnnouncement struct {
		ClubID      func(childComplexity int) int
		Content     func(childComplexity int) int
		CreatedAt   func(childComplexity int) int
		CreatedBy   func(childComplexity int) int
		CreatorInfo func(childComplexity int) int
		ID          func(childComplexity int) int
		Title       func(childComplexity int) int
	}

	ClubAnnouncementCreatorInfo struct {
		ProfileImageURL func(childComplexity int) int
		Username        func(childComplexity int) int
	}

	ClubAnnouncementsPage struct {
		HasMore      func(childComplexity int) int
		PageNumber   func(childComplexity int) int
		PageSize     func(childComplexity int) int
		Results      func(childComplexity int) int
		TotalResults func(childComplexity int) int
	}

	ClubEvent struct {
		ClubEventPlayID    func(childComplexity int) int
		ClubEventType      func(childComplexity int) int
		ClubID             func(childComplexity int) int
		CreatedAt          func(childComplexity int) int
		CreatedBy          func(childComplexity int) int
		Description        func(childComplexity int) int
		GameConfig         func(childComplexity int) int
		ID                 func(childComplexity int) int
		OpenToAll          func(childComplexity int) int
		ParticipationCount func(childComplexity int) int
		PlayerSetting      func(childComplexity int) int
		RatedEvent         func(childComplexity int) int
		StartTime          func(childComplexity int) int
		Title              func(childComplexity int) int
		UpdatedAt          func(childComplexity int) int
		Visibility         func(childComplexity int) int
	}

	ClubEventParticipant struct {
		ClubEventID func(childComplexity int) int
		ID          func(childComplexity int) int
		JoinedAt    func(childComplexity int) int
		UserID      func(childComplexity int) int
	}

	ClubEventsPage struct {
		HasMore      func(childComplexity int) int
		PageNumber   func(childComplexity int) int
		PageSize     func(childComplexity int) int
		Results      func(childComplexity int) int
		TotalResults func(childComplexity int) int
	}

	ClubLeaderboard struct {
		HasMore      func(childComplexity int) int
		PageNumber   func(childComplexity int) int
		PageSize     func(childComplexity int) int
		Results      func(childComplexity int) int
		TotalResults func(childComplexity int) int
	}

	ClubLeaderboardEntry struct {
		Rank func(childComplexity int) int
		User func(childComplexity int) int
	}

	ClubMember struct {
		ClubID               func(childComplexity int) int
		ClubMembershipStatus func(childComplexity int) int
		ID                   func(childComplexity int) int
		JoinedAt             func(childComplexity int) int
		MemberInfo           func(childComplexity int) int
		Role                 func(childComplexity int) int
		UserID               func(childComplexity int) int
	}

	ClubMembersPage struct {
		HasMore      func(childComplexity int) int
		PageNumber   func(childComplexity int) int
		PageSize     func(childComplexity int) int
		Results      func(childComplexity int) int
		TotalResults func(childComplexity int) int
	}

	ClubsPage struct {
		HasMore      func(childComplexity int) int
		PageNumber   func(childComplexity int) int
		PageSize     func(childComplexity int) int
		Results      func(childComplexity int) int
		TotalResults func(childComplexity int) int
	}

	ConnectionRequest struct {
		SentBy func(childComplexity int) int
	}

	Contest struct {
		ClubID                   func(childComplexity int) int
		ContestDuration          func(childComplexity int) int
		CurrentUserParticipation func(childComplexity int) int
		Description              func(childComplexity int) int
		Details                  func(childComplexity int) int
		EncryptedQuestions       func(childComplexity int) int
		EndTime                  func(childComplexity int) int
		HostedBy                 func(childComplexity int) int
		HostedByV2               func(childComplexity int) int
		ID                       func(childComplexity int) int
		Name                     func(childComplexity int) int
		RecentParticipants       func(childComplexity int) int
		RegistrationCount        func(childComplexity int) int
		RegistrationEndTime      func(childComplexity int) int
		RegistrationForm         func(childComplexity int) int
		RegistrationStartTime    func(childComplexity int) int
		StartTime                func(childComplexity int) int
		Status                   func(childComplexity int) int
	}

	ContestDetails struct {
		About        func(childComplexity int) int
		Instructions func(childComplexity int) int
		Requirements func(childComplexity int) int
	}

	ContestLeaderboard struct {
		Participants      func(childComplexity int) int
		TotalParticipants func(childComplexity int) int
	}

	ContestParticipant struct {
		CorrectSubmission    func(childComplexity int) int
		IncorrectSubmission  func(childComplexity int) int
		IsVirtualParticipant func(childComplexity int) int
		LastSubmissionTime   func(childComplexity int) int
		Rank                 func(childComplexity int) int
		RegistrationData     func(childComplexity int) int
		Score                func(childComplexity int) int
		StartTime            func(childComplexity int) int
		User                 func(childComplexity int) int
	}

	ContestQuestion struct {
		ID       func(childComplexity int) int
		Points   func(childComplexity int) int
		Question func(childComplexity int) int
	}

	ContestSubmission struct {
		Answer         func(childComplexity int) int
		IsCorrect      func(childComplexity int) int
		Points         func(childComplexity int) int
		QuestionID     func(childComplexity int) int
		SubmissionTime func(childComplexity int) int
	}

	CreateMessageInput struct {
		Attachment func(childComplexity int) int
		Content    func(childComplexity int) int
		GroupID    func(childComplexity int) int
		Sender     func(childComplexity int) int
	}

	CreatorInfo struct {
		ProfileImageURL func(childComplexity int) int
		Rating          func(childComplexity int) int
		Username        func(childComplexity int) int
	}

	CrossMathPuzzle struct {
		PuzzleString func(childComplexity int) int
	}

	CrossMathPuzzleRush struct {
		BestAllTime    func(childComplexity int) int
		CreatedAt      func(childComplexity int) int
		ID             func(childComplexity int) int
		IsNewBestScore func(childComplexity int) int
		UpdatedAt      func(childComplexity int) int
		UserID         func(childComplexity int) int
	}

	CrossMathPuzzleRushPlayerInfo struct {
		Rank     func(childComplexity int) int
		Score    func(childComplexity int) int
		UserInfo func(childComplexity int) int
	}

	CrossMathPuzzleRushStats struct {
		BestAllTime func(childComplexity int) int
		FriendsRank func(childComplexity int) int
		GlobalRank  func(childComplexity int) int
	}

	CurrentShowdonParticipant struct {
		CurrentGame        func(childComplexity int) int
		CurrentRound       func(childComplexity int) int
		HadABye            func(childComplexity int) int
		LastSubmissionTime func(childComplexity int) int
		Rank               func(childComplexity int) int
		RecentOpponent     func(childComplexity int) int
		RegistrationData   func(childComplexity int) int
		Rounds             func(childComplexity int) int
		ShowdownID         func(childComplexity int) int
		Stats              func(childComplexity int) int
		TotalScore         func(childComplexity int) int
		UserID             func(childComplexity int) int
	}

	CurrentUserParticipation struct {
		ContestID          func(childComplexity int) int
		LastSubmissionTime func(childComplexity int) int
		RegistrationData   func(childComplexity int) int
		Score              func(childComplexity int) int
		UserID             func(childComplexity int) int
	}

	DailyChallenge struct {
		ChallengeNumber    func(childComplexity int) int
		ChallengeStatus    func(childComplexity int) int
		Division           func(childComplexity int) int
		EncryptedQuestions func(childComplexity int) int
		EndTime            func(childComplexity int) int
		HasAttempted       func(childComplexity int) int
		ID                 func(childComplexity int) int
		Questions          func(childComplexity int) int
		StartTime          func(childComplexity int) int
		Stats              func(childComplexity int) int
	}

	DailyChallengeResult struct {
		ChallengeID       func(childComplexity int) int
		CompletedAt       func(childComplexity int) int
		Rank              func(childComplexity int) int
		ResultStatus      func(childComplexity int) int
		Score             func(childComplexity int) int
		StatikCoinsEarned func(childComplexity int) int
		UserID            func(childComplexity int) int
	}

	DailyChallengeResultWithStats struct {
		Result func(childComplexity int) int
		Stats  func(childComplexity int) int
	}

	DailyChallengeStat struct {
		AverageAccuracy func(childComplexity int) int
		AverageTime     func(childComplexity int) int
		BestTime        func(childComplexity int) int
		TotalAttempts   func(childComplexity int) int
		TotalSubmission func(childComplexity int) int
	}

	DefaultGameConfig struct {
		TimeLimit func(childComplexity int) int
	}

	DefaultGameModeConfig struct {
		NumPlayers func(childComplexity int) int
	}

	DeviceTokenRegistrationResponse struct {
		Message func(childComplexity int) int
		Success func(childComplexity int) int
	}

	FeedAdditionalInfo struct {
		ConnectionRequest func(childComplexity int) int
	}

	FeedData struct {
		AdditionalInfo      func(childComplexity int) int
		CreatedAt           func(childComplexity int) int
		Description         func(childComplexity int) int
		ExpirationTime      func(childComplexity int) int
		FeedForFriends      func(childComplexity int) int
		ID                  func(childComplexity int) int
		LastLikedByUserName func(childComplexity int) int
		LikesCount          func(childComplexity int) int
		SentAt              func(childComplexity int) int
		SentFor             func(childComplexity int) int
		Title               func(childComplexity int) int
		UpdatedAt           func(childComplexity int) int
	}

	FeedForFriends struct {
		Body  func(childComplexity int) int
		Title func(childComplexity int) int
	}

	FeedResponse struct {
		Feeds       func(childComplexity int) int
		HasMore     func(childComplexity int) int
		IsRead      func(childComplexity int) int
		LastID      func(childComplexity int) int
		UserDetails func(childComplexity int) int
	}

	Fictures struct {
		ID           func(childComplexity int) int
		Participants func(childComplexity int) int
		Round        func(childComplexity int) int
		ShowdownID   func(childComplexity int) int
		Users        func(childComplexity int) int
	}

	FicturesCollection struct {
		CurrentUserFicture func(childComplexity int) int
	}

	FieldValidation struct {
		EmailSuffix            func(childComplexity int) int
		EmailSuffixes          func(childComplexity int) int
		Max                    func(childComplexity int) int
		Min                    func(childComplexity int) int
		NeedManualVerification func(childComplexity int) int
		Regex                  func(childComplexity int) int
	}

	File struct {
		Content     func(childComplexity int) int
		ContentType func(childComplexity int) int
		Name        func(childComplexity int) int
		URL         func(childComplexity int) int
	}

	FollowersAndFollowee struct {
		FollowedAt func(childComplexity int) int
		FolloweeID func(childComplexity int) int
		FollowerID func(childComplexity int) int
		ID         func(childComplexity int) int
	}

	FollowersAndFolloweeOutput struct {
		FollowedAt func(childComplexity int) int
		FolloweeID func(childComplexity int) int
		FollowerID func(childComplexity int) int
		ID         func(childComplexity int) int
		UserInfo   func(childComplexity int) int
	}

	FollowersAndFolloweePage struct {
		HasMore      func(childComplexity int) int
		PageNumber   func(childComplexity int) int
		PageSize     func(childComplexity int) int
		Results      func(childComplexity int) int
		TotalResults func(childComplexity int) int
	}

	FormField struct {
		ID         func(childComplexity int) int
		Label      func(childComplexity int) int
		Name       func(childComplexity int) int
		Options    func(childComplexity int) int
		Required   func(childComplexity int) int
		Type       func(childComplexity int) int
		Validation func(childComplexity int) int
	}

	Forum struct {
		ClubID      func(childComplexity int) int
		CreatedAt   func(childComplexity int) int
		CreatedBy   func(childComplexity int) int
		CreatorInfo func(childComplexity int) int
		Description func(childComplexity int) int
		ID          func(childComplexity int) int
		Title       func(childComplexity int) int
		UpdatedAt   func(childComplexity int) int
	}

	ForumPage struct {
		HasMore      func(childComplexity int) int
		PageNumber   func(childComplexity int) int
		PageSize     func(childComplexity int) int
		Results      func(childComplexity int) int
		TotalResults func(childComplexity int) int
	}

	ForumReply struct {
		Content     func(childComplexity int) int
		CreatedAt   func(childComplexity int) int
		CreatedBy   func(childComplexity int) int
		CreatorInfo func(childComplexity int) int
		ID          func(childComplexity int) int
		ThreadID    func(childComplexity int) int
	}

	ForumThread struct {
		Content     func(childComplexity int) int
		CreatedAt   func(childComplexity int) int
		CreatedBy   func(childComplexity int) int
		CreatorInfo func(childComplexity int) int
		ForumID     func(childComplexity int) int
		ID          func(childComplexity int) int
		Title       func(childComplexity int) int
	}

	FriendRequest struct {
		ID          func(childComplexity int) int
		ReceiverID  func(childComplexity int) int
		RespondedAt func(childComplexity int) int
		SenderID    func(childComplexity int) int
		SentAt      func(childComplexity int) int
		Status      func(childComplexity int) int
	}

	FriendRequestOutput struct {
		ID          func(childComplexity int) int
		ReceiverID  func(childComplexity int) int
		RespondedAt func(childComplexity int) int
		Sender      func(childComplexity int) int
		SenderID    func(childComplexity int) int
		SentAt      func(childComplexity int) int
		Status      func(childComplexity int) int
	}

	FriendRequestPage struct {
		HasMore      func(childComplexity int) int
		PageNumber   func(childComplexity int) int
		PageSize     func(childComplexity int) int
		Results      func(childComplexity int) int
		TotalResults func(childComplexity int) int
	}

	Friends struct {
		AcceptedAt func(childComplexity int) int
		ID         func(childComplexity int) int
		ReceiverID func(childComplexity int) int
		SenderID   func(childComplexity int) int
	}

	FriendsOutput struct {
		AcceptedAt   func(childComplexity int) int
		CurrActivity func(childComplexity int) int
		FriendInfo   func(childComplexity int) int
		ID           func(childComplexity int) int
		IsOnline     func(childComplexity int) int
		ReceiverID   func(childComplexity int) int
		SenderID     func(childComplexity int) int
	}

	FriendsPage struct {
		HasMore      func(childComplexity int) int
		PageNumber   func(childComplexity int) int
		PageSize     func(childComplexity int) int
		Results      func(childComplexity int) int
		TotalResults func(childComplexity int) int
	}

	Game struct {
		Config             func(childComplexity int) int
		CreatedBy          func(childComplexity int) int
		EncryptedQuestions func(childComplexity int) int
		EndTime            func(childComplexity int) int
		GameCategory       func(childComplexity int) int
		GameMode           func(childComplexity int) int
		GameStatus         func(childComplexity int) int
		GameType           func(childComplexity int) int
		ID                 func(childComplexity int) int
		LeaderBoard        func(childComplexity int) int
		MinifiedQuestions  func(childComplexity int) int
		Players            func(childComplexity int) int
		Questions          func(childComplexity int) int
		RematchRequestedBy func(childComplexity int) int
		SeriesID           func(childComplexity int) int
		ShowdownGameConfig func(childComplexity int) int
		ShowdownId         func(childComplexity int) int
		StartTime          func(childComplexity int) int
	}

	GameCanceledOutput struct {
		CreatorID  func(childComplexity int) int
		GameID     func(childComplexity int) int
		OpponentID func(childComplexity int) int
		Status     func(childComplexity int) int
	}

	GameCategorySpecificConfig struct {
		Blitz     func(childComplexity int) int
		Category  func(childComplexity int) int
		Classical func(childComplexity int) int
		Memory    func(childComplexity int) int
		Puzzle    func(childComplexity int) int
	}

	GameConfig struct {
		CategorySpecificConfig func(childComplexity int) int
		DifficultyLevel        func(childComplexity int) int
		GameType               func(childComplexity int) int
		GameTypeSpecificConfig func(childComplexity int) int
		MaxTimePerQuestion     func(childComplexity int) int
		ModeSpecificConfig     func(childComplexity int) int
		NumPlayers             func(childComplexity int) int
		QuestionTags           func(childComplexity int) int
		TimeLimit              func(childComplexity int) int
	}

	GameDetailedAnalysis struct {
		Game      func(childComplexity int) int
		Questions func(childComplexity int) int
	}

	GameModeSpecificConfig struct {
		GroupPlay        func(childComplexity int) int
		Mode             func(childComplexity int) int
		OnlineChallenge  func(childComplexity int) int
		OnlineSearch     func(childComplexity int) int
		PlayViaLink      func(childComplexity int) int
		Practice         func(childComplexity int) int
		RushWithTime     func(childComplexity int) int
		RushWithoutTime  func(childComplexity int) int
		SumdayShowdown   func(childComplexity int) int
		SurvivalSaturday func(childComplexity int) int
	}

	GameQuestion struct {
		Question    func(childComplexity int) int
		Stats       func(childComplexity int) int
		Submissions func(childComplexity int) int
	}

	GameQuestionAnalysis struct {
		AvgTimes       func(childComplexity int) int
		GlobalAvgTime  func(childComplexity int) int
		GlobalBestTime func(childComplexity int) int
		Question       func(childComplexity int) int
	}

	GameQuestionStats struct {
		FastestTime func(childComplexity int) int
		UserIds     func(childComplexity int) int
	}

	GameSeries struct {
		GameIds   func(childComplexity int) int
		ID        func(childComplexity int) int
		PlayerIds func(childComplexity int) int
	}

	GameTypeSpecificConfig struct {
		Type func(childComplexity int) int
	}

	GetGamesByRatingOutput struct {
		Games       func(childComplexity int) int
		PuzzleGames func(childComplexity int) int
		TotalCount  func(childComplexity int) int
		Users       func(childComplexity int) int
	}

	GetGamesOutput struct {
		Games func(childComplexity int) int
		Users func(childComplexity int) int
	}

	GetPuzzleGamesOutput struct {
		Games func(childComplexity int) int
		Users func(childComplexity int) int
	}

	GlobalPreset struct {
		BestStreak              func(childComplexity int) int
		BestTime                func(childComplexity int) int
		GlobalAccuracy          func(childComplexity int) int
		GlobalAverageTime       func(childComplexity int) int
		ID                      func(childComplexity int) int
		Identifier              func(childComplexity int) int
		IncorrectSubmissions    func(childComplexity int) int
		NumOfCorrectSubmissions func(childComplexity int) int
		Top10Mathletes          func(childComplexity int) int
		TotalQuestionsSolved    func(childComplexity int) int
	}

	GlobalPresets struct {
		GlobalPresets func(childComplexity int) int
		TotalCount    func(childComplexity int) int
	}

	GroupPlayGameConfig struct {
		DifficultyLevel    func(childComplexity int) int
		MaxGapBwGame       func(childComplexity int) int
		MaxPlayers         func(childComplexity int) int
		MaxTimePerQuestion func(childComplexity int) int
		MinPlayers         func(childComplexity int) int
		QuestionTags       func(childComplexity int) int
	}

	HectocPuzzle struct {
		PuzzleString func(childComplexity int) int
	}

	HostDetails struct {
		Logo func(childComplexity int) int
		Name func(childComplexity int) int
	}

	HostInfo struct {
		HostLogo func(childComplexity int) int
		HostType func(childComplexity int) int
		ID       func(childComplexity int) int
	}

	Institution struct {
		City    func(childComplexity int) int
		Country func(childComplexity int) int
		Domains func(childComplexity int) int
		ID      func(childComplexity int) int
		Name    func(childComplexity int) int
		Slug    func(childComplexity int) int
		State   func(childComplexity int) int
	}

	JoinedWeeklyLeagueEvent struct {
		LeagueInfo func(childComplexity int) int
	}

	KenKenPuzzle struct {
		PuzzleString func(childComplexity int) int
	}

	LeaderBoardEntry struct {
		Correct           func(childComplexity int) int
		Incorrect         func(childComplexity int) int
		Rank              func(childComplexity int) int
		RatingChange      func(childComplexity int) int
		StatikCoinsEarned func(childComplexity int) int
		TotalPoints       func(childComplexity int) int
		UserID            func(childComplexity int) int
	}

	LeaderParticipantEntity struct {
		ID          func(childComplexity int) int
		Participant func(childComplexity int) int
		Rank        func(childComplexity int) int
		Score       func(childComplexity int) int
		ShowdownId  func(childComplexity int) int
		UserId      func(childComplexity int) int
	}

	LeaderboardConnection struct {
		Edges    func(childComplexity int) int
		PageInfo func(childComplexity int) int
	}

	LeaderboardEdge struct {
		Cursor func(childComplexity int) int
		Node   func(childComplexity int) int
	}

	LeaderboardPage struct {
		HasMore      func(childComplexity int) int
		PageNumber   func(childComplexity int) int
		PageSize     func(childComplexity int) int
		Results      func(childComplexity int) int
		TotalResults func(childComplexity int) int
	}

	League struct {
		ChatRoomId               func(childComplexity int) int
		CurrentUserParticipation func(childComplexity int) int
		CurrentUserResult        func(childComplexity int) int
		Details                  func(childComplexity int) int
		HostedBy                 func(childComplexity int) int
		HostedByV2               func(childComplexity int) int
		ID                       func(childComplexity int) int
		LeagueEnd                func(childComplexity int) int
		LeagueStart              func(childComplexity int) int
		Name                     func(childComplexity int) int
		RegistrationCount        func(childComplexity int) int
		RegistrationEnd          func(childComplexity int) int
		RegistrationForm         func(childComplexity int) int
		RegistrationStart        func(childComplexity int) int
	}

	LeagueDetails struct {
		About        func(childComplexity int) int
		Awards       func(childComplexity int) int
		Instructions func(childComplexity int) int
		Requirements func(childComplexity int) int
	}

	LeagueInfo struct {
		CoinsTillLastWeek func(childComplexity int) int
		GroupID           func(childComplexity int) int
		HasParticipated   func(childComplexity int) int
		League            func(childComplexity int) int
		ProgressState     func(childComplexity int) int
		UpdatedAt         func(childComplexity int) int
	}

	LeagueLeaderboardEntry struct {
		ActivitySummary func(childComplexity int) int
		Rank            func(childComplexity int) int
		StatikCoins     func(childComplexity int) int
		User            func(childComplexity int) int
	}

	LeagueLeaderboardPage struct {
		Participants func(childComplexity int) int
		TotalCount   func(childComplexity int) int
	}

	LeagueParticipant struct {
		ID               func(childComplexity int) int
		JoinedAt         func(childComplexity int) int
		LeagueID         func(childComplexity int) int
		RegistrationData func(childComplexity int) int
		UserID           func(childComplexity int) int
	}

	Mathlete struct {
		BestStreak              func(childComplexity int) int
		BestTime                func(childComplexity int) int
		NumOfCorrectSubmissions func(childComplexity int) int
		QuestionsSolved         func(childComplexity int) int
		UserID                  func(childComplexity int) int
	}

	Message struct {
		Attachment func(childComplexity int) int
		Content    func(childComplexity int) int
		CreatedAt  func(childComplexity int) int
		GroupID    func(childComplexity int) int
		ID         func(childComplexity int) int
		Sender     func(childComplexity int) int
		SenderInfo func(childComplexity int) int
	}

	MessageGroup struct {
		Alias                func(childComplexity int) int
		CreatedAt            func(childComplexity int) int
		DeepLinkRoute        func(childComplexity int) int
		GroupName            func(childComplexity int) int
		GroupType            func(childComplexity int) int
		ID                   func(childComplexity int) int
		LastMessage          func(childComplexity int) int
		LastMessageRead      func(childComplexity int) int
		Members              func(childComplexity int) int
		Messages             func(childComplexity int) int
		UpdatedAt            func(childComplexity int) int
		UserInfoIfIndividual func(childComplexity int) int
	}

	MessageRead struct {
		LastMessageRead func(childComplexity int) int
		UserID          func(childComplexity int) int
	}

	MinifiedGame struct {
		Config      func(childComplexity int) int
		EndTime     func(childComplexity int) int
		ID          func(childComplexity int) int
		LeaderBoard func(childComplexity int) int
		Players     func(childComplexity int) int
		StartTime   func(childComplexity int) int
	}

	MinifiedPuzzleGame struct {
		Config      func(childComplexity int) int
		EndTime     func(childComplexity int) int
		ID          func(childComplexity int) int
		LeaderBoard func(childComplexity int) int
		Players     func(childComplexity int) int
		StartTime   func(childComplexity int) int
	}

	Mutation struct {
		AbortSearching                     func(childComplexity int) int
		AbortSearchingForPuzzleGame        func(childComplexity int) int
		AcceptChallenge                    func(childComplexity int, gameID primitive.ObjectID) int
		AcceptChallengeOfPuzzleGame        func(childComplexity int, gameID primitive.ObjectID) int
		AcceptFriendRequest                func(childComplexity int, acceptRequestInput *models.FriendRequestInput) int
		AcceptRematch                      func(childComplexity int, gameID primitive.ObjectID) int
		AcceptRematchOfPuzzleGame          func(childComplexity int, gameID primitive.ObjectID) int
		AddClubMember                      func(childComplexity int, clubID primitive.ObjectID, userID primitive.ObjectID) int
		AttemptDailyChallenge              func(childComplexity int, challengeID primitive.ObjectID) int
		CancelGame                         func(childComplexity int, gameID primitive.ObjectID) int
		CancelPuzzleGame                   func(childComplexity int, gameID primitive.ObjectID) int
		CancelRematchRequest               func(childComplexity int, gameID primitive.ObjectID) int
		ChallengeUser                      func(childComplexity int, challengeUserInput *models.ChallengeUserInput) int
		ChallengeUserForPuzzleGame         func(childComplexity int, challengeUserInput *models.ChallengeUserForPuzzleGameInput) int
		CreateAnnouncement                 func(childComplexity int, input models.CreateAnnouncementInput) int
		CreateClub                         func(childComplexity int, input models.CreateClubInput) int
		CreateClubAnnouncement             func(childComplexity int, input models.CreateClubAnnouncementInput) int
		CreateClubEvent                    func(childComplexity int, input models.CreateClubEventInput) int
		CreateContest                      func(childComplexity int, input models.CreateContestInput) int
		CreateForum                        func(childComplexity int, input models.CreateForumInput) int
		CreateForumReply                   func(childComplexity int, input models.CreateForumReplyInput) int
		CreateForumThread                  func(childComplexity int, input models.CreateForumThreadInput) int
		CreateGame                         func(childComplexity int, gameConfig *models.GameConfigInput) int
		CreateInstitution                  func(childComplexity int, input models.CreateInstitutionInput) int
		CreateLeague                       func(childComplexity int, input models.CreateLeagueInput) int
		CreatePuzzleGame                   func(childComplexity int, gameConfig *models.PuzzleGameConfigInput) int
		CreateShowdown                     func(childComplexity int, input models.CreateShowdownInput) int
		CreateUser                         func(childComplexity int, userInput *models.UserInput) int
		DeleteAnnouncement                 func(childComplexity int, id primitive.ObjectID) int
		DeleteClub                         func(childComplexity int, id primitive.ObjectID) int
		DeleteClubAnnouncement             func(childComplexity int, id primitive.ObjectID) int
		DeleteClubEvent                    func(childComplexity int, id primitive.ObjectID) int
		DeleteUser                         func(childComplexity int) int
		DeleteUserSavedPreset              func(childComplexity int, presetID primitive.ObjectID) int
		EndAbilityDuelsGame                func(childComplexity int, gameID *primitive.ObjectID) int
		EndGame                            func(childComplexity int, gameID *primitive.ObjectID) int
		EndGameForShowdown                 func(childComplexity int, gameID *primitive.ObjectID) int
		EndPuzzleGame                      func(childComplexity int, gameID primitive.ObjectID) int
		FollowUser                         func(childComplexity int, followUserInput *models.FollowUserInput) int
		GetUpdatedUserStreaks              func(childComplexity int) int
		GoogleLogin                        func(childComplexity int, authCode string, tokenType *string, expiresIn *string, guestID *primitive.ObjectID) int
		JoinClub                           func(childComplexity int, clubID primitive.ObjectID) int
		JoinClubEvent                      func(childComplexity int, eventID primitive.ObjectID) int
		JoinGame                           func(childComplexity int, joinGameInput *models.JoinGameInput) int
		JoinLeague                         func(childComplexity int, joinLeagueInput *models.JoinLeagueInput) int
		JoinPuzzleGame                     func(childComplexity int, gameID primitive.ObjectID) int
		JoinVirtualContest                 func(childComplexity int, contestID primitive.ObjectID) int
		LeaveClub                          func(childComplexity int, clubID primitive.ObjectID) int
		LeaveClubEvent                     func(childComplexity int, eventID primitive.ObjectID) int
		LeaveGame                          func(childComplexity int, gameID primitive.ObjectID) int
		LeavePuzzleGame                    func(childComplexity int, gameID primitive.ObjectID) int
		LegacyGoogleLogin                  func(childComplexity int, idToken string, guestID *primitive.ObjectID) int
		LoginAsGuest                       func(childComplexity int, guestID primitive.ObjectID) int
		MarkAllAnnouncementsAsRead         func(childComplexity int) int
		MarkAnnouncementAsRead             func(childComplexity int, announcementID primitive.ObjectID) int
		RegisterDeviceToken                func(childComplexity int, pushNotificationToken string, deviceID *string, platform *string) int
		RegisterForContest                 func(childComplexity int, input models.RegistrationFormValuesInput) int
		RegisterForShowdown                func(childComplexity int, input models.ShowdownRegistrationFormValuesInput) int
		RejectChallenge                    func(childComplexity int, gameID primitive.ObjectID) int
		RejectChallengeOfPuzzleGame        func(childComplexity int, gameID primitive.ObjectID) int
		RejectFriendRequest                func(childComplexity int, rejectRequestInput *models.FriendRequestInput) int
		RejectRematch                      func(childComplexity int, gameID primitive.ObjectID) int
		RejectRematchOfPuzzleGame          func(childComplexity int, gameID primitive.ObjectID) int
		RemoveClubMember                   func(childComplexity int, clubID primitive.ObjectID, userID primitive.ObjectID) int
		RemoveFollower                     func(childComplexity int, removeFollowerInput *models.RemoveFollowerInput) int
		RemoveFriend                       func(childComplexity int, removeFriendInput *models.RemoveFriendInput) int
		RemovePlayer                       func(childComplexity int, gameID primitive.ObjectID, playerID primitive.ObjectID) int
		RemovePlayerFromPuzzleGame         func(childComplexity int, gameID primitive.ObjectID, playerID primitive.ObjectID) int
		RequestRematch                     func(childComplexity int, gameID primitive.ObjectID) int
		RequestRematchForPuzzleGame        func(childComplexity int, gameID primitive.ObjectID) int
		SaveUserPreset                     func(childComplexity int, identifier *string, name *string) int
		SendFeedback                       func(childComplexity int, input models.Feedback) int
		SendFriendRequest                  func(childComplexity int, sendRequestInput *models.FriendRequestInput) int
		SendOtp                            func(childComplexity int, email string) int
		SignInWithApple                    func(childComplexity int, input models.AppleSignInInput) int
		StartGame                          func(childComplexity int, startGameInput *models.StartGameInput) int
		StartGameForShowdown               func(childComplexity int, input *models.StartGameForShowdownInput) int
		StartPuzzleGame                    func(childComplexity int, gameID primitive.ObjectID) int
		StartSearching                     func(childComplexity int, gameConfig *models.GameConfigInput) int
		StartSearchingForPuzzleGame        func(childComplexity int, gameConfig *models.PuzzleGameConfigInput) int
		SubmitAnswer                       func(childComplexity int, answerInput *models.SubmitAnswerInput) int
		SubmitChallengeResult              func(childComplexity int, input models.SubmitSolutionInput) int
		SubmitContestAnswer                func(childComplexity int, contestID primitive.ObjectID, questionID string, answer string) int
		SubmitFlashAnzanAnswer             func(childComplexity int, answerInput *models.SubmitFlashAnzanAnswerInput) int
		SubmitPuzzleGameAnswer             func(childComplexity int, answerInput *models.SubmitPuzzleGameAnswerInput) int
		SubmitPuzzleGameRush               func(childComplexity int, input models.SubmitPuzzleRushGame) int
		SubmitPuzzleSolution               func(childComplexity int, puzzleID primitive.ObjectID, timeSpent int) int
		SubmitRatingFixtureResponses       func(childComplexity int, submission []*int, timeTaken int) int
		SubmitReferral                     func(childComplexity int, referralCode string) int
		SubmitUserPresetResult             func(childComplexity int, userPresetResultInput *models.UserPresetResultInput) int
		SubmitVirtualContestAnswer         func(childComplexity int, contestID primitive.ObjectID, questionID string, answer string) int
		TakePledge                         func(childComplexity int, duration *int) int
		UnFollowUser                       func(childComplexity int, unFollowUserInput *models.UnFollowUserInput) int
		UnregisterDeviceToken              func(childComplexity int, pushNotificationToken *string, deviceID *string) int
		UnregisterFromContest              func(childComplexity int, contestID primitive.ObjectID) int
		UnregisterFromShowdown             func(childComplexity int, showdownID primitive.ObjectID) int
		UpdateAnnouncement                 func(childComplexity int, id primitive.ObjectID, input models.UpdateAnnouncementInput) int
		UpdateClub                         func(childComplexity int, input models.UpdateClubInput) int
		UpdateClubEvent                    func(childComplexity int, input models.UpdateClubEventInput) int
		UpdateContestParticipantStartTime  func(childComplexity int, contestID primitive.ObjectID) int
		UpdateLastMessageRead              func(childComplexity int, groupID primitive.ObjectID, lastMessageReadID primitive.ObjectID) int
		UpdateLastReadFeedID               func(childComplexity int, lastReadFeedID primitive.ObjectID) int
		UpdateLikeStatus                   func(childComplexity int, feedID primitive.ObjectID) int
		UpdateMemberRole                   func(childComplexity int, clubID primitive.ObjectID, userID primitive.ObjectID, role models.ClubMemberRole) int
		UpdateRatingBasedOnFixtureResponse func(childComplexity int, userStance models.UserStance) int
		UpdateUser                         func(childComplexity int, updateUserInput *models.UpdateUserInput) int
		UpdateUserSettings                 func(childComplexity int, settings *models.UpdateSettingsInput) int
		UploadClubBannerImage              func(childComplexity int, file graphql.Upload, clubID primitive.ObjectID) int
		UploadClubLogoImage                func(childComplexity int, file graphql.Upload, clubID primitive.ObjectID) int
		UploadFiles                        func(childComplexity int, files []*graphql.Upload) int
		UploadProfilePicture               func(childComplexity int, file graphql.Upload) int
		UseStreakFreezer                   func(childComplexity int) int
		VerifyOtp                          func(childComplexity int, otp string) int
		WithdrawFriendRequest              func(childComplexity int, withdrawFriendRequestInput *models.WithdrawFriendRequestInput) int
	}

	MyInstituteUsersPage struct {
		HasMore      func(childComplexity int) int
		PageNumber   func(childComplexity int) int
		PageSize     func(childComplexity int) int
		Results      func(childComplexity int) int
		TotalResults func(childComplexity int) int
	}

	OnlineUsersPage struct {
		HasMore      func(childComplexity int) int
		PageNumber   func(childComplexity int) int
		PageSize     func(childComplexity int) int
		TotalResults func(childComplexity int) int
		Users        func(childComplexity int) int
	}

	PageInfo struct {
		EndCursor   func(childComplexity int) int
		HasNextPage func(childComplexity int) int
	}

	PaginatedContests struct {
		Contests   func(childComplexity int) int
		TotalCount func(childComplexity int) int
	}

	PaginatedLeaderboard struct {
		Count        func(childComplexity int) int
		Participants func(childComplexity int) int
	}

	PaginatedLeagues struct {
		League     func(childComplexity int) int
		TotalCount func(childComplexity int) int
	}

	PaginatedMessage struct {
		HasMore       func(childComplexity int) int
		LastMessageID func(childComplexity int) int
		Messages      func(childComplexity int) int
	}

	PaginatedMessageGroups struct {
		Groups   func(childComplexity int) int
		HasMore  func(childComplexity int) int
		IsRead   func(childComplexity int) int
		NextPage func(childComplexity int) int
	}

	PaginatedShowdowns struct {
		Count     func(childComplexity int) int
		Showdowns func(childComplexity int) int
	}

	ParticipantBasicInfo struct {
		ID         func(childComplexity int) int
		Rounds     func(childComplexity int) int
		ShowdownID func(childComplexity int) int
		UserID     func(childComplexity int) int
		UserInfo   func(childComplexity int) int
	}

	PhoneNumber struct {
		CountryCode func(childComplexity int) int
		Number      func(childComplexity int) int
	}

	PlatformStats struct {
		TotalGames         func(childComplexity int) int
		TotalSignedInUsers func(childComplexity int) int
		TotalUsers         func(childComplexity int) int
	}

	PlayedPresets struct {
		AvgTime    func(childComplexity int) int
		Identifier func(childComplexity int) int
	}

	Player struct {
		Rating      func(childComplexity int) int
		StatikCoins func(childComplexity int) int
		Status      func(childComplexity int) int
		TimeLeft    func(childComplexity int) int
		UserID      func(childComplexity int) int
	}

	PlayerSetting struct {
		MaxRating func(childComplexity int) int
		MinRating func(childComplexity int) int
	}

	Puzzle struct {
		AvailableAnswers  func(childComplexity int) int
		Cells             func(childComplexity int) int
		CurrentUserResult func(childComplexity int) int
		Difficulty        func(childComplexity int) int
		HasAttempted      func(childComplexity int) int
		ID                func(childComplexity int) int
		PuzzleDate        func(childComplexity int) int
		PuzzleType        func(childComplexity int) int
		SolvedBy          func(childComplexity int) int
		Stats             func(childComplexity int) int
		TypeSpecific      func(childComplexity int) int
		UserStat          func(childComplexity int) int
	}

	PuzzleGame struct {
		Config             func(childComplexity int) int
		CreatedBy          func(childComplexity int) int
		EndTime            func(childComplexity int) int
		GameStatus         func(childComplexity int) int
		GameType           func(childComplexity int) int
		ID                 func(childComplexity int) int
		IsRatedGame        func(childComplexity int) int
		LeaderBoard        func(childComplexity int) int
		Players            func(childComplexity int) int
		Questions          func(childComplexity int) int
		RematchRequestedBy func(childComplexity int) int
		SeriesID           func(childComplexity int) int
		StartTime          func(childComplexity int) int
	}

	PuzzleGameConfig struct {
		DifficultyLevel    func(childComplexity int) int
		GameType           func(childComplexity int) int
		MaxTimePerQuestion func(childComplexity int) int
		NumOfQuestions     func(childComplexity int) int
		NumPlayers         func(childComplexity int) int
		TimeLimit          func(childComplexity int) int
	}

	PuzzleGameQuestion struct {
		Id          func(childComplexity int) int
		Question    func(childComplexity int) int
		Stats       func(childComplexity int) int
		Submissions func(childComplexity int) int
	}

	PuzzleGameQuestionStats struct {
		FastestTime func(childComplexity int) int
		UserIds     func(childComplexity int) int
	}

	PuzzleLeaderboardEntry struct {
		Correct           func(childComplexity int) int
		Incorrect         func(childComplexity int) int
		Rank              func(childComplexity int) int
		RatingChange      func(childComplexity int) int
		StatikCoinsEarned func(childComplexity int) int
		TotalPoints       func(childComplexity int) int
		UserID            func(childComplexity int) int
	}

	PuzzleMonthlySubmissionReport struct {
		PuzzleSubmissions func(childComplexity int) int
		YearMonth         func(childComplexity int) int
	}

	PuzzleQuestionSubmission struct {
		InCorrectAttempts func(childComplexity int) int
		IsCorrect         func(childComplexity int) int
		Points            func(childComplexity int) int
		SubmissionTime    func(childComplexity int) int
		SubmittedValues   func(childComplexity int) int
		TimeTaken         func(childComplexity int) int
		UserID            func(childComplexity int) int
	}

	PuzzleResult struct {
		CompletedAt       func(childComplexity int) int
		ID                func(childComplexity int) int
		PuzzleDate        func(childComplexity int) int
		PuzzleID          func(childComplexity int) int
		PuzzleType        func(childComplexity int) int
		StatikCoinsEarned func(childComplexity int) int
		TimeSpent         func(childComplexity int) int
		UserID            func(childComplexity int) int
	}

	PuzzleStats struct {
		AverageTime     func(childComplexity int) int
		BestTime        func(childComplexity int) int
		NumOfSubmission func(childComplexity int) int
	}

	PuzzleTypeSpecificDetails struct {
		CrossMath  func(childComplexity int) int
		Hectoc     func(childComplexity int) int
		KenKen     func(childComplexity int) int
		PuzzleType func(childComplexity int) int
	}

	PuzzleUserStats struct {
		AverageTime     func(childComplexity int) int
		BestTime        func(childComplexity int) int
		ID              func(childComplexity int) int
		NumOfSubmission func(childComplexity int) int
		PuzzleType      func(childComplexity int) int
		UserID          func(childComplexity int) int
	}

	Query struct {
		CheckBotBehavior                       func(childComplexity int, challengeID primitive.ObjectID, userID primitive.ObjectID) int
		CheckIfPledgeTaken                     func(childComplexity int) int
		CheckUserStreakStatus                  func(childComplexity int) int
		Club                                   func(childComplexity int, id primitive.ObjectID) int
		ClubAnnouncements                      func(childComplexity int, page *int, pageSize *int, clubID *primitive.ObjectID, from *time.Time, to *time.Time) int
		ClubEvent                              func(childComplexity int, id primitive.ObjectID) int
		ClubEvents                             func(childComplexity int, page *int, pageSize *int, clubID *primitive.ObjectID, clubEventType *models.ClubEventType, from *time.Time, to *time.Time) int
		ClubMembers                            func(childComplexity int, clubID primitive.ObjectID, clubMembershipStatus models.ClubMembershipStatus, page *int, pageSize *int) int
		Clubs                                  func(childComplexity int, page *int, pageSize *int, visibility *models.Visibility, search *string) int
		Forum                                  func(childComplexity int, id primitive.ObjectID) int
		ForumReplies                           func(childComplexity int, threadID primitive.ObjectID, page *int, pageSize *int) int
		ForumThread                            func(childComplexity int, id primitive.ObjectID) int
		ForumThreads                           func(childComplexity int, forumID primitive.ObjectID, page *int, pageSize *int) int
		Forums                                 func(childComplexity int, clubID primitive.ObjectID, page *int, pageSize *int) int
		GetAllAnnouncements                    func(childComplexity int, limit *int, offset *int, typeArg *models.AnnouncementType) int
		GetAllMessageGroups                    func(childComplexity int, input *models.GetAllMessageGroupsInput) int
		GetAnnouncement                        func(childComplexity int, id primitive.ObjectID) int
		GetClubLeaderboard                     func(childComplexity int, clubID primitive.ObjectID, page *int, pageSize *int) int
		GetClubMemberInfo                      func(childComplexity int, clubID primitive.ObjectID) int
		GetContestByID                         func(childComplexity int, contestID primitive.ObjectID) int
		GetContestLeaderboard                  func(childComplexity int, contestID primitive.ObjectID, pageNumber *int, pageSize *int) int
		GetContestsByStatus                    func(childComplexity int, statuses []models.ContestStatus, page *int, pageSize *int, sortDirection *string) int
		GetCurrentUser                         func(childComplexity int) int
		GetDailyChallenge                      func(childComplexity int) int
		GetDailyChallengeByID                  func(childComplexity int, id primitive.ObjectID) int
		GetDailyChallengeLeaderboard           func(childComplexity int, challengeNumber *int, pageNumber *int, pageSize *int) int
		GetDailyChallengeLeaderboardByDivision func(childComplexity int, dateStr *string, division *models.ChallengeDivision, pageNumber *int, pageSize *int) int
		GetDailyChallengeLeaderboardByDivison  func(childComplexity int, dateStr *string, division *models.ChallengeDivision, pageNumber *int, pageSize *int) int
		GetDailyChallenges                     func(childComplexity int) int
		GetDailyPuzzle                         func(childComplexity int, date string) int
		GetDailyPuzzleByType                   func(childComplexity int, date string, puzzleType models.PuzzleType) int
		GetFeaturedContests                    func(childComplexity int) int
		GetFeaturedShowdown                    func(childComplexity int) int
		GetFicturesByShowdownID                func(childComplexity int, showdownID primitive.ObjectID) int
		GetFollowers                           func(childComplexity int, page *int, pageSize *int) int
		GetFollowings                          func(childComplexity int, page *int, pageSize *int) int
		GetFriends                             func(childComplexity int, page *int, pageSize *int, sortOption *models.SortOptions) int
		GetFriendsLeaderboard                  func(childComplexity int, page *int, pageSize *int, ratingType *string) int
		GetFriendsTop5CrossMathPuzzleRushStats func(childComplexity int) int
		GetFriendsTopPlayers                   func(childComplexity int) int
		GetGameByID                            func(childComplexity int, gameID *primitive.ObjectID) int
		GetGameDetailedAnalysis                func(childComplexity int, gameID *primitive.ObjectID) int
		GetGameSeriesByID                      func(childComplexity int, gameSeriesID primitive.ObjectID) int
		GetGamesByUser                         func(childComplexity int, payload *models.GetGamesInput) int
		GetGlobalPresets                       func(childComplexity int, page *int, pageSize *int) int
		GetGlobalPresetsByIdentifier           func(childComplexity int, identifier *string) int
		GetGlobalTop5CrossMathPuzzleRushStats  func(childComplexity int) int
		GetGlobalTopPlayers                    func(childComplexity int) int
		GetLeague                              func(childComplexity int, id primitive.ObjectID) int
		GetLeagueLeaderboard                   func(childComplexity int, leagueID primitive.ObjectID, page int, pageSize int) int
		GetLeaguesByStatus                     func(childComplexity int, statuses []models.LeagueStatus, page *int, pageSize *int, sortDirection *string) int
		GetMessageGroupDetailsByID             func(childComplexity int, groupID primitive.ObjectID) int
		GetMessageGroupIDForFriends            func(childComplexity int, friendID primitive.ObjectID) int
		GetMessagesByGroupID                   func(childComplexity int, groupID primitive.ObjectID, lastMessageID *primitive.ObjectID, pageSize *int, sortDirection *models.SortDirection) int
		GetMyCrossMathPuzzleRushStats          func(childComplexity int) int
		GetPaginatedLeaderboard                func(childComplexity int, input *models.PaginatedLeaderboardInput) int
		GetPendingFriendRequests               func(childComplexity int, page *int, pageSize *int) int
		GetPlatformStats                       func(childComplexity int) int
		GetPuzzleGameByID                      func(childComplexity int, gameID primitive.ObjectID) int
		GetPuzzleGamesByUser                   func(childComplexity int, payload *models.GetPuzzleGamesInput) int
		GetPuzzleSubmissionsByMonth            func(childComplexity int, yearMonths []string) int
		GetPuzzleSubmissionsByMonthByType      func(childComplexity int, yearMonths []string, puzzleType models.PuzzleType) int
		GetRatingFixtureQuestions              func(childComplexity int) int
		GetRatingFixtureSubmission             func(childComplexity int) int
		GetRegisteredContests                  func(childComplexity int) int
		GetShowdownByID                        func(childComplexity int, showdownID primitive.ObjectID) int
		GetShowdownByStatus                    func(childComplexity int, status *models.ShowdownContestStatus) int
		GetShowdownsByStatus                   func(childComplexity int, statuses []models.ShowdownContestStatus, page *int, pageSize *int, sortDirection *string) int
		GetTimeSpentByUser                     func(childComplexity int, date *string) int
		GetUnreadAnnouncements                 func(childComplexity int, limit *int, offset *int) int
		GetUpcomingShowdown                    func(childComplexity int) int
		GetUserByID                            func(childComplexity int, userID primitive.ObjectID) int
		GetUserByUsername                      func(childComplexity int, username *string) int
		GetUserContestResult                   func(childComplexity int, contestID primitive.ObjectID) int
		GetUserContestSubmissions              func(childComplexity int, userID *primitive.ObjectID, contestID primitive.ObjectID) int
		GetUserFeeds                           func(childComplexity int, lastID *primitive.ObjectID, pageSize *int) int
		GetUserGamesByRatingType               func(childComplexity int, payload *models.GetGamesByRatingInput) int
		GetUserLeagueGroupLeaderboard          func(childComplexity int, page *int, pageSize *int) int
		GetUserPresetStatsByDate               func(childComplexity int, username *string, durationFilter *int, identifier *string) int
		GetUserPresetsByIdentifier             func(childComplexity int, identifier *string) int
		GetUserPuzzleStats                     func(childComplexity int) int
		GetUserPuzzleStatsByType               func(childComplexity int, puzzleType models.PuzzleType) int
		GetUserRecentPresets                   func(childComplexity int) int
		GetUserResolution                      func(childComplexity int) int
		GetUserResult                          func(childComplexity int, challengeNumber *int) int
		GetUserResultByDailyChallengeID        func(childComplexity int, challengeID primitive.ObjectID) int
		GetUserResultByDivision                func(childComplexity int, dateStr *string, division *models.ChallengeDivision) int
		GetUserResultByDivison                 func(childComplexity int, dateStr *string, division *models.ChallengeDivision) int
		GetUserSavedPresets                    func(childComplexity int, page *int, pageSize *int) int
		GetUserSettings                        func(childComplexity int) int
		GetUserStreakHistoryByMonth            func(childComplexity int, yearMonths []string) int
		GetUserStreakShieldTransactions        func(childComplexity int, page *int, pageSize *int) int
		GetUsersAllPlayedPresets               func(childComplexity int, username *string) int
		GetUsersOfMyInstitute                  func(childComplexity int, page *int, pageSize *int) int
		GetUsersWeeklyStatikCoins              func(childComplexity int) int
		GetUsersWeeklyStatikCoinsV2            func(childComplexity int, userID primitive.ObjectID) int
		IsUsernameAvailable                    func(childComplexity int, username string) int
		Leaderboard                            func(childComplexity int, countryCode *string, searchKey *string, first *int, after *string) int
		LeaderboardNew                         func(childComplexity int, countryCode *string, searchKey *string, page *int, limit *int, ratingType *string) int
		LeaderboardV3                          func(childComplexity int, countryCode *string, searchKey *string, page *int, limit *int) int
		Login                                  func(childComplexity int, email string, password string) int
		OnlineUsers                            func(childComplexity int, page int, pageSize int) int
		SearchInstitutions                     func(childComplexity int, query string, limit *int) int
		SearchUsersInMyInstitute               func(childComplexity int, searchKey *string, page *int, pageSize *int) int
		StatikCoinsLeaderboard                 func(childComplexity int, page int, pageSize *int, leaderboardType *models.StatikCoinLeaderboardType) int
		VerifyToken                            func(childComplexity int, token string) int
	}

	Question struct {
		Answers          func(childComplexity int) int
		Description      func(childComplexity int) int
		Expression       func(childComplexity int) int
		FastestTimeTaken func(childComplexity int) int
		ID               func(childComplexity int) int
		MaxTimeLimit     func(childComplexity int) int
		Options          func(childComplexity int) int
		PresetIdentifier func(childComplexity int) int
		QuestionType     func(childComplexity int) int
		Rating           func(childComplexity int) int
		Tags             func(childComplexity int) int
	}

	RatingFixtureOutput struct {
		NewRating func(childComplexity int) int
	}

	Referral struct {
		ID         func(childComplexity int) int
		ReferredAt func(childComplexity int) int
		ReferredTo func(childComplexity int) int
		Referrer   func(childComplexity int) int
	}

	ReferralDetails struct {
		Referral     func(childComplexity int) int
		ReferredUser func(childComplexity int) int
	}

	RegistrationFieldData struct {
		Name   func(childComplexity int) int
		Values func(childComplexity int) int
	}

	RegistrationForm struct {
		Fields func(childComplexity int) int
		ID     func(childComplexity int) int
	}

	RematchRequestOutput struct {
		GameID      func(childComplexity int) int
		GameType    func(childComplexity int) int
		NewGameID   func(childComplexity int) int
		RequestedBy func(childComplexity int) int
		Status      func(childComplexity int) int
		User        func(childComplexity int) int
		WaitingTime func(childComplexity int) int
	}

	RepliesPage struct {
		HasMore      func(childComplexity int) int
		PageNumber   func(childComplexity int) int
		PageSize     func(childComplexity int) int
		Results      func(childComplexity int) int
		TotalResults func(childComplexity int) int
	}

	Result struct {
		ChallengeID    func(childComplexity int) int
		CompletedAt    func(childComplexity int) int
		Score          func(childComplexity int) int
		SubmittedTimes func(childComplexity int) int
		UserID         func(childComplexity int) int
	}

	RoundConfig struct {
		GameDuration func(childComplexity int) int
		GameType     func(childComplexity int) int
		MaxGapBwGame func(childComplexity int) int
		MaxWaitTime  func(childComplexity int) int
		NumOfGames   func(childComplexity int) int
		NumOfPlayer  func(childComplexity int) int
	}

	SearchSubscriptionOutput struct {
		Event    func(childComplexity int) int
		Game     func(childComplexity int) int
		Opponent func(childComplexity int) int
	}

	SearchUserOutput struct {
		FriendshipStatus  func(childComplexity int) int
		IsFollowing       func(childComplexity int) int
		UserPublicDetails func(childComplexity int) int
	}

	Showdown struct {
		ClubID                   func(childComplexity int) int
		CreatedAt                func(childComplexity int) int
		CurrentRound             func(childComplexity int) int
		CurrentUserParticipation func(childComplexity int) int
		Description              func(childComplexity int) int
		Details                  func(childComplexity int) int
		Duration                 func(childComplexity int) int
		EndTime                  func(childComplexity int) int
		GapBwRounds              func(childComplexity int) int
		HostedBy                 func(childComplexity int) int
		ID                       func(childComplexity int) int
		Instructions             func(childComplexity int) int
		IsRatedEvent             func(childComplexity int) int
		Name                     func(childComplexity int) int
		RecentParticipants       func(childComplexity int) int
		RegistrationCount        func(childComplexity int) int
		RegistrationEndTime      func(childComplexity int) int
		RegistrationForm         func(childComplexity int) int
		RegistrationStartTime    func(childComplexity int) int
		RoundConfig              func(childComplexity int) int
		RoundTime                func(childComplexity int) int
		Rounds                   func(childComplexity int) int
		StartTime                func(childComplexity int) int
		Stats                    func(childComplexity int) int
		Status                   func(childComplexity int) int
		UpdatedAt                func(childComplexity int) int
	}

	ShowdownGameConfig struct {
		HasOpponentNotShown func(childComplexity int) int
		IsRoundEnded        func(childComplexity int) int
		NextGameID          func(childComplexity int) int
		NextGameStartsAt    func(childComplexity int) int
		NumOfGames          func(childComplexity int) int
		Round               func(childComplexity int) int
		ShowdownGamePlayer  func(childComplexity int) int
		TotalGamesPlayed    func(childComplexity int) int
	}

	ShowdownGamePlayer struct {
		IsTie    func(childComplexity int) int
		IsWinner func(childComplexity int) int
		Score    func(childComplexity int) int
		UserID   func(childComplexity int) int
		Wins     func(childComplexity int) int
	}

	ShowdownParticipant struct {
		CreatedAt        func(childComplexity int) int
		HadABye          func(childComplexity int) int
		ID               func(childComplexity int) int
		Rank             func(childComplexity int) int
		RatingChange     func(childComplexity int) int
		RecentOpponents  func(childComplexity int) int
		RegistrationData func(childComplexity int) int
		Rounds           func(childComplexity int) int
		ShowdownID       func(childComplexity int) int
		Stats            func(childComplexity int) int
		Status           func(childComplexity int) int
		TotalScore       func(childComplexity int) int
		UpdatedAt        func(childComplexity int) int
		UserID           func(childComplexity int) int
		UserInfo         func(childComplexity int) int
	}

	ShowdownParticipantDetail struct {
		CurrentRound        func(childComplexity int) int
		ShowdownParticipant func(childComplexity int) int
	}

	ShowdownParticipantStats struct {
		CurrentScore func(childComplexity int) int
		Draw         func(childComplexity int) int
		Loss         func(childComplexity int) int
		Win          func(childComplexity int) int
	}

	ShowdownRound struct {
		Games               func(childComplexity int) int
		HasFailedToPlay     func(childComplexity int) int
		HasJoined           func(childComplexity int) int
		HasOpponentNotShown func(childComplexity int) int
		IsBye               func(childComplexity int) int
		IsRoundEnded        func(childComplexity int) int
		Loose               func(childComplexity int) int
		Opponent            func(childComplexity int) int
		PlayerStatus        func(childComplexity int) int
		Round               func(childComplexity int) int
		Score               func(childComplexity int) int
		TotalGamesPlayed    func(childComplexity int) int
		Wins                func(childComplexity int) int
	}

	ShowdownStats struct {
		TotalGamesPlayed  func(childComplexity int) int
		TotalParticipants func(childComplexity int) int
	}

	ShowdownUserDetails struct {
		Name            func(childComplexity int) int
		ProfileImageURL func(childComplexity int) int
		Rating          func(childComplexity int) int
		Username        func(childComplexity int) int
	}

	StatikCoinLeaderboardEntry struct {
		Rank        func(childComplexity int) int
		StatikCoins func(childComplexity int) int
		User        func(childComplexity int) int
	}

	StatikCoinLeaderboardPage struct {
		HasMore      func(childComplexity int) int
		PageNumber   func(childComplexity int) int
		PageSize     func(childComplexity int) int
		Results      func(childComplexity int) int
		TotalResults func(childComplexity int) int
	}

	StreakDay struct {
		Activity     func(childComplexity int) int
		Date         func(childComplexity int) int
		IsShieldUsed func(childComplexity int) int
	}

	StreakEntry struct {
		Date         func(childComplexity int) int
		IsShieldUsed func(childComplexity int) int
	}

	StreakHistory struct {
		ID               func(childComplexity int) int
		StreakHistoryObj func(childComplexity int) int
	}

	StreakShieldTransaction struct {
		CreatedAt       func(childComplexity int) int
		EarnVia         func(childComplexity int) int
		ID              func(childComplexity int) int
		Quantity        func(childComplexity int) int
		RedeemedOn      func(childComplexity int) int
		ReferralID      func(childComplexity int) int
		TransactionID   func(childComplexity int) int
		TransactionType func(childComplexity int) int
		UpdatedAt       func(childComplexity int) int
		UserID          func(childComplexity int) int
	}

	StreakShieldTransactionOutput struct {
		ReferralDetails func(childComplexity int) int
		Transaction     func(childComplexity int) int
	}

	StreakShieldTransactionPage struct {
		HasMore      func(childComplexity int) int
		PageNumber   func(childComplexity int) int
		PageSize     func(childComplexity int) int
		Results      func(childComplexity int) int
		TotalResults func(childComplexity int) int
	}

	StreakStatusResponse struct {
		CanSaveStreak   func(childComplexity int) int
		HasStreak       func(childComplexity int) int
		LastSevenDays   func(childComplexity int) int
		LostStreakCount func(childComplexity int) int
		MissedDays      func(childComplexity int) int
		StreakFreezers  func(childComplexity int) int
	}

	Submission struct {
		InCorrectAttempts func(childComplexity int) int
		IsCorrect         func(childComplexity int) int
		Points            func(childComplexity int) int
		SubmissionTime    func(childComplexity int) int
		SubmittedValues   func(childComplexity int) int
		TimeTaken         func(childComplexity int) int
		UserID            func(childComplexity int) int
	}

	SubmitChallengeResult struct {
		Message func(childComplexity int) int
		Result  func(childComplexity int) int
		Success func(childComplexity int) int
	}

	Subscription struct {
		ContestLeaderboardUpdated func(childComplexity int, contestID primitive.ObjectID) int
		GameEvent                 func(childComplexity int, gameID *primitive.ObjectID) int
		RematchRequest            func(childComplexity int, gameID primitive.ObjectID) int
		SearchPlayer              func(childComplexity int, userID *primitive.ObjectID) int
		UserEvents                func(childComplexity int, userID *primitive.ObjectID) int
	}

	SubscriptionOutput struct {
		Event    func(childComplexity int) int
		Game     func(childComplexity int) int
		Question func(childComplexity int) int
	}

	ThreadsPage struct {
		HasMore      func(childComplexity int) int
		PageNumber   func(childComplexity int) int
		PageSize     func(childComplexity int) int
		Results      func(childComplexity int) int
		TotalResults func(childComplexity int) int
	}

	TopPlayerEntry struct {
		Rank   func(childComplexity int) int
		Rating func(childComplexity int) int
		User   func(childComplexity int) int
	}

	TopPlayersLeaderboard struct {
		AbilityRating func(childComplexity int) int
		GlobalRating  func(childComplexity int) int
		MemoryRating  func(childComplexity int) int
	}

	User struct {
		AccountStatus         func(childComplexity int) int
		Additional            func(childComplexity int) int
		AwardsAndAchievements func(childComplexity int) int
		Badge                 func(childComplexity int) int
		Bio                   func(childComplexity int) int
		Country               func(childComplexity int) int
		CountryCode           func(childComplexity int) int
		CountryRank           func(childComplexity int) int
		Email                 func(childComplexity int) int
		GlobalRank            func(childComplexity int) int
		HasFixedRating        func(childComplexity int) int
		ID                    func(childComplexity int) int
		InstitutionID         func(childComplexity int) int
		IsBot                 func(childComplexity int) int
		IsDeleted             func(childComplexity int) int
		IsGuest               func(childComplexity int) int
		IsReferred            func(childComplexity int) int
		IsShadowBanned        func(childComplexity int) int
		IsSignup              func(childComplexity int) int
		LastReadFeedID        func(childComplexity int) int
		League                func(childComplexity int) int
		Links                 func(childComplexity int) int
		Name                  func(childComplexity int) int
		PhoneNumber           func(childComplexity int) int
		PreviousCountryRank   func(childComplexity int) int
		PreviousGlobalRank    func(childComplexity int) int
		ProfileImageURL       func(childComplexity int) int
		Rating                func(childComplexity int) int
		RatingV2              func(childComplexity int) int
		ReferralCode          func(childComplexity int) int
		ShadowBanStatus       func(childComplexity int) int
		StatikCoins           func(childComplexity int) int
		Stats                 func(childComplexity int) int
		SuspiciousActivity    func(childComplexity int) int
		Timezone              func(childComplexity int) int
		Token                 func(childComplexity int) int
		UserStreaks           func(childComplexity int) int
		Username              func(childComplexity int) int
	}

	UserAdditional struct {
		HasUnlockedAllGames func(childComplexity int) int
		TimeSpent           func(childComplexity int) int
	}

	UserAvgTime struct {
		PresetAvgTime   func(childComplexity int) int
		PresetBestTime  func(childComplexity int) int
		QuestionAvgTime func(childComplexity int) int
		UserID          func(childComplexity int) int
	}

	UserContestResult struct {
		CorrectSubmission    func(childComplexity int) int
		IncorrectSubmission  func(childComplexity int) int
		IsVirtualParticipant func(childComplexity int) int
		LastSubmissionTime   func(childComplexity int) int
		QuestionsSolved      func(childComplexity int) int
		Rank                 func(childComplexity int) int
		StartTime            func(childComplexity int) int
		TotalParticipants    func(childComplexity int) int
		TotalScore           func(childComplexity int) int
		User                 func(childComplexity int) int
	}

	UserContestSubmissions struct {
		CorrectSubmission   func(childComplexity int) int
		IncorrectSubmission func(childComplexity int) int
		LastSubmissionTime  func(childComplexity int) int
		StartTime           func(childComplexity int) int
		Submissions         func(childComplexity int) int
		TotalScore          func(childComplexity int) int
		User                func(childComplexity int) int
	}

	UserDailyChallengeResultWithStats struct {
		Error   func(childComplexity int) int
		Result  func(childComplexity int) int
		Success func(childComplexity int) int
	}

	UserDailyChallengeStats struct {
		AverageAccuracy func(childComplexity int) int
		AverageTime     func(childComplexity int) int
		BestTime        func(childComplexity int) int
		Division        func(childComplexity int) int
		ID              func(childComplexity int) int
		Streaks         func(childComplexity int) int
		TotalAttempts   func(childComplexity int) int
		TotalSubmission func(childComplexity int) int
		UserID          func(childComplexity int) int
	}

	UserDailyChallengeStreaks struct {
		Current        func(childComplexity int) int
		Highest        func(childComplexity int) int
		LastPlayedDate func(childComplexity int) int
	}

	UserDetailWithActivity struct {
		CurrActivity func(childComplexity int) int
		UserInfo     func(childComplexity int) int
	}

	UserDetailsForMessage struct {
		ID              func(childComplexity int) int
		Name            func(childComplexity int) int
		ProfileImageURL func(childComplexity int) int
		Rating          func(childComplexity int) int
		Username        func(childComplexity int) int
	}

	UserFeed struct {
		CreatedAt       func(childComplexity int) int
		ExpirationTime  func(childComplexity int) int
		FeedData        func(childComplexity int) int
		FeedReferenceID func(childComplexity int) int
		FeedType        func(childComplexity int) int
		ID              func(childComplexity int) int
		ImageURL        func(childComplexity int) int
		IsLiked         func(childComplexity int) int
		UserID          func(childComplexity int) int
	}

	UserGame struct {
		ID func(childComplexity int) int
		ST func(childComplexity int) int
	}

	UserLeaderboardPage struct {
		Edges      func(childComplexity int) int
		TotalCount func(childComplexity int) int
	}

	UserPreset struct {
		BestStreak              func(childComplexity int) int
		BestTime                func(childComplexity int) int
		CurAvgAccuracy          func(childComplexity int) int
		CurAvgTime              func(childComplexity int) int
		GlobalPresetID          func(childComplexity int) int
		ID                      func(childComplexity int) int
		Identifier              func(childComplexity int) int
		IncorrectSubmissions    func(childComplexity int) int
		Last10IncorrectAttempts func(childComplexity int) int
		Last10Time              func(childComplexity int) int
		Name                    func(childComplexity int) int
		NumOfCorrectSubmissions func(childComplexity int) int
		QuestionsSolved         func(childComplexity int) int
		Saved                   func(childComplexity int) int
		SavedConfig             func(childComplexity int) int
		UserID                  func(childComplexity int) int
	}

	UserPresetDayStats struct {
		Date            func(childComplexity int) int
		UserPresetStats func(childComplexity int) int
	}

	UserPresetStats struct {
		AvgAccuracy                func(childComplexity int) int
		AvgTime                    func(childComplexity int) int
		BestStreak                 func(childComplexity int) int
		Date                       func(childComplexity int) int
		GlobalPresetID             func(childComplexity int) int
		ID                         func(childComplexity int) int
		Identifier                 func(childComplexity int) int
		InaccuracyPerformanceTrend func(childComplexity int) int
		IncorrectSubmissions       func(childComplexity int) int
		NumOfCorrectSubmissions    func(childComplexity int) int
		QuestionsSolved            func(childComplexity int) int
		TimePerformanceTrend       func(childComplexity int) int
		UserID                     func(childComplexity int) int
		UserPresetID               func(childComplexity int) int
	}

	UserPresetStatsGraph struct {
		GlobalStats func(childComplexity int) int
		UserStats   func(childComplexity int) int
	}

	UserPresets struct {
		TotalCount  func(childComplexity int) int
		UserPresets func(childComplexity int) int
	}

	UserPublicDetails struct {
		Badge               func(childComplexity int) int
		CountryCode         func(childComplexity int) int
		CountryRank         func(childComplexity int) int
		GlobalRank          func(childComplexity int) int
		ID                  func(childComplexity int) int
		InstitutionID       func(childComplexity int) int
		IsGuest             func(childComplexity int) int
		Name                func(childComplexity int) int
		PreviousCountryRank func(childComplexity int) int
		PreviousGlobalRank  func(childComplexity int) int
		ProfileImageURL     func(childComplexity int) int
		Rating              func(childComplexity int) int
		RatingV2            func(childComplexity int) int
		Stats               func(childComplexity int) int
		UserStreaks         func(childComplexity int) int
		Username            func(childComplexity int) int
	}

	UserRating struct {
		AbilityDuelsRating func(childComplexity int) int
		FlashAnzanRating   func(childComplexity int) int
		GlobalRating       func(childComplexity int) int
		PuzzleRating       func(childComplexity int) int
	}

	UserRatingFixtureSubmission struct {
		CreatedAt      func(childComplexity int) int
		CurrentRating  func(childComplexity int) int
		ID             func(childComplexity int) int
		ProposedRating func(childComplexity int) int
		Submissions    func(childComplexity int) int
		TimeTaken      func(childComplexity int) int
		UpdatedAt      func(childComplexity int) int
		UserID         func(childComplexity int) int
		UserScore      func(childComplexity int) int
		UserStance     func(childComplexity int) int
	}

	UserResolution struct {
		CreatedAt func(childComplexity int) int
		Duration  func(childComplexity int) int
		ID        func(childComplexity int) int
		UserID    func(childComplexity int) int
	}

	UserResult struct {
		Error   func(childComplexity int) int
		Result  func(childComplexity int) int
		Success func(childComplexity int) int
	}

	UserSettings struct {
		HapticFeedback func(childComplexity int) int
		ID             func(childComplexity int) int
		PlaySound      func(childComplexity int) int
		UserID         func(childComplexity int) int
	}

	UserStats struct {
		FollowersCount  func(childComplexity int) int
		FollowingsCount func(childComplexity int) int
		FriendsCount    func(childComplexity int) int
		Games           func(childComplexity int) int
		Hr              func(childComplexity int) int
		Last10BotGames  func(childComplexity int) int
		Ngp             func(childComplexity int) int
	}

	UserStreaks struct {
		CurrentStreak  func(childComplexity int) int
		LastPlayedDate func(childComplexity int) int
		LastSevenDays  func(childComplexity int) int
		LongestStreak  func(childComplexity int) int
		StreakFreezers func(childComplexity int) int
		StreakHistory  func(childComplexity int) int
	}

	UsersWeeklyStatikCoinsOutput struct {
		DailyCoins func(childComplexity int) int
		TotalCoins func(childComplexity int) int
	}

	WeeklyLeagueLeaderboardEntry struct {
		ProgressState func(childComplexity int) int
		Rank          func(childComplexity int) int
		StatikCoins   func(childComplexity int) int
		User          func(childComplexity int) int
	}

	WeeklyLeagueLeaderboardPage struct {
		CurrentUserLeague func(childComplexity int) int
		HasMore           func(childComplexity int) int
		PageNumber        func(childComplexity int) int
		PageSize          func(childComplexity int) int
		Results           func(childComplexity int) int
		TotalResults      func(childComplexity int) int
	}

	ShowdownDetails struct {
		Format func(childComplexity int) int
		Rules  func(childComplexity int) int
	}
}

type executableSchema struct {
	schema     *ast.Schema
	resolvers  ResolverRoot
	directives DirectiveRoot
	complexity ComplexityRoot
}

func (e *executableSchema) Schema() *ast.Schema {
	if e.schema != nil {
		return e.schema
	}
	return parsedSchema
}

func (e *executableSchema) Complexity(ctx context.Context, typeName, field string, childComplexity int, rawArgs map[string]any) (int, bool) {
	ec := executionContext{nil, e, 0, 0, nil}
	_ = ec
	switch typeName + "." + field {

	case "ActivitySummaryEntry.activity":
		if e.complexity.ActivitySummaryEntry.Activity == nil {
			break
		}

		return e.complexity.ActivitySummaryEntry.Activity(childComplexity), true

	case "ActivitySummaryEntry.coins":
		if e.complexity.ActivitySummaryEntry.Coins == nil {
			break
		}

		return e.complexity.ActivitySummaryEntry.Coins(childComplexity), true

	case "AllPlayedPresetsOutput.count":
		if e.complexity.AllPlayedPresetsOutput.Count == nil {
			break
		}

		return e.complexity.AllPlayedPresetsOutput.Count(childComplexity), true

	case "AllPlayedPresetsOutput.presets":
		if e.complexity.AllPlayedPresetsOutput.Presets == nil {
			break
		}

		return e.complexity.AllPlayedPresetsOutput.Presets(childComplexity), true

	case "Announcement.ctas":
		if e.complexity.Announcement.CTAs == nil {
			break
		}

		return e.complexity.Announcement.CTAs(childComplexity), true

	case "Announcement.createdAt":
		if e.complexity.Announcement.CreatedAt == nil {
			break
		}

		return e.complexity.Announcement.CreatedAt(childComplexity), true

	case "Announcement.description":
		if e.complexity.Announcement.Description == nil {
			break
		}

		return e.complexity.Announcement.Description(childComplexity), true

	case "Announcement.expiresAt":
		if e.complexity.Announcement.ExpiresAt == nil {
			break
		}

		return e.complexity.Announcement.ExpiresAt(childComplexity), true

	case "Announcement.id":
		if e.complexity.Announcement.ID == nil {
			break
		}

		return e.complexity.Announcement.ID(childComplexity), true

	case "Announcement.imageUrl":
		if e.complexity.Announcement.ImageURL == nil {
			break
		}

		return e.complexity.Announcement.ImageURL(childComplexity), true

	case "Announcement.mediaUrl":
		if e.complexity.Announcement.MediaURL == nil {
			break
		}

		return e.complexity.Announcement.MediaURL(childComplexity), true

	case "Announcement.priority":
		if e.complexity.Announcement.Priority == nil {
			break
		}

		return e.complexity.Announcement.Priority(childComplexity), true

	case "Announcement.publishedAt":
		if e.complexity.Announcement.PublishedAt == nil {
			break
		}

		return e.complexity.Announcement.PublishedAt(childComplexity), true

	case "Announcement.riveAnimationUrl":
		if e.complexity.Announcement.RiveAnimationURL == nil {
			break
		}

		return e.complexity.Announcement.RiveAnimationURL(childComplexity), true

	case "Announcement.title":
		if e.complexity.Announcement.Title == nil {
			break
		}

		return e.complexity.Announcement.Title(childComplexity), true

	case "Announcement.type":
		if e.complexity.Announcement.Type == nil {
			break
		}

		return e.complexity.Announcement.Type(childComplexity), true

	case "AnnouncementMutationResponse.message":
		if e.complexity.AnnouncementMutationResponse.Message == nil {
			break
		}

		return e.complexity.AnnouncementMutationResponse.Message(childComplexity), true

	case "AnnouncementMutationResponse.success":
		if e.complexity.AnnouncementMutationResponse.Success == nil {
			break
		}

		return e.complexity.AnnouncementMutationResponse.Success(childComplexity), true

	case "Attachment.type":
		if e.complexity.Attachment.Type == nil {
			break
		}

		return e.complexity.Attachment.Type(childComplexity), true

	case "Attachment.url":
		if e.complexity.Attachment.URL == nil {
			break
		}

		return e.complexity.Attachment.URL(childComplexity), true

	case "AwardsAndAchievements.description":
		if e.complexity.AwardsAndAchievements.Description == nil {
			break
		}

		return e.complexity.AwardsAndAchievements.Description(childComplexity), true

	case "AwardsAndAchievements.imageUrl":
		if e.complexity.AwardsAndAchievements.ImageURL == nil {
			break
		}

		return e.complexity.AwardsAndAchievements.ImageURL(childComplexity), true

	case "AwardsAndAchievements.link":
		if e.complexity.AwardsAndAchievements.Link == nil {
			break
		}

		return e.complexity.AwardsAndAchievements.Link(childComplexity), true

	case "AwardsAndAchievements.title":
		if e.complexity.AwardsAndAchievements.Title == nil {
			break
		}

		return e.complexity.AwardsAndAchievements.Title(childComplexity), true

	case "BadgeAssignedEvent.initialBadge":
		if e.complexity.BadgeAssignedEvent.InitialBadge == nil {
			break
		}

		return e.complexity.BadgeAssignedEvent.InitialBadge(childComplexity), true

	case "BadgeAssignedEvent.newBadge":
		if e.complexity.BadgeAssignedEvent.NewBadge == nil {
			break
		}

		return e.complexity.BadgeAssignedEvent.NewBadge(childComplexity), true

	case "BotDetectionResult.challengeId":
		if e.complexity.BotDetectionResult.ChallengeID == nil {
			break
		}

		return e.complexity.BotDetectionResult.ChallengeID(childComplexity), true

	case "BotDetectionResult.isBotBehavior":
		if e.complexity.BotDetectionResult.IsBotBehavior == nil {
			break
		}

		return e.complexity.BotDetectionResult.IsBotBehavior(childComplexity), true

	case "BotDetectionResult.userId":
		if e.complexity.BotDetectionResult.UserID == nil {
			break
		}

		return e.complexity.BotDetectionResult.UserID(childComplexity), true

	case "CallToAction.actionType":
		if e.complexity.CallToAction.ActionType == nil {
			break
		}

		return e.complexity.CallToAction.ActionType(childComplexity), true

	case "CallToAction.style":
		if e.complexity.CallToAction.Style == nil {
			break
		}

		return e.complexity.CallToAction.Style(childComplexity), true

	case "CallToAction.target":
		if e.complexity.CallToAction.Target == nil {
			break
		}

		return e.complexity.CallToAction.Target(childComplexity), true

	case "CallToAction.text":
		if e.complexity.CallToAction.Text == nil {
			break
		}

		return e.complexity.CallToAction.Text(childComplexity), true

	case "Cell.isVisible":
		if e.complexity.Cell.IsVisible == nil {
			break
		}

		return e.complexity.Cell.IsVisible(childComplexity), true

	case "Cell.type":
		if e.complexity.Cell.Type == nil {
			break
		}

		return e.complexity.Cell.Type(childComplexity), true

	case "Cell.value":
		if e.complexity.Cell.Value == nil {
			break
		}

		return e.complexity.Cell.Value(childComplexity), true

	case "ChallengeOutput.challengedBy":
		if e.complexity.ChallengeOutput.ChallengedBy == nil {
			break
		}

		return e.complexity.ChallengeOutput.ChallengedBy(childComplexity), true

	case "ChallengeOutput.createdAt":
		if e.complexity.ChallengeOutput.CreatedAt == nil {
			break
		}

		return e.complexity.ChallengeOutput.CreatedAt(childComplexity), true

	case "ChallengeOutput.gameConfig":
		if e.complexity.ChallengeOutput.GameConfig == nil {
			break
		}

		return e.complexity.ChallengeOutput.GameConfig(childComplexity), true

	case "ChallengeOutput.gameId":
		if e.complexity.ChallengeOutput.GameID == nil {
			break
		}

		return e.complexity.ChallengeOutput.GameID(childComplexity), true

	case "ChallengeOutput.opponent":
		if e.complexity.ChallengeOutput.Opponent == nil {
			break
		}

		return e.complexity.ChallengeOutput.Opponent(childComplexity), true

	case "ChallengeOutput.status":
		if e.complexity.ChallengeOutput.Status == nil {
			break
		}

		return e.complexity.ChallengeOutput.Status(childComplexity), true

	case "ChallengeResult.rank":
		if e.complexity.ChallengeResult.Rank == nil {
			break
		}

		return e.complexity.ChallengeResult.Rank(childComplexity), true

	case "ChallengeResult.score":
		if e.complexity.ChallengeResult.Score == nil {
			break
		}

		return e.complexity.ChallengeResult.Score(childComplexity), true

	case "ChallengeResult.user":
		if e.complexity.ChallengeResult.User == nil {
			break
		}

		return e.complexity.ChallengeResult.User(childComplexity), true

	case "Club.bannerImage":
		if e.complexity.Club.BannerImage == nil {
			break
		}

		return e.complexity.Club.BannerImage(childComplexity), true

	case "Club.category":
		if e.complexity.Club.Category == nil {
			break
		}

		return e.complexity.Club.Category(childComplexity), true

	case "Club.chatRoomId":
		if e.complexity.Club.ChatRoomID == nil {
			break
		}

		return e.complexity.Club.ChatRoomID(childComplexity), true

	case "Club.clubEventsCount":
		if e.complexity.Club.ClubEventsCount == nil {
			break
		}

		return e.complexity.Club.ClubEventsCount(childComplexity), true

	case "Club.createdAt":
		if e.complexity.Club.CreatedAt == nil {
			break
		}

		return e.complexity.Club.CreatedAt(childComplexity), true

	case "Club.createdBy":
		if e.complexity.Club.CreatedBy == nil {
			break
		}

		return e.complexity.Club.CreatedBy(childComplexity), true

	case "Club.description":
		if e.complexity.Club.Description == nil {
			break
		}

		return e.complexity.Club.Description(childComplexity), true

	case "Club.forumId":
		if e.complexity.Club.ForumID == nil {
			break
		}

		return e.complexity.Club.ForumID(childComplexity), true

	case "Club.hasRequestedToJoin":
		if e.complexity.Club.HasRequestedToJoin == nil {
			break
		}

		return e.complexity.Club.HasRequestedToJoin(childComplexity), true

	case "Club.id":
		if e.complexity.Club.ID == nil {
			break
		}

		return e.complexity.Club.ID(childComplexity), true

	case "Club.isAdmin":
		if e.complexity.Club.IsAdmin == nil {
			break
		}

		return e.complexity.Club.IsAdmin(childComplexity), true

	case "Club.isClubMember":
		if e.complexity.Club.IsClubMember == nil {
			break
		}

		return e.complexity.Club.IsClubMember(childComplexity), true

	case "Club.logoImage":
		if e.complexity.Club.LogoImage == nil {
			break
		}

		return e.complexity.Club.LogoImage(childComplexity), true

	case "Club.membersCount":
		if e.complexity.Club.MembersCount == nil {
			break
		}

		return e.complexity.Club.MembersCount(childComplexity), true

	case "Club.name":
		if e.complexity.Club.Name == nil {
			break
		}

		return e.complexity.Club.Name(childComplexity), true

	case "Club.updatedAt":
		if e.complexity.Club.UpdatedAt == nil {
			break
		}

		return e.complexity.Club.UpdatedAt(childComplexity), true

	case "Club.visibility":
		if e.complexity.Club.Visibility == nil {
			break
		}

		return e.complexity.Club.Visibility(childComplexity), true

	case "ClubAnnouncement.clubId":
		if e.complexity.ClubAnnouncement.ClubID == nil {
			break
		}

		return e.complexity.ClubAnnouncement.ClubID(childComplexity), true

	case "ClubAnnouncement.content":
		if e.complexity.ClubAnnouncement.Content == nil {
			break
		}

		return e.complexity.ClubAnnouncement.Content(childComplexity), true

	case "ClubAnnouncement.createdAt":
		if e.complexity.ClubAnnouncement.CreatedAt == nil {
			break
		}

		return e.complexity.ClubAnnouncement.CreatedAt(childComplexity), true

	case "ClubAnnouncement.createdBy":
		if e.complexity.ClubAnnouncement.CreatedBy == nil {
			break
		}

		return e.complexity.ClubAnnouncement.CreatedBy(childComplexity), true

	case "ClubAnnouncement.creatorInfo":
		if e.complexity.ClubAnnouncement.CreatorInfo == nil {
			break
		}

		return e.complexity.ClubAnnouncement.CreatorInfo(childComplexity), true

	case "ClubAnnouncement.id":
		if e.complexity.ClubAnnouncement.ID == nil {
			break
		}

		return e.complexity.ClubAnnouncement.ID(childComplexity), true

	case "ClubAnnouncement.title":
		if e.complexity.ClubAnnouncement.Title == nil {
			break
		}

		return e.complexity.ClubAnnouncement.Title(childComplexity), true

	case "ClubAnnouncementCreatorInfo.profileImageUrl":
		if e.complexity.ClubAnnouncementCreatorInfo.ProfileImageURL == nil {
			break
		}

		return e.complexity.ClubAnnouncementCreatorInfo.ProfileImageURL(childComplexity), true

	case "ClubAnnouncementCreatorInfo.username":
		if e.complexity.ClubAnnouncementCreatorInfo.Username == nil {
			break
		}

		return e.complexity.ClubAnnouncementCreatorInfo.Username(childComplexity), true

	case "ClubAnnouncementsPage.hasMore":
		if e.complexity.ClubAnnouncementsPage.HasMore == nil {
			break
		}

		return e.complexity.ClubAnnouncementsPage.HasMore(childComplexity), true

	case "ClubAnnouncementsPage.pageNumber":
		if e.complexity.ClubAnnouncementsPage.PageNumber == nil {
			break
		}

		return e.complexity.ClubAnnouncementsPage.PageNumber(childComplexity), true

	case "ClubAnnouncementsPage.pageSize":
		if e.complexity.ClubAnnouncementsPage.PageSize == nil {
			break
		}

		return e.complexity.ClubAnnouncementsPage.PageSize(childComplexity), true

	case "ClubAnnouncementsPage.results":
		if e.complexity.ClubAnnouncementsPage.Results == nil {
			break
		}

		return e.complexity.ClubAnnouncementsPage.Results(childComplexity), true

	case "ClubAnnouncementsPage.totalResults":
		if e.complexity.ClubAnnouncementsPage.TotalResults == nil {
			break
		}

		return e.complexity.ClubAnnouncementsPage.TotalResults(childComplexity), true

	case "ClubEvent.clubEventPlayId":
		if e.complexity.ClubEvent.ClubEventPlayID == nil {
			break
		}

		return e.complexity.ClubEvent.ClubEventPlayID(childComplexity), true

	case "ClubEvent.clubEventType":
		if e.complexity.ClubEvent.ClubEventType == nil {
			break
		}

		return e.complexity.ClubEvent.ClubEventType(childComplexity), true

	case "ClubEvent.clubId":
		if e.complexity.ClubEvent.ClubID == nil {
			break
		}

		return e.complexity.ClubEvent.ClubID(childComplexity), true

	case "ClubEvent.createdAt":
		if e.complexity.ClubEvent.CreatedAt == nil {
			break
		}

		return e.complexity.ClubEvent.CreatedAt(childComplexity), true

	case "ClubEvent.createdBy":
		if e.complexity.ClubEvent.CreatedBy == nil {
			break
		}

		return e.complexity.ClubEvent.CreatedBy(childComplexity), true

	case "ClubEvent.description":
		if e.complexity.ClubEvent.Description == nil {
			break
		}

		return e.complexity.ClubEvent.Description(childComplexity), true

	case "ClubEvent.gameConfig":
		if e.complexity.ClubEvent.GameConfig == nil {
			break
		}

		return e.complexity.ClubEvent.GameConfig(childComplexity), true

	case "ClubEvent.id":
		if e.complexity.ClubEvent.ID == nil {
			break
		}

		return e.complexity.ClubEvent.ID(childComplexity), true

	case "ClubEvent.openToAll":
		if e.complexity.ClubEvent.OpenToAll == nil {
			break
		}

		return e.complexity.ClubEvent.OpenToAll(childComplexity), true

	case "ClubEvent.participationCount":
		if e.complexity.ClubEvent.ParticipationCount == nil {
			break
		}

		return e.complexity.ClubEvent.ParticipationCount(childComplexity), true

	case "ClubEvent.playerSetting":
		if e.complexity.ClubEvent.PlayerSetting == nil {
			break
		}

		return e.complexity.ClubEvent.PlayerSetting(childComplexity), true

	case "ClubEvent.ratedEvent":
		if e.complexity.ClubEvent.RatedEvent == nil {
			break
		}

		return e.complexity.ClubEvent.RatedEvent(childComplexity), true

	case "ClubEvent.startTime":
		if e.complexity.ClubEvent.StartTime == nil {
			break
		}

		return e.complexity.ClubEvent.StartTime(childComplexity), true

	case "ClubEvent.title":
		if e.complexity.ClubEvent.Title == nil {
			break
		}

		return e.complexity.ClubEvent.Title(childComplexity), true

	case "ClubEvent.updatedAt":
		if e.complexity.ClubEvent.UpdatedAt == nil {
			break
		}

		return e.complexity.ClubEvent.UpdatedAt(childComplexity), true

	case "ClubEvent.visibility":
		if e.complexity.ClubEvent.Visibility == nil {
			break
		}

		return e.complexity.ClubEvent.Visibility(childComplexity), true

	case "ClubEventParticipant.clubEventId":
		if e.complexity.ClubEventParticipant.ClubEventID == nil {
			break
		}

		return e.complexity.ClubEventParticipant.ClubEventID(childComplexity), true

	case "ClubEventParticipant.id":
		if e.complexity.ClubEventParticipant.ID == nil {
			break
		}

		return e.complexity.ClubEventParticipant.ID(childComplexity), true

	case "ClubEventParticipant.joinedAt":
		if e.complexity.ClubEventParticipant.JoinedAt == nil {
			break
		}

		return e.complexity.ClubEventParticipant.JoinedAt(childComplexity), true

	case "ClubEventParticipant.userId":
		if e.complexity.ClubEventParticipant.UserID == nil {
			break
		}

		return e.complexity.ClubEventParticipant.UserID(childComplexity), true

	case "ClubEventsPage.hasMore":
		if e.complexity.ClubEventsPage.HasMore == nil {
			break
		}

		return e.complexity.ClubEventsPage.HasMore(childComplexity), true

	case "ClubEventsPage.pageNumber":
		if e.complexity.ClubEventsPage.PageNumber == nil {
			break
		}

		return e.complexity.ClubEventsPage.PageNumber(childComplexity), true

	case "ClubEventsPage.pageSize":
		if e.complexity.ClubEventsPage.PageSize == nil {
			break
		}

		return e.complexity.ClubEventsPage.PageSize(childComplexity), true

	case "ClubEventsPage.results":
		if e.complexity.ClubEventsPage.Results == nil {
			break
		}

		return e.complexity.ClubEventsPage.Results(childComplexity), true

	case "ClubEventsPage.totalResults":
		if e.complexity.ClubEventsPage.TotalResults == nil {
			break
		}

		return e.complexity.ClubEventsPage.TotalResults(childComplexity), true

	case "ClubLeaderboard.hasMore":
		if e.complexity.ClubLeaderboard.HasMore == nil {
			break
		}

		return e.complexity.ClubLeaderboard.HasMore(childComplexity), true

	case "ClubLeaderboard.pageNumber":
		if e.complexity.ClubLeaderboard.PageNumber == nil {
			break
		}

		return e.complexity.ClubLeaderboard.PageNumber(childComplexity), true

	case "ClubLeaderboard.pageSize":
		if e.complexity.ClubLeaderboard.PageSize == nil {
			break
		}

		return e.complexity.ClubLeaderboard.PageSize(childComplexity), true

	case "ClubLeaderboard.results":
		if e.complexity.ClubLeaderboard.Results == nil {
			break
		}

		return e.complexity.ClubLeaderboard.Results(childComplexity), true

	case "ClubLeaderboard.totalResults":
		if e.complexity.ClubLeaderboard.TotalResults == nil {
			break
		}

		return e.complexity.ClubLeaderboard.TotalResults(childComplexity), true

	case "ClubLeaderboardEntry.rank":
		if e.complexity.ClubLeaderboardEntry.Rank == nil {
			break
		}

		return e.complexity.ClubLeaderboardEntry.Rank(childComplexity), true

	case "ClubLeaderboardEntry.user":
		if e.complexity.ClubLeaderboardEntry.User == nil {
			break
		}

		return e.complexity.ClubLeaderboardEntry.User(childComplexity), true

	case "ClubMember.clubId":
		if e.complexity.ClubMember.ClubID == nil {
			break
		}

		return e.complexity.ClubMember.ClubID(childComplexity), true

	case "ClubMember.clubMembershipStatus":
		if e.complexity.ClubMember.ClubMembershipStatus == nil {
			break
		}

		return e.complexity.ClubMember.ClubMembershipStatus(childComplexity), true

	case "ClubMember.id":
		if e.complexity.ClubMember.ID == nil {
			break
		}

		return e.complexity.ClubMember.ID(childComplexity), true

	case "ClubMember.joinedAt":
		if e.complexity.ClubMember.JoinedAt == nil {
			break
		}

		return e.complexity.ClubMember.JoinedAt(childComplexity), true

	case "ClubMember.memberInfo":
		if e.complexity.ClubMember.MemberInfo == nil {
			break
		}

		return e.complexity.ClubMember.MemberInfo(childComplexity), true

	case "ClubMember.role":
		if e.complexity.ClubMember.Role == nil {
			break
		}

		return e.complexity.ClubMember.Role(childComplexity), true

	case "ClubMember.userId":
		if e.complexity.ClubMember.UserID == nil {
			break
		}

		return e.complexity.ClubMember.UserID(childComplexity), true

	case "ClubMembersPage.hasMore":
		if e.complexity.ClubMembersPage.HasMore == nil {
			break
		}

		return e.complexity.ClubMembersPage.HasMore(childComplexity), true

	case "ClubMembersPage.pageNumber":
		if e.complexity.ClubMembersPage.PageNumber == nil {
			break
		}

		return e.complexity.ClubMembersPage.PageNumber(childComplexity), true

	case "ClubMembersPage.pageSize":
		if e.complexity.ClubMembersPage.PageSize == nil {
			break
		}

		return e.complexity.ClubMembersPage.PageSize(childComplexity), true

	case "ClubMembersPage.results":
		if e.complexity.ClubMembersPage.Results == nil {
			break
		}

		return e.complexity.ClubMembersPage.Results(childComplexity), true

	case "ClubMembersPage.totalResults":
		if e.complexity.ClubMembersPage.TotalResults == nil {
			break
		}

		return e.complexity.ClubMembersPage.TotalResults(childComplexity), true

	case "ClubsPage.hasMore":
		if e.complexity.ClubsPage.HasMore == nil {
			break
		}

		return e.complexity.ClubsPage.HasMore(childComplexity), true

	case "ClubsPage.pageNumber":
		if e.complexity.ClubsPage.PageNumber == nil {
			break
		}

		return e.complexity.ClubsPage.PageNumber(childComplexity), true

	case "ClubsPage.pageSize":
		if e.complexity.ClubsPage.PageSize == nil {
			break
		}

		return e.complexity.ClubsPage.PageSize(childComplexity), true

	case "ClubsPage.results":
		if e.complexity.ClubsPage.Results == nil {
			break
		}

		return e.complexity.ClubsPage.Results(childComplexity), true

	case "ClubsPage.totalResults":
		if e.complexity.ClubsPage.TotalResults == nil {
			break
		}

		return e.complexity.ClubsPage.TotalResults(childComplexity), true

	case "ConnectionRequest.sentBy":
		if e.complexity.ConnectionRequest.SentBy == nil {
			break
		}

		return e.complexity.ConnectionRequest.SentBy(childComplexity), true

	case "Contest.clubId":
		if e.complexity.Contest.ClubID == nil {
			break
		}

		return e.complexity.Contest.ClubID(childComplexity), true

	case "Contest.contestDuration":
		if e.complexity.Contest.ContestDuration == nil {
			break
		}

		return e.complexity.Contest.ContestDuration(childComplexity), true

	case "Contest.currentUserParticipation":
		if e.complexity.Contest.CurrentUserParticipation == nil {
			break
		}

		return e.complexity.Contest.CurrentUserParticipation(childComplexity), true

	case "Contest.description":
		if e.complexity.Contest.Description == nil {
			break
		}

		return e.complexity.Contest.Description(childComplexity), true

	case "Contest.details":
		if e.complexity.Contest.Details == nil {
			break
		}

		return e.complexity.Contest.Details(childComplexity), true

	case "Contest.encryptedQuestions":
		if e.complexity.Contest.EncryptedQuestions == nil {
			break
		}

		return e.complexity.Contest.EncryptedQuestions(childComplexity), true

	case "Contest.endTime":
		if e.complexity.Contest.EndTime == nil {
			break
		}

		return e.complexity.Contest.EndTime(childComplexity), true

	case "Contest.hostedBy":
		if e.complexity.Contest.HostedBy == nil {
			break
		}

		return e.complexity.Contest.HostedBy(childComplexity), true

	case "Contest.hostedByV2":
		if e.complexity.Contest.HostedByV2 == nil {
			break
		}

		return e.complexity.Contest.HostedByV2(childComplexity), true

	case "Contest._id":
		if e.complexity.Contest.ID == nil {
			break
		}

		return e.complexity.Contest.ID(childComplexity), true

	case "Contest.name":
		if e.complexity.Contest.Name == nil {
			break
		}

		return e.complexity.Contest.Name(childComplexity), true

	case "Contest.recentParticipants":
		if e.complexity.Contest.RecentParticipants == nil {
			break
		}

		return e.complexity.Contest.RecentParticipants(childComplexity), true

	case "Contest.registrationCount":
		if e.complexity.Contest.RegistrationCount == nil {
			break
		}

		return e.complexity.Contest.RegistrationCount(childComplexity), true

	case "Contest.registrationEndTime":
		if e.complexity.Contest.RegistrationEndTime == nil {
			break
		}

		return e.complexity.Contest.RegistrationEndTime(childComplexity), true

	case "Contest.registrationForm":
		if e.complexity.Contest.RegistrationForm == nil {
			break
		}

		return e.complexity.Contest.RegistrationForm(childComplexity), true

	case "Contest.registrationStartTime":
		if e.complexity.Contest.RegistrationStartTime == nil {
			break
		}

		return e.complexity.Contest.RegistrationStartTime(childComplexity), true

	case "Contest.startTime":
		if e.complexity.Contest.StartTime == nil {
			break
		}

		return e.complexity.Contest.StartTime(childComplexity), true

	case "Contest.status":
		if e.complexity.Contest.Status == nil {
			break
		}

		return e.complexity.Contest.Status(childComplexity), true

	case "ContestDetails.about":
		if e.complexity.ContestDetails.About == nil {
			break
		}

		return e.complexity.ContestDetails.About(childComplexity), true

	case "ContestDetails.instructions":
		if e.complexity.ContestDetails.Instructions == nil {
			break
		}

		return e.complexity.ContestDetails.Instructions(childComplexity), true

	case "ContestDetails.requirements":
		if e.complexity.ContestDetails.Requirements == nil {
			break
		}

		return e.complexity.ContestDetails.Requirements(childComplexity), true

	case "ContestLeaderboard.participants":
		if e.complexity.ContestLeaderboard.Participants == nil {
			break
		}

		return e.complexity.ContestLeaderboard.Participants(childComplexity), true

	case "ContestLeaderboard.totalParticipants":
		if e.complexity.ContestLeaderboard.TotalParticipants == nil {
			break
		}

		return e.complexity.ContestLeaderboard.TotalParticipants(childComplexity), true

	case "ContestParticipant.correctSubmission":
		if e.complexity.ContestParticipant.CorrectSubmission == nil {
			break
		}

		return e.complexity.ContestParticipant.CorrectSubmission(childComplexity), true

	case "ContestParticipant.incorrectSubmission":
		if e.complexity.ContestParticipant.IncorrectSubmission == nil {
			break
		}

		return e.complexity.ContestParticipant.IncorrectSubmission(childComplexity), true

	case "ContestParticipant.isVirtualParticipant":
		if e.complexity.ContestParticipant.IsVirtualParticipant == nil {
			break
		}

		return e.complexity.ContestParticipant.IsVirtualParticipant(childComplexity), true

	case "ContestParticipant.lastSubmissionTime":
		if e.complexity.ContestParticipant.LastSubmissionTime == nil {
			break
		}

		return e.complexity.ContestParticipant.LastSubmissionTime(childComplexity), true

	case "ContestParticipant.rank":
		if e.complexity.ContestParticipant.Rank == nil {
			break
		}

		return e.complexity.ContestParticipant.Rank(childComplexity), true

	case "ContestParticipant.registrationData":
		if e.complexity.ContestParticipant.RegistrationData == nil {
			break
		}

		return e.complexity.ContestParticipant.RegistrationData(childComplexity), true

	case "ContestParticipant.score":
		if e.complexity.ContestParticipant.Score == nil {
			break
		}

		return e.complexity.ContestParticipant.Score(childComplexity), true

	case "ContestParticipant.startTime":
		if e.complexity.ContestParticipant.StartTime == nil {
			break
		}

		return e.complexity.ContestParticipant.StartTime(childComplexity), true

	case "ContestParticipant.user":
		if e.complexity.ContestParticipant.User == nil {
			break
		}

		return e.complexity.ContestParticipant.User(childComplexity), true

	case "ContestQuestion._id":
		if e.complexity.ContestQuestion.ID == nil {
			break
		}

		return e.complexity.ContestQuestion.ID(childComplexity), true

	case "ContestQuestion.points":
		if e.complexity.ContestQuestion.Points == nil {
			break
		}

		return e.complexity.ContestQuestion.Points(childComplexity), true

	case "ContestQuestion.question":
		if e.complexity.ContestQuestion.Question == nil {
			break
		}

		return e.complexity.ContestQuestion.Question(childComplexity), true

	case "ContestSubmission.answer":
		if e.complexity.ContestSubmission.Answer == nil {
			break
		}

		return e.complexity.ContestSubmission.Answer(childComplexity), true

	case "ContestSubmission.isCorrect":
		if e.complexity.ContestSubmission.IsCorrect == nil {
			break
		}

		return e.complexity.ContestSubmission.IsCorrect(childComplexity), true

	case "ContestSubmission.points":
		if e.complexity.ContestSubmission.Points == nil {
			break
		}

		return e.complexity.ContestSubmission.Points(childComplexity), true

	case "ContestSubmission.questionId":
		if e.complexity.ContestSubmission.QuestionID == nil {
			break
		}

		return e.complexity.ContestSubmission.QuestionID(childComplexity), true

	case "ContestSubmission.submissionTime":
		if e.complexity.ContestSubmission.SubmissionTime == nil {
			break
		}

		return e.complexity.ContestSubmission.SubmissionTime(childComplexity), true

	case "CreateMessageInput.attachment":
		if e.complexity.CreateMessageInput.Attachment == nil {
			break
		}

		return e.complexity.CreateMessageInput.Attachment(childComplexity), true

	case "CreateMessageInput.content":
		if e.complexity.CreateMessageInput.Content == nil {
			break
		}

		return e.complexity.CreateMessageInput.Content(childComplexity), true

	case "CreateMessageInput.groupId":
		if e.complexity.CreateMessageInput.GroupID == nil {
			break
		}

		return e.complexity.CreateMessageInput.GroupID(childComplexity), true

	case "CreateMessageInput.sender":
		if e.complexity.CreateMessageInput.Sender == nil {
			break
		}

		return e.complexity.CreateMessageInput.Sender(childComplexity), true

	case "CreatorInfo.profileImageUrl":
		if e.complexity.CreatorInfo.ProfileImageURL == nil {
			break
		}

		return e.complexity.CreatorInfo.ProfileImageURL(childComplexity), true

	case "CreatorInfo.rating":
		if e.complexity.CreatorInfo.Rating == nil {
			break
		}

		return e.complexity.CreatorInfo.Rating(childComplexity), true

	case "CreatorInfo.username":
		if e.complexity.CreatorInfo.Username == nil {
			break
		}

		return e.complexity.CreatorInfo.Username(childComplexity), true

	case "CrossMathPuzzle.puzzleString":
		if e.complexity.CrossMathPuzzle.PuzzleString == nil {
			break
		}

		return e.complexity.CrossMathPuzzle.PuzzleString(childComplexity), true

	case "CrossMathPuzzleRush.bestAllTime":
		if e.complexity.CrossMathPuzzleRush.BestAllTime == nil {
			break
		}

		return e.complexity.CrossMathPuzzleRush.BestAllTime(childComplexity), true

	case "CrossMathPuzzleRush.createdAt":
		if e.complexity.CrossMathPuzzleRush.CreatedAt == nil {
			break
		}

		return e.complexity.CrossMathPuzzleRush.CreatedAt(childComplexity), true

	case "CrossMathPuzzleRush._id":
		if e.complexity.CrossMathPuzzleRush.ID == nil {
			break
		}

		return e.complexity.CrossMathPuzzleRush.ID(childComplexity), true

	case "CrossMathPuzzleRush.isNewBestScore":
		if e.complexity.CrossMathPuzzleRush.IsNewBestScore == nil {
			break
		}

		return e.complexity.CrossMathPuzzleRush.IsNewBestScore(childComplexity), true

	case "CrossMathPuzzleRush.updatedAt":
		if e.complexity.CrossMathPuzzleRush.UpdatedAt == nil {
			break
		}

		return e.complexity.CrossMathPuzzleRush.UpdatedAt(childComplexity), true

	case "CrossMathPuzzleRush.userId":
		if e.complexity.CrossMathPuzzleRush.UserID == nil {
			break
		}

		return e.complexity.CrossMathPuzzleRush.UserID(childComplexity), true

	case "CrossMathPuzzleRushPlayerInfo.rank":
		if e.complexity.CrossMathPuzzleRushPlayerInfo.Rank == nil {
			break
		}

		return e.complexity.CrossMathPuzzleRushPlayerInfo.Rank(childComplexity), true

	case "CrossMathPuzzleRushPlayerInfo.score":
		if e.complexity.CrossMathPuzzleRushPlayerInfo.Score == nil {
			break
		}

		return e.complexity.CrossMathPuzzleRushPlayerInfo.Score(childComplexity), true

	case "CrossMathPuzzleRushPlayerInfo.userInfo":
		if e.complexity.CrossMathPuzzleRushPlayerInfo.UserInfo == nil {
			break
		}

		return e.complexity.CrossMathPuzzleRushPlayerInfo.UserInfo(childComplexity), true

	case "CrossMathPuzzleRushStats.bestAllTime":
		if e.complexity.CrossMathPuzzleRushStats.BestAllTime == nil {
			break
		}

		return e.complexity.CrossMathPuzzleRushStats.BestAllTime(childComplexity), true

	case "CrossMathPuzzleRushStats.friendsRank":
		if e.complexity.CrossMathPuzzleRushStats.FriendsRank == nil {
			break
		}

		return e.complexity.CrossMathPuzzleRushStats.FriendsRank(childComplexity), true

	case "CrossMathPuzzleRushStats.globalRank":
		if e.complexity.CrossMathPuzzleRushStats.GlobalRank == nil {
			break
		}

		return e.complexity.CrossMathPuzzleRushStats.GlobalRank(childComplexity), true

	case "CurrentShowdonParticipant.currentGame":
		if e.complexity.CurrentShowdonParticipant.CurrentGame == nil {
			break
		}

		return e.complexity.CurrentShowdonParticipant.CurrentGame(childComplexity), true

	case "CurrentShowdonParticipant.currentRound":
		if e.complexity.CurrentShowdonParticipant.CurrentRound == nil {
			break
		}

		return e.complexity.CurrentShowdonParticipant.CurrentRound(childComplexity), true

	case "CurrentShowdonParticipant.hadABye":
		if e.complexity.CurrentShowdonParticipant.HadABye == nil {
			break
		}

		return e.complexity.CurrentShowdonParticipant.HadABye(childComplexity), true

	case "CurrentShowdonParticipant.lastSubmissionTime":
		if e.complexity.CurrentShowdonParticipant.LastSubmissionTime == nil {
			break
		}

		return e.complexity.CurrentShowdonParticipant.LastSubmissionTime(childComplexity), true

	case "CurrentShowdonParticipant.rank":
		if e.complexity.CurrentShowdonParticipant.Rank == nil {
			break
		}

		return e.complexity.CurrentShowdonParticipant.Rank(childComplexity), true

	case "CurrentShowdonParticipant.recentOpponent":
		if e.complexity.CurrentShowdonParticipant.RecentOpponent == nil {
			break
		}

		return e.complexity.CurrentShowdonParticipant.RecentOpponent(childComplexity), true

	case "CurrentShowdonParticipant.registrationData":
		if e.complexity.CurrentShowdonParticipant.RegistrationData == nil {
			break
		}

		return e.complexity.CurrentShowdonParticipant.RegistrationData(childComplexity), true

	case "CurrentShowdonParticipant.rounds":
		if e.complexity.CurrentShowdonParticipant.Rounds == nil {
			break
		}

		return e.complexity.CurrentShowdonParticipant.Rounds(childComplexity), true

	case "CurrentShowdonParticipant.showdownId":
		if e.complexity.CurrentShowdonParticipant.ShowdownID == nil {
			break
		}

		return e.complexity.CurrentShowdonParticipant.ShowdownID(childComplexity), true

	case "CurrentShowdonParticipant.stats":
		if e.complexity.CurrentShowdonParticipant.Stats == nil {
			break
		}

		return e.complexity.CurrentShowdonParticipant.Stats(childComplexity), true

	case "CurrentShowdonParticipant.totalScore":
		if e.complexity.CurrentShowdonParticipant.TotalScore == nil {
			break
		}

		return e.complexity.CurrentShowdonParticipant.TotalScore(childComplexity), true

	case "CurrentShowdonParticipant.userId":
		if e.complexity.CurrentShowdonParticipant.UserID == nil {
			break
		}

		return e.complexity.CurrentShowdonParticipant.UserID(childComplexity), true

	case "CurrentUserParticipation.contestId":
		if e.complexity.CurrentUserParticipation.ContestID == nil {
			break
		}

		return e.complexity.CurrentUserParticipation.ContestID(childComplexity), true

	case "CurrentUserParticipation.lastSubmissionTime":
		if e.complexity.CurrentUserParticipation.LastSubmissionTime == nil {
			break
		}

		return e.complexity.CurrentUserParticipation.LastSubmissionTime(childComplexity), true

	case "CurrentUserParticipation.registrationData":
		if e.complexity.CurrentUserParticipation.RegistrationData == nil {
			break
		}

		return e.complexity.CurrentUserParticipation.RegistrationData(childComplexity), true

	case "CurrentUserParticipation.score":
		if e.complexity.CurrentUserParticipation.Score == nil {
			break
		}

		return e.complexity.CurrentUserParticipation.Score(childComplexity), true

	case "CurrentUserParticipation.userId":
		if e.complexity.CurrentUserParticipation.UserID == nil {
			break
		}

		return e.complexity.CurrentUserParticipation.UserID(childComplexity), true

	case "DailyChallenge.challengeNumber":
		if e.complexity.DailyChallenge.ChallengeNumber == nil {
			break
		}

		return e.complexity.DailyChallenge.ChallengeNumber(childComplexity), true

	case "DailyChallenge.challengeStatus":
		if e.complexity.DailyChallenge.ChallengeStatus == nil {
			break
		}

		return e.complexity.DailyChallenge.ChallengeStatus(childComplexity), true

	case "DailyChallenge.division":
		if e.complexity.DailyChallenge.Division == nil {
			break
		}

		return e.complexity.DailyChallenge.Division(childComplexity), true

	case "DailyChallenge.encryptedQuestions":
		if e.complexity.DailyChallenge.EncryptedQuestions == nil {
			break
		}

		return e.complexity.DailyChallenge.EncryptedQuestions(childComplexity), true

	case "DailyChallenge.endTime":
		if e.complexity.DailyChallenge.EndTime == nil {
			break
		}

		return e.complexity.DailyChallenge.EndTime(childComplexity), true

	case "DailyChallenge.hasAttempted":
		if e.complexity.DailyChallenge.HasAttempted == nil {
			break
		}

		return e.complexity.DailyChallenge.HasAttempted(childComplexity), true

	case "DailyChallenge._id":
		if e.complexity.DailyChallenge.ID == nil {
			break
		}

		return e.complexity.DailyChallenge.ID(childComplexity), true

	case "DailyChallenge.questions":
		if e.complexity.DailyChallenge.Questions == nil {
			break
		}

		return e.complexity.DailyChallenge.Questions(childComplexity), true

	case "DailyChallenge.startTime":
		if e.complexity.DailyChallenge.StartTime == nil {
			break
		}

		return e.complexity.DailyChallenge.StartTime(childComplexity), true

	case "DailyChallenge.stats":
		if e.complexity.DailyChallenge.Stats == nil {
			break
		}

		return e.complexity.DailyChallenge.Stats(childComplexity), true

	case "DailyChallengeResult.challengeId":
		if e.complexity.DailyChallengeResult.ChallengeID == nil {
			break
		}

		return e.complexity.DailyChallengeResult.ChallengeID(childComplexity), true

	case "DailyChallengeResult.completedAt":
		if e.complexity.DailyChallengeResult.CompletedAt == nil {
			break
		}

		return e.complexity.DailyChallengeResult.CompletedAt(childComplexity), true

	case "DailyChallengeResult.rank":
		if e.complexity.DailyChallengeResult.Rank == nil {
			break
		}

		return e.complexity.DailyChallengeResult.Rank(childComplexity), true

	case "DailyChallengeResult.resultStatus":
		if e.complexity.DailyChallengeResult.ResultStatus == nil {
			break
		}

		return e.complexity.DailyChallengeResult.ResultStatus(childComplexity), true

	case "DailyChallengeResult.score":
		if e.complexity.DailyChallengeResult.Score == nil {
			break
		}

		return e.complexity.DailyChallengeResult.Score(childComplexity), true

	case "DailyChallengeResult.statikCoinsEarned":
		if e.complexity.DailyChallengeResult.StatikCoinsEarned == nil {
			break
		}

		return e.complexity.DailyChallengeResult.StatikCoinsEarned(childComplexity), true

	case "DailyChallengeResult.userId":
		if e.complexity.DailyChallengeResult.UserID == nil {
			break
		}

		return e.complexity.DailyChallengeResult.UserID(childComplexity), true

	case "DailyChallengeResultWithStats.result":
		if e.complexity.DailyChallengeResultWithStats.Result == nil {
			break
		}

		return e.complexity.DailyChallengeResultWithStats.Result(childComplexity), true

	case "DailyChallengeResultWithStats.stats":
		if e.complexity.DailyChallengeResultWithStats.Stats == nil {
			break
		}

		return e.complexity.DailyChallengeResultWithStats.Stats(childComplexity), true

	case "DailyChallengeStat.averageAccuracy":
		if e.complexity.DailyChallengeStat.AverageAccuracy == nil {
			break
		}

		return e.complexity.DailyChallengeStat.AverageAccuracy(childComplexity), true

	case "DailyChallengeStat.averageTime":
		if e.complexity.DailyChallengeStat.AverageTime == nil {
			break
		}

		return e.complexity.DailyChallengeStat.AverageTime(childComplexity), true

	case "DailyChallengeStat.bestTime":
		if e.complexity.DailyChallengeStat.BestTime == nil {
			break
		}

		return e.complexity.DailyChallengeStat.BestTime(childComplexity), true

	case "DailyChallengeStat.totalAttempts":
		if e.complexity.DailyChallengeStat.TotalAttempts == nil {
			break
		}

		return e.complexity.DailyChallengeStat.TotalAttempts(childComplexity), true

	case "DailyChallengeStat.totalSubmission":
		if e.complexity.DailyChallengeStat.TotalSubmission == nil {
			break
		}

		return e.complexity.DailyChallengeStat.TotalSubmission(childComplexity), true

	case "DefaultGameConfig.timeLimit":
		if e.complexity.DefaultGameConfig.TimeLimit == nil {
			break
		}

		return e.complexity.DefaultGameConfig.TimeLimit(childComplexity), true

	case "DefaultGameModeConfig.numPlayers":
		if e.complexity.DefaultGameModeConfig.NumPlayers == nil {
			break
		}

		return e.complexity.DefaultGameModeConfig.NumPlayers(childComplexity), true

	case "DeviceTokenRegistrationResponse.message":
		if e.complexity.DeviceTokenRegistrationResponse.Message == nil {
			break
		}

		return e.complexity.DeviceTokenRegistrationResponse.Message(childComplexity), true

	case "DeviceTokenRegistrationResponse.success":
		if e.complexity.DeviceTokenRegistrationResponse.Success == nil {
			break
		}

		return e.complexity.DeviceTokenRegistrationResponse.Success(childComplexity), true

	case "FeedAdditionalInfo.connectionRequest":
		if e.complexity.FeedAdditionalInfo.ConnectionRequest == nil {
			break
		}

		return e.complexity.FeedAdditionalInfo.ConnectionRequest(childComplexity), true

	case "FeedData.additionalInfo":
		if e.complexity.FeedData.AdditionalInfo == nil {
			break
		}

		return e.complexity.FeedData.AdditionalInfo(childComplexity), true

	case "FeedData.createdAt":
		if e.complexity.FeedData.CreatedAt == nil {
			break
		}

		return e.complexity.FeedData.CreatedAt(childComplexity), true

	case "FeedData.description":
		if e.complexity.FeedData.Description == nil {
			break
		}

		return e.complexity.FeedData.Description(childComplexity), true

	case "FeedData.expirationTime":
		if e.complexity.FeedData.ExpirationTime == nil {
			break
		}

		return e.complexity.FeedData.ExpirationTime(childComplexity), true

	case "FeedData.feedForFriends":
		if e.complexity.FeedData.FeedForFriends == nil {
			break
		}

		return e.complexity.FeedData.FeedForFriends(childComplexity), true

	case "FeedData._id":
		if e.complexity.FeedData.ID == nil {
			break
		}

		return e.complexity.FeedData.ID(childComplexity), true

	case "FeedData.lastLikedByUserName":
		if e.complexity.FeedData.LastLikedByUserName == nil {
			break
		}

		return e.complexity.FeedData.LastLikedByUserName(childComplexity), true

	case "FeedData.likesCount":
		if e.complexity.FeedData.LikesCount == nil {
			break
		}

		return e.complexity.FeedData.LikesCount(childComplexity), true

	case "FeedData.sentAt":
		if e.complexity.FeedData.SentAt == nil {
			break
		}

		return e.complexity.FeedData.SentAt(childComplexity), true

	case "FeedData.sentFor":
		if e.complexity.FeedData.SentFor == nil {
			break
		}

		return e.complexity.FeedData.SentFor(childComplexity), true

	case "FeedData.title":
		if e.complexity.FeedData.Title == nil {
			break
		}

		return e.complexity.FeedData.Title(childComplexity), true

	case "FeedData.updatedAt":
		if e.complexity.FeedData.UpdatedAt == nil {
			break
		}

		return e.complexity.FeedData.UpdatedAt(childComplexity), true

	case "FeedForFriends.body":
		if e.complexity.FeedForFriends.Body == nil {
			break
		}

		return e.complexity.FeedForFriends.Body(childComplexity), true

	case "FeedForFriends.title":
		if e.complexity.FeedForFriends.Title == nil {
			break
		}

		return e.complexity.FeedForFriends.Title(childComplexity), true

	case "FeedResponse.feeds":
		if e.complexity.FeedResponse.Feeds == nil {
			break
		}

		return e.complexity.FeedResponse.Feeds(childComplexity), true

	case "FeedResponse.hasMore":
		if e.complexity.FeedResponse.HasMore == nil {
			break
		}

		return e.complexity.FeedResponse.HasMore(childComplexity), true

	case "FeedResponse.isRead":
		if e.complexity.FeedResponse.IsRead == nil {
			break
		}

		return e.complexity.FeedResponse.IsRead(childComplexity), true

	case "FeedResponse.lastId":
		if e.complexity.FeedResponse.LastID == nil {
			break
		}

		return e.complexity.FeedResponse.LastID(childComplexity), true

	case "FeedResponse.userDetails":
		if e.complexity.FeedResponse.UserDetails == nil {
			break
		}

		return e.complexity.FeedResponse.UserDetails(childComplexity), true

	case "Fictures.id":
		if e.complexity.Fictures.ID == nil {
			break
		}

		return e.complexity.Fictures.ID(childComplexity), true

	case "Fictures.participants":
		if e.complexity.Fictures.Participants == nil {
			break
		}

		return e.complexity.Fictures.Participants(childComplexity), true

	case "Fictures.round":
		if e.complexity.Fictures.Round == nil {
			break
		}

		return e.complexity.Fictures.Round(childComplexity), true

	case "Fictures.showdownId":
		if e.complexity.Fictures.ShowdownID == nil {
			break
		}

		return e.complexity.Fictures.ShowdownID(childComplexity), true

	case "Fictures.users":
		if e.complexity.Fictures.Users == nil {
			break
		}

		return e.complexity.Fictures.Users(childComplexity), true

	case "FicturesCollection.currentUserFicture":
		if e.complexity.FicturesCollection.CurrentUserFicture == nil {
			break
		}

		return e.complexity.FicturesCollection.CurrentUserFicture(childComplexity), true

	case "FieldValidation.emailSuffix":
		if e.complexity.FieldValidation.EmailSuffix == nil {
			break
		}

		return e.complexity.FieldValidation.EmailSuffix(childComplexity), true

	case "FieldValidation.emailSuffixes":
		if e.complexity.FieldValidation.EmailSuffixes == nil {
			break
		}

		return e.complexity.FieldValidation.EmailSuffixes(childComplexity), true

	case "FieldValidation.max":
		if e.complexity.FieldValidation.Max == nil {
			break
		}

		return e.complexity.FieldValidation.Max(childComplexity), true

	case "FieldValidation.min":
		if e.complexity.FieldValidation.Min == nil {
			break
		}

		return e.complexity.FieldValidation.Min(childComplexity), true

	case "FieldValidation.needManualVerification":
		if e.complexity.FieldValidation.NeedManualVerification == nil {
			break
		}

		return e.complexity.FieldValidation.NeedManualVerification(childComplexity), true

	case "FieldValidation.regex":
		if e.complexity.FieldValidation.Regex == nil {
			break
		}

		return e.complexity.FieldValidation.Regex(childComplexity), true

	case "File.content":
		if e.complexity.File.Content == nil {
			break
		}

		return e.complexity.File.Content(childComplexity), true

	case "File.contentType":
		if e.complexity.File.ContentType == nil {
			break
		}

		return e.complexity.File.ContentType(childComplexity), true

	case "File.name":
		if e.complexity.File.Name == nil {
			break
		}

		return e.complexity.File.Name(childComplexity), true

	case "File.url":
		if e.complexity.File.URL == nil {
			break
		}

		return e.complexity.File.URL(childComplexity), true

	case "FollowersAndFollowee.followedAt":
		if e.complexity.FollowersAndFollowee.FollowedAt == nil {
			break
		}

		return e.complexity.FollowersAndFollowee.FollowedAt(childComplexity), true

	case "FollowersAndFollowee.followeeId":
		if e.complexity.FollowersAndFollowee.FolloweeID == nil {
			break
		}

		return e.complexity.FollowersAndFollowee.FolloweeID(childComplexity), true

	case "FollowersAndFollowee.followerId":
		if e.complexity.FollowersAndFollowee.FollowerID == nil {
			break
		}

		return e.complexity.FollowersAndFollowee.FollowerID(childComplexity), true

	case "FollowersAndFollowee._id":
		if e.complexity.FollowersAndFollowee.ID == nil {
			break
		}

		return e.complexity.FollowersAndFollowee.ID(childComplexity), true

	case "FollowersAndFolloweeOutput.followedAt":
		if e.complexity.FollowersAndFolloweeOutput.FollowedAt == nil {
			break
		}

		return e.complexity.FollowersAndFolloweeOutput.FollowedAt(childComplexity), true

	case "FollowersAndFolloweeOutput.followeeId":
		if e.complexity.FollowersAndFolloweeOutput.FolloweeID == nil {
			break
		}

		return e.complexity.FollowersAndFolloweeOutput.FolloweeID(childComplexity), true

	case "FollowersAndFolloweeOutput.followerId":
		if e.complexity.FollowersAndFolloweeOutput.FollowerID == nil {
			break
		}

		return e.complexity.FollowersAndFolloweeOutput.FollowerID(childComplexity), true

	case "FollowersAndFolloweeOutput._id":
		if e.complexity.FollowersAndFolloweeOutput.ID == nil {
			break
		}

		return e.complexity.FollowersAndFolloweeOutput.ID(childComplexity), true

	case "FollowersAndFolloweeOutput.userInfo":
		if e.complexity.FollowersAndFolloweeOutput.UserInfo == nil {
			break
		}

		return e.complexity.FollowersAndFolloweeOutput.UserInfo(childComplexity), true

	case "FollowersAndFolloweePage.hasMore":
		if e.complexity.FollowersAndFolloweePage.HasMore == nil {
			break
		}

		return e.complexity.FollowersAndFolloweePage.HasMore(childComplexity), true

	case "FollowersAndFolloweePage.pageNumber":
		if e.complexity.FollowersAndFolloweePage.PageNumber == nil {
			break
		}

		return e.complexity.FollowersAndFolloweePage.PageNumber(childComplexity), true

	case "FollowersAndFolloweePage.pageSize":
		if e.complexity.FollowersAndFolloweePage.PageSize == nil {
			break
		}

		return e.complexity.FollowersAndFolloweePage.PageSize(childComplexity), true

	case "FollowersAndFolloweePage.results":
		if e.complexity.FollowersAndFolloweePage.Results == nil {
			break
		}

		return e.complexity.FollowersAndFolloweePage.Results(childComplexity), true

	case "FollowersAndFolloweePage.totalResults":
		if e.complexity.FollowersAndFolloweePage.TotalResults == nil {
			break
		}

		return e.complexity.FollowersAndFolloweePage.TotalResults(childComplexity), true

	case "FormField._id":
		if e.complexity.FormField.ID == nil {
			break
		}

		return e.complexity.FormField.ID(childComplexity), true

	case "FormField.label":
		if e.complexity.FormField.Label == nil {
			break
		}

		return e.complexity.FormField.Label(childComplexity), true

	case "FormField.name":
		if e.complexity.FormField.Name == nil {
			break
		}

		return e.complexity.FormField.Name(childComplexity), true

	case "FormField.options":
		if e.complexity.FormField.Options == nil {
			break
		}

		return e.complexity.FormField.Options(childComplexity), true

	case "FormField.required":
		if e.complexity.FormField.Required == nil {
			break
		}

		return e.complexity.FormField.Required(childComplexity), true

	case "FormField.type":
		if e.complexity.FormField.Type == nil {
			break
		}

		return e.complexity.FormField.Type(childComplexity), true

	case "FormField.validation":
		if e.complexity.FormField.Validation == nil {
			break
		}

		return e.complexity.FormField.Validation(childComplexity), true

	case "Forum.clubId":
		if e.complexity.Forum.ClubID == nil {
			break
		}

		return e.complexity.Forum.ClubID(childComplexity), true

	case "Forum.createdAt":
		if e.complexity.Forum.CreatedAt == nil {
			break
		}

		return e.complexity.Forum.CreatedAt(childComplexity), true

	case "Forum.createdBy":
		if e.complexity.Forum.CreatedBy == nil {
			break
		}

		return e.complexity.Forum.CreatedBy(childComplexity), true

	case "Forum.creatorInfo":
		if e.complexity.Forum.CreatorInfo == nil {
			break
		}

		return e.complexity.Forum.CreatorInfo(childComplexity), true

	case "Forum.description":
		if e.complexity.Forum.Description == nil {
			break
		}

		return e.complexity.Forum.Description(childComplexity), true

	case "Forum.id":
		if e.complexity.Forum.ID == nil {
			break
		}

		return e.complexity.Forum.ID(childComplexity), true

	case "Forum.title":
		if e.complexity.Forum.Title == nil {
			break
		}

		return e.complexity.Forum.Title(childComplexity), true

	case "Forum.updatedAt":
		if e.complexity.Forum.UpdatedAt == nil {
			break
		}

		return e.complexity.Forum.UpdatedAt(childComplexity), true

	case "ForumPage.hasMore":
		if e.complexity.ForumPage.HasMore == nil {
			break
		}

		return e.complexity.ForumPage.HasMore(childComplexity), true

	case "ForumPage.pageNumber":
		if e.complexity.ForumPage.PageNumber == nil {
			break
		}

		return e.complexity.ForumPage.PageNumber(childComplexity), true

	case "ForumPage.pageSize":
		if e.complexity.ForumPage.PageSize == nil {
			break
		}

		return e.complexity.ForumPage.PageSize(childComplexity), true

	case "ForumPage.results":
		if e.complexity.ForumPage.Results == nil {
			break
		}

		return e.complexity.ForumPage.Results(childComplexity), true

	case "ForumPage.totalResults":
		if e.complexity.ForumPage.TotalResults == nil {
			break
		}

		return e.complexity.ForumPage.TotalResults(childComplexity), true

	case "ForumReply.content":
		if e.complexity.ForumReply.Content == nil {
			break
		}

		return e.complexity.ForumReply.Content(childComplexity), true

	case "ForumReply.createdAt":
		if e.complexity.ForumReply.CreatedAt == nil {
			break
		}

		return e.complexity.ForumReply.CreatedAt(childComplexity), true

	case "ForumReply.createdBy":
		if e.complexity.ForumReply.CreatedBy == nil {
			break
		}

		return e.complexity.ForumReply.CreatedBy(childComplexity), true

	case "ForumReply.creatorInfo":
		if e.complexity.ForumReply.CreatorInfo == nil {
			break
		}

		return e.complexity.ForumReply.CreatorInfo(childComplexity), true

	case "ForumReply.id":
		if e.complexity.ForumReply.ID == nil {
			break
		}

		return e.complexity.ForumReply.ID(childComplexity), true

	case "ForumReply.threadId":
		if e.complexity.ForumReply.ThreadID == nil {
			break
		}

		return e.complexity.ForumReply.ThreadID(childComplexity), true

	case "ForumThread.content":
		if e.complexity.ForumThread.Content == nil {
			break
		}

		return e.complexity.ForumThread.Content(childComplexity), true

	case "ForumThread.createdAt":
		if e.complexity.ForumThread.CreatedAt == nil {
			break
		}

		return e.complexity.ForumThread.CreatedAt(childComplexity), true

	case "ForumThread.createdBy":
		if e.complexity.ForumThread.CreatedBy == nil {
			break
		}

		return e.complexity.ForumThread.CreatedBy(childComplexity), true

	case "ForumThread.creatorInfo":
		if e.complexity.ForumThread.CreatorInfo == nil {
			break
		}

		return e.complexity.ForumThread.CreatorInfo(childComplexity), true

	case "ForumThread.forumId":
		if e.complexity.ForumThread.ForumID == nil {
			break
		}

		return e.complexity.ForumThread.ForumID(childComplexity), true

	case "ForumThread.id":
		if e.complexity.ForumThread.ID == nil {
			break
		}

		return e.complexity.ForumThread.ID(childComplexity), true

	case "ForumThread.title":
		if e.complexity.ForumThread.Title == nil {
			break
		}

		return e.complexity.ForumThread.Title(childComplexity), true

	case "FriendRequest._id":
		if e.complexity.FriendRequest.ID == nil {
			break
		}

		return e.complexity.FriendRequest.ID(childComplexity), true

	case "FriendRequest.receiverId":
		if e.complexity.FriendRequest.ReceiverID == nil {
			break
		}

		return e.complexity.FriendRequest.ReceiverID(childComplexity), true

	case "FriendRequest.respondedAt":
		if e.complexity.FriendRequest.RespondedAt == nil {
			break
		}

		return e.complexity.FriendRequest.RespondedAt(childComplexity), true

	case "FriendRequest.senderId":
		if e.complexity.FriendRequest.SenderID == nil {
			break
		}

		return e.complexity.FriendRequest.SenderID(childComplexity), true

	case "FriendRequest.sentAt":
		if e.complexity.FriendRequest.SentAt == nil {
			break
		}

		return e.complexity.FriendRequest.SentAt(childComplexity), true

	case "FriendRequest.status":
		if e.complexity.FriendRequest.Status == nil {
			break
		}

		return e.complexity.FriendRequest.Status(childComplexity), true

	case "FriendRequestOutput._id":
		if e.complexity.FriendRequestOutput.ID == nil {
			break
		}

		return e.complexity.FriendRequestOutput.ID(childComplexity), true

	case "FriendRequestOutput.receiverId":
		if e.complexity.FriendRequestOutput.ReceiverID == nil {
			break
		}

		return e.complexity.FriendRequestOutput.ReceiverID(childComplexity), true

	case "FriendRequestOutput.respondedAt":
		if e.complexity.FriendRequestOutput.RespondedAt == nil {
			break
		}

		return e.complexity.FriendRequestOutput.RespondedAt(childComplexity), true

	case "FriendRequestOutput.sender":
		if e.complexity.FriendRequestOutput.Sender == nil {
			break
		}

		return e.complexity.FriendRequestOutput.Sender(childComplexity), true

	case "FriendRequestOutput.senderId":
		if e.complexity.FriendRequestOutput.SenderID == nil {
			break
		}

		return e.complexity.FriendRequestOutput.SenderID(childComplexity), true

	case "FriendRequestOutput.sentAt":
		if e.complexity.FriendRequestOutput.SentAt == nil {
			break
		}

		return e.complexity.FriendRequestOutput.SentAt(childComplexity), true

	case "FriendRequestOutput.status":
		if e.complexity.FriendRequestOutput.Status == nil {
			break
		}

		return e.complexity.FriendRequestOutput.Status(childComplexity), true

	case "FriendRequestPage.hasMore":
		if e.complexity.FriendRequestPage.HasMore == nil {
			break
		}

		return e.complexity.FriendRequestPage.HasMore(childComplexity), true

	case "FriendRequestPage.pageNumber":
		if e.complexity.FriendRequestPage.PageNumber == nil {
			break
		}

		return e.complexity.FriendRequestPage.PageNumber(childComplexity), true

	case "FriendRequestPage.pageSize":
		if e.complexity.FriendRequestPage.PageSize == nil {
			break
		}

		return e.complexity.FriendRequestPage.PageSize(childComplexity), true

	case "FriendRequestPage.results":
		if e.complexity.FriendRequestPage.Results == nil {
			break
		}

		return e.complexity.FriendRequestPage.Results(childComplexity), true

	case "FriendRequestPage.totalResults":
		if e.complexity.FriendRequestPage.TotalResults == nil {
			break
		}

		return e.complexity.FriendRequestPage.TotalResults(childComplexity), true

	case "Friends.acceptedAt":
		if e.complexity.Friends.AcceptedAt == nil {
			break
		}

		return e.complexity.Friends.AcceptedAt(childComplexity), true

	case "Friends._id":
		if e.complexity.Friends.ID == nil {
			break
		}

		return e.complexity.Friends.ID(childComplexity), true

	case "Friends.receiverId":
		if e.complexity.Friends.ReceiverID == nil {
			break
		}

		return e.complexity.Friends.ReceiverID(childComplexity), true

	case "Friends.senderId":
		if e.complexity.Friends.SenderID == nil {
			break
		}

		return e.complexity.Friends.SenderID(childComplexity), true

	case "FriendsOutput.acceptedAt":
		if e.complexity.FriendsOutput.AcceptedAt == nil {
			break
		}

		return e.complexity.FriendsOutput.AcceptedAt(childComplexity), true

	case "FriendsOutput.currActivity":
		if e.complexity.FriendsOutput.CurrActivity == nil {
			break
		}

		return e.complexity.FriendsOutput.CurrActivity(childComplexity), true

	case "FriendsOutput.friendInfo":
		if e.complexity.FriendsOutput.FriendInfo == nil {
			break
		}

		return e.complexity.FriendsOutput.FriendInfo(childComplexity), true

	case "FriendsOutput._id":
		if e.complexity.FriendsOutput.ID == nil {
			break
		}

		return e.complexity.FriendsOutput.ID(childComplexity), true

	case "FriendsOutput.isOnline":
		if e.complexity.FriendsOutput.IsOnline == nil {
			break
		}

		return e.complexity.FriendsOutput.IsOnline(childComplexity), true

	case "FriendsOutput.receiverId":
		if e.complexity.FriendsOutput.ReceiverID == nil {
			break
		}

		return e.complexity.FriendsOutput.ReceiverID(childComplexity), true

	case "FriendsOutput.senderId":
		if e.complexity.FriendsOutput.SenderID == nil {
			break
		}

		return e.complexity.FriendsOutput.SenderID(childComplexity), true

	case "FriendsPage.hasMore":
		if e.complexity.FriendsPage.HasMore == nil {
			break
		}

		return e.complexity.FriendsPage.HasMore(childComplexity), true

	case "FriendsPage.pageNumber":
		if e.complexity.FriendsPage.PageNumber == nil {
			break
		}

		return e.complexity.FriendsPage.PageNumber(childComplexity), true

	case "FriendsPage.pageSize":
		if e.complexity.FriendsPage.PageSize == nil {
			break
		}

		return e.complexity.FriendsPage.PageSize(childComplexity), true

	case "FriendsPage.results":
		if e.complexity.FriendsPage.Results == nil {
			break
		}

		return e.complexity.FriendsPage.Results(childComplexity), true

	case "FriendsPage.totalResults":
		if e.complexity.FriendsPage.TotalResults == nil {
			break
		}

		return e.complexity.FriendsPage.TotalResults(childComplexity), true

	case "Game.config":
		if e.complexity.Game.Config == nil {
			break
		}

		return e.complexity.Game.Config(childComplexity), true

	case "Game.createdBy":
		if e.complexity.Game.CreatedBy == nil {
			break
		}

		return e.complexity.Game.CreatedBy(childComplexity), true

	case "Game.encryptedQuestions":
		if e.complexity.Game.EncryptedQuestions == nil {
			break
		}

		return e.complexity.Game.EncryptedQuestions(childComplexity), true

	case "Game.endTime":
		if e.complexity.Game.EndTime == nil {
			break
		}

		return e.complexity.Game.EndTime(childComplexity), true

	case "Game.gameCategory":
		if e.complexity.Game.GameCategory == nil {
			break
		}

		return e.complexity.Game.GameCategory(childComplexity), true

	case "Game.gameMode":
		if e.complexity.Game.GameMode == nil {
			break
		}

		return e.complexity.Game.GameMode(childComplexity), true

	case "Game.gameStatus":
		if e.complexity.Game.GameStatus == nil {
			break
		}

		return e.complexity.Game.GameStatus(childComplexity), true

	case "Game.gameType":
		if e.complexity.Game.GameType == nil {
			break
		}

		return e.complexity.Game.GameType(childComplexity), true

	case "Game._id":
		if e.complexity.Game.ID == nil {
			break
		}

		return e.complexity.Game.ID(childComplexity), true

	case "Game.leaderBoard":
		if e.complexity.Game.LeaderBoard == nil {
			break
		}

		return e.complexity.Game.LeaderBoard(childComplexity), true

	case "Game.minifiedQuestions":
		if e.complexity.Game.MinifiedQuestions == nil {
			break
		}

		return e.complexity.Game.MinifiedQuestions(childComplexity), true

	case "Game.players":
		if e.complexity.Game.Players == nil {
			break
		}

		return e.complexity.Game.Players(childComplexity), true

	case "Game.questions":
		if e.complexity.Game.Questions == nil {
			break
		}

		return e.complexity.Game.Questions(childComplexity), true

	case "Game.rematchRequestedBy":
		if e.complexity.Game.RematchRequestedBy == nil {
			break
		}

		return e.complexity.Game.RematchRequestedBy(childComplexity), true

	case "Game.seriesId":
		if e.complexity.Game.SeriesID == nil {
			break
		}

		return e.complexity.Game.SeriesID(childComplexity), true

	case "Game.showdownGameConfig":
		if e.complexity.Game.ShowdownGameConfig == nil {
			break
		}

		return e.complexity.Game.ShowdownGameConfig(childComplexity), true

	case "Game.showdownId":
		if e.complexity.Game.ShowdownId == nil {
			break
		}

		return e.complexity.Game.ShowdownId(childComplexity), true

	case "Game.startTime":
		if e.complexity.Game.StartTime == nil {
			break
		}

		return e.complexity.Game.StartTime(childComplexity), true

	case "GameCanceledOutput.creatorId":
		if e.complexity.GameCanceledOutput.CreatorID == nil {
			break
		}

		return e.complexity.GameCanceledOutput.CreatorID(childComplexity), true

	case "GameCanceledOutput.gameId":
		if e.complexity.GameCanceledOutput.GameID == nil {
			break
		}

		return e.complexity.GameCanceledOutput.GameID(childComplexity), true

	case "GameCanceledOutput.opponentId":
		if e.complexity.GameCanceledOutput.OpponentID == nil {
			break
		}

		return e.complexity.GameCanceledOutput.OpponentID(childComplexity), true

	case "GameCanceledOutput.status":
		if e.complexity.GameCanceledOutput.Status == nil {
			break
		}

		return e.complexity.GameCanceledOutput.Status(childComplexity), true

	case "GameCategorySpecificConfig.blitz":
		if e.complexity.GameCategorySpecificConfig.Blitz == nil {
			break
		}

		return e.complexity.GameCategorySpecificConfig.Blitz(childComplexity), true

	case "GameCategorySpecificConfig.category":
		if e.complexity.GameCategorySpecificConfig.Category == nil {
			break
		}

		return e.complexity.GameCategorySpecificConfig.Category(childComplexity), true

	case "GameCategorySpecificConfig.classical":
		if e.complexity.GameCategorySpecificConfig.Classical == nil {
			break
		}

		return e.complexity.GameCategorySpecificConfig.Classical(childComplexity), true

	case "GameCategorySpecificConfig.memory":
		if e.complexity.GameCategorySpecificConfig.Memory == nil {
			break
		}

		return e.complexity.GameCategorySpecificConfig.Memory(childComplexity), true

	case "GameCategorySpecificConfig.puzzle":
		if e.complexity.GameCategorySpecificConfig.Puzzle == nil {
			break
		}

		return e.complexity.GameCategorySpecificConfig.Puzzle(childComplexity), true

	case "GameConfig.categorySpecificConfig":
		if e.complexity.GameConfig.CategorySpecificConfig == nil {
			break
		}

		return e.complexity.GameConfig.CategorySpecificConfig(childComplexity), true

	case "GameConfig.difficultyLevel":
		if e.complexity.GameConfig.DifficultyLevel == nil {
			break
		}

		return e.complexity.GameConfig.DifficultyLevel(childComplexity), true

	case "GameConfig.gameType":
		if e.complexity.GameConfig.GameType == nil {
			break
		}

		return e.complexity.GameConfig.GameType(childComplexity), true

	case "GameConfig.gameTypeSpecificConfig":
		if e.complexity.GameConfig.GameTypeSpecificConfig == nil {
			break
		}

		return e.complexity.GameConfig.GameTypeSpecificConfig(childComplexity), true

	case "GameConfig.maxTimePerQuestion":
		if e.complexity.GameConfig.MaxTimePerQuestion == nil {
			break
		}

		return e.complexity.GameConfig.MaxTimePerQuestion(childComplexity), true

	case "GameConfig.modeSpecificConfig":
		if e.complexity.GameConfig.ModeSpecificConfig == nil {
			break
		}

		return e.complexity.GameConfig.ModeSpecificConfig(childComplexity), true

	case "GameConfig.numPlayers":
		if e.complexity.GameConfig.NumPlayers == nil {
			break
		}

		return e.complexity.GameConfig.NumPlayers(childComplexity), true

	case "GameConfig.questionTags":
		if e.complexity.GameConfig.QuestionTags == nil {
			break
		}

		return e.complexity.GameConfig.QuestionTags(childComplexity), true

	case "GameConfig.timeLimit":
		if e.complexity.GameConfig.TimeLimit == nil {
			break
		}

		return e.complexity.GameConfig.TimeLimit(childComplexity), true

	case "GameDetailedAnalysis.game":
		if e.complexity.GameDetailedAnalysis.Game == nil {
			break
		}

		return e.complexity.GameDetailedAnalysis.Game(childComplexity), true

	case "GameDetailedAnalysis.questions":
		if e.complexity.GameDetailedAnalysis.Questions == nil {
			break
		}

		return e.complexity.GameDetailedAnalysis.Questions(childComplexity), true

	case "GameModeSpecificConfig.groupPlay":
		if e.complexity.GameModeSpecificConfig.GroupPlay == nil {
			break
		}

		return e.complexity.GameModeSpecificConfig.GroupPlay(childComplexity), true

	case "GameModeSpecificConfig.mode":
		if e.complexity.GameModeSpecificConfig.Mode == nil {
			break
		}

		return e.complexity.GameModeSpecificConfig.Mode(childComplexity), true

	case "GameModeSpecificConfig.onlineChallenge":
		if e.complexity.GameModeSpecificConfig.OnlineChallenge == nil {
			break
		}

		return e.complexity.GameModeSpecificConfig.OnlineChallenge(childComplexity), true

	case "GameModeSpecificConfig.onlineSearch":
		if e.complexity.GameModeSpecificConfig.OnlineSearch == nil {
			break
		}

		return e.complexity.GameModeSpecificConfig.OnlineSearch(childComplexity), true

	case "GameModeSpecificConfig.playViaLink":
		if e.complexity.GameModeSpecificConfig.PlayViaLink == nil {
			break
		}

		return e.complexity.GameModeSpecificConfig.PlayViaLink(childComplexity), true

	case "GameModeSpecificConfig.practice":
		if e.complexity.GameModeSpecificConfig.Practice == nil {
			break
		}

		return e.complexity.GameModeSpecificConfig.Practice(childComplexity), true

	case "GameModeSpecificConfig.rushWithTime":
		if e.complexity.GameModeSpecificConfig.RushWithTime == nil {
			break
		}

		return e.complexity.GameModeSpecificConfig.RushWithTime(childComplexity), true

	case "GameModeSpecificConfig.rushWithoutTime":
		if e.complexity.GameModeSpecificConfig.RushWithoutTime == nil {
			break
		}

		return e.complexity.GameModeSpecificConfig.RushWithoutTime(childComplexity), true

	case "GameModeSpecificConfig.sumdayShowdown":
		if e.complexity.GameModeSpecificConfig.SumdayShowdown == nil {
			break
		}

		return e.complexity.GameModeSpecificConfig.SumdayShowdown(childComplexity), true

	case "GameModeSpecificConfig.survivalSaturday":
		if e.complexity.GameModeSpecificConfig.SurvivalSaturday == nil {
			break
		}

		return e.complexity.GameModeSpecificConfig.SurvivalSaturday(childComplexity), true

	case "GameQuestion.question":
		if e.complexity.GameQuestion.Question == nil {
			break
		}

		return e.complexity.GameQuestion.Question(childComplexity), true

	case "GameQuestion.stats":
		if e.complexity.GameQuestion.Stats == nil {
			break
		}

		return e.complexity.GameQuestion.Stats(childComplexity), true

	case "GameQuestion.submissions":
		if e.complexity.GameQuestion.Submissions == nil {
			break
		}

		return e.complexity.GameQuestion.Submissions(childComplexity), true

	case "GameQuestionAnalysis.avgTimes":
		if e.complexity.GameQuestionAnalysis.AvgTimes == nil {
			break
		}

		return e.complexity.GameQuestionAnalysis.AvgTimes(childComplexity), true

	case "GameQuestionAnalysis.globalAvgTime":
		if e.complexity.GameQuestionAnalysis.GlobalAvgTime == nil {
			break
		}

		return e.complexity.GameQuestionAnalysis.GlobalAvgTime(childComplexity), true

	case "GameQuestionAnalysis.globalBestTime":
		if e.complexity.GameQuestionAnalysis.GlobalBestTime == nil {
			break
		}

		return e.complexity.GameQuestionAnalysis.GlobalBestTime(childComplexity), true

	case "GameQuestionAnalysis.question":
		if e.complexity.GameQuestionAnalysis.Question == nil {
			break
		}

		return e.complexity.GameQuestionAnalysis.Question(childComplexity), true

	case "GameQuestionStats.fastestTime":
		if e.complexity.GameQuestionStats.FastestTime == nil {
			break
		}

		return e.complexity.GameQuestionStats.FastestTime(childComplexity), true

	case "GameQuestionStats.userIds":
		if e.complexity.GameQuestionStats.UserIds == nil {
			break
		}

		return e.complexity.GameQuestionStats.UserIds(childComplexity), true

	case "GameSeries.gameIds":
		if e.complexity.GameSeries.GameIds == nil {
			break
		}

		return e.complexity.GameSeries.GameIds(childComplexity), true

	case "GameSeries._id":
		if e.complexity.GameSeries.ID == nil {
			break
		}

		return e.complexity.GameSeries.ID(childComplexity), true

	case "GameSeries.playerIds":
		if e.complexity.GameSeries.PlayerIds == nil {
			break
		}

		return e.complexity.GameSeries.PlayerIds(childComplexity), true

	case "GameTypeSpecificConfig.type":
		if e.complexity.GameTypeSpecificConfig.Type == nil {
			break
		}

		return e.complexity.GameTypeSpecificConfig.Type(childComplexity), true

	case "GetGamesByRatingOutput.games":
		if e.complexity.GetGamesByRatingOutput.Games == nil {
			break
		}

		return e.complexity.GetGamesByRatingOutput.Games(childComplexity), true

	case "GetGamesByRatingOutput.puzzleGames":
		if e.complexity.GetGamesByRatingOutput.PuzzleGames == nil {
			break
		}

		return e.complexity.GetGamesByRatingOutput.PuzzleGames(childComplexity), true

	case "GetGamesByRatingOutput.totalCount":
		if e.complexity.GetGamesByRatingOutput.TotalCount == nil {
			break
		}

		return e.complexity.GetGamesByRatingOutput.TotalCount(childComplexity), true

	case "GetGamesByRatingOutput.users":
		if e.complexity.GetGamesByRatingOutput.Users == nil {
			break
		}

		return e.complexity.GetGamesByRatingOutput.Users(childComplexity), true

	case "GetGamesOutput.games":
		if e.complexity.GetGamesOutput.Games == nil {
			break
		}

		return e.complexity.GetGamesOutput.Games(childComplexity), true

	case "GetGamesOutput.users":
		if e.complexity.GetGamesOutput.Users == nil {
			break
		}

		return e.complexity.GetGamesOutput.Users(childComplexity), true

	case "GetPuzzleGamesOutput.games":
		if e.complexity.GetPuzzleGamesOutput.Games == nil {
			break
		}

		return e.complexity.GetPuzzleGamesOutput.Games(childComplexity), true

	case "GetPuzzleGamesOutput.users":
		if e.complexity.GetPuzzleGamesOutput.Users == nil {
			break
		}

		return e.complexity.GetPuzzleGamesOutput.Users(childComplexity), true

	case "GlobalPreset.bestStreak":
		if e.complexity.GlobalPreset.BestStreak == nil {
			break
		}

		return e.complexity.GlobalPreset.BestStreak(childComplexity), true

	case "GlobalPreset.bestTime":
		if e.complexity.GlobalPreset.BestTime == nil {
			break
		}

		return e.complexity.GlobalPreset.BestTime(childComplexity), true

	case "GlobalPreset.globalAccuracy":
		if e.complexity.GlobalPreset.GlobalAccuracy == nil {
			break
		}

		return e.complexity.GlobalPreset.GlobalAccuracy(childComplexity), true

	case "GlobalPreset.globalAverageTime":
		if e.complexity.GlobalPreset.GlobalAverageTime == nil {
			break
		}

		return e.complexity.GlobalPreset.GlobalAverageTime(childComplexity), true

	case "GlobalPreset._id":
		if e.complexity.GlobalPreset.ID == nil {
			break
		}

		return e.complexity.GlobalPreset.ID(childComplexity), true

	case "GlobalPreset.identifier":
		if e.complexity.GlobalPreset.Identifier == nil {
			break
		}

		return e.complexity.GlobalPreset.Identifier(childComplexity), true

	case "GlobalPreset.incorrectSubmissions":
		if e.complexity.GlobalPreset.IncorrectSubmissions == nil {
			break
		}

		return e.complexity.GlobalPreset.IncorrectSubmissions(childComplexity), true

	case "GlobalPreset.numOfCorrectSubmissions":
		if e.complexity.GlobalPreset.NumOfCorrectSubmissions == nil {
			break
		}

		return e.complexity.GlobalPreset.NumOfCorrectSubmissions(childComplexity), true

	case "GlobalPreset.top10Mathletes":
		if e.complexity.GlobalPreset.Top10Mathletes == nil {
			break
		}

		return e.complexity.GlobalPreset.Top10Mathletes(childComplexity), true

	case "GlobalPreset.totalQuestionsSolved":
		if e.complexity.GlobalPreset.TotalQuestionsSolved == nil {
			break
		}

		return e.complexity.GlobalPreset.TotalQuestionsSolved(childComplexity), true

	case "GlobalPresets.globalPresets":
		if e.complexity.GlobalPresets.GlobalPresets == nil {
			break
		}

		return e.complexity.GlobalPresets.GlobalPresets(childComplexity), true

	case "GlobalPresets.totalCount":
		if e.complexity.GlobalPresets.TotalCount == nil {
			break
		}

		return e.complexity.GlobalPresets.TotalCount(childComplexity), true

	case "GroupPlayGameConfig.difficultyLevel":
		if e.complexity.GroupPlayGameConfig.DifficultyLevel == nil {
			break
		}

		return e.complexity.GroupPlayGameConfig.DifficultyLevel(childComplexity), true

	case "GroupPlayGameConfig.maxGapBwGame":
		if e.complexity.GroupPlayGameConfig.MaxGapBwGame == nil {
			break
		}

		return e.complexity.GroupPlayGameConfig.MaxGapBwGame(childComplexity), true

	case "GroupPlayGameConfig.maxPlayers":
		if e.complexity.GroupPlayGameConfig.MaxPlayers == nil {
			break
		}

		return e.complexity.GroupPlayGameConfig.MaxPlayers(childComplexity), true

	case "GroupPlayGameConfig.maxTimePerQuestion":
		if e.complexity.GroupPlayGameConfig.MaxTimePerQuestion == nil {
			break
		}

		return e.complexity.GroupPlayGameConfig.MaxTimePerQuestion(childComplexity), true

	case "GroupPlayGameConfig.minPlayers":
		if e.complexity.GroupPlayGameConfig.MinPlayers == nil {
			break
		}

		return e.complexity.GroupPlayGameConfig.MinPlayers(childComplexity), true

	case "GroupPlayGameConfig.questionTags":
		if e.complexity.GroupPlayGameConfig.QuestionTags == nil {
			break
		}

		return e.complexity.GroupPlayGameConfig.QuestionTags(childComplexity), true

	case "HectocPuzzle.puzzleString":
		if e.complexity.HectocPuzzle.PuzzleString == nil {
			break
		}

		return e.complexity.HectocPuzzle.PuzzleString(childComplexity), true

	case "HostDetails.logo":
		if e.complexity.HostDetails.Logo == nil {
			break
		}

		return e.complexity.HostDetails.Logo(childComplexity), true

	case "HostDetails.name":
		if e.complexity.HostDetails.Name == nil {
			break
		}

		return e.complexity.HostDetails.Name(childComplexity), true

	case "HostInfo.hostLogo":
		if e.complexity.HostInfo.HostLogo == nil {
			break
		}

		return e.complexity.HostInfo.HostLogo(childComplexity), true

	case "HostInfo.hostType":
		if e.complexity.HostInfo.HostType == nil {
			break
		}

		return e.complexity.HostInfo.HostType(childComplexity), true

	case "HostInfo.id":
		if e.complexity.HostInfo.ID == nil {
			break
		}

		return e.complexity.HostInfo.ID(childComplexity), true

	case "Institution.city":
		if e.complexity.Institution.City == nil {
			break
		}

		return e.complexity.Institution.City(childComplexity), true

	case "Institution.country":
		if e.complexity.Institution.Country == nil {
			break
		}

		return e.complexity.Institution.Country(childComplexity), true

	case "Institution.domains":
		if e.complexity.Institution.Domains == nil {
			break
		}

		return e.complexity.Institution.Domains(childComplexity), true

	case "Institution.id":
		if e.complexity.Institution.ID == nil {
			break
		}

		return e.complexity.Institution.ID(childComplexity), true

	case "Institution.name":
		if e.complexity.Institution.Name == nil {
			break
		}

		return e.complexity.Institution.Name(childComplexity), true

	case "Institution.slug":
		if e.complexity.Institution.Slug == nil {
			break
		}

		return e.complexity.Institution.Slug(childComplexity), true

	case "Institution.state":
		if e.complexity.Institution.State == nil {
			break
		}

		return e.complexity.Institution.State(childComplexity), true

	case "JoinedWeeklyLeagueEvent.leagueInfo":
		if e.complexity.JoinedWeeklyLeagueEvent.LeagueInfo == nil {
			break
		}

		return e.complexity.JoinedWeeklyLeagueEvent.LeagueInfo(childComplexity), true

	case "KenKenPuzzle.puzzleString":
		if e.complexity.KenKenPuzzle.PuzzleString == nil {
			break
		}

		return e.complexity.KenKenPuzzle.PuzzleString(childComplexity), true

	case "LeaderBoardEntry.correct":
		if e.complexity.LeaderBoardEntry.Correct == nil {
			break
		}

		return e.complexity.LeaderBoardEntry.Correct(childComplexity), true

	case "LeaderBoardEntry.incorrect":
		if e.complexity.LeaderBoardEntry.Incorrect == nil {
			break
		}

		return e.complexity.LeaderBoardEntry.Incorrect(childComplexity), true

	case "LeaderBoardEntry.rank":
		if e.complexity.LeaderBoardEntry.Rank == nil {
			break
		}

		return e.complexity.LeaderBoardEntry.Rank(childComplexity), true

	case "LeaderBoardEntry.ratingChange":
		if e.complexity.LeaderBoardEntry.RatingChange == nil {
			break
		}

		return e.complexity.LeaderBoardEntry.RatingChange(childComplexity), true

	case "LeaderBoardEntry.statikCoinsEarned":
		if e.complexity.LeaderBoardEntry.StatikCoinsEarned == nil {
			break
		}

		return e.complexity.LeaderBoardEntry.StatikCoinsEarned(childComplexity), true

	case "LeaderBoardEntry.totalPoints":
		if e.complexity.LeaderBoardEntry.TotalPoints == nil {
			break
		}

		return e.complexity.LeaderBoardEntry.TotalPoints(childComplexity), true

	case "LeaderBoardEntry.userId":
		if e.complexity.LeaderBoardEntry.UserID == nil {
			break
		}

		return e.complexity.LeaderBoardEntry.UserID(childComplexity), true

	case "LeaderParticipantEntity._id":
		if e.complexity.LeaderParticipantEntity.ID == nil {
			break
		}

		return e.complexity.LeaderParticipantEntity.ID(childComplexity), true

	case "LeaderParticipantEntity.participant":
		if e.complexity.LeaderParticipantEntity.Participant == nil {
			break
		}

		return e.complexity.LeaderParticipantEntity.Participant(childComplexity), true

	case "LeaderParticipantEntity.rank":
		if e.complexity.LeaderParticipantEntity.Rank == nil {
			break
		}

		return e.complexity.LeaderParticipantEntity.Rank(childComplexity), true

	case "LeaderParticipantEntity.score":
		if e.complexity.LeaderParticipantEntity.Score == nil {
			break
		}

		return e.complexity.LeaderParticipantEntity.Score(childComplexity), true

	case "LeaderParticipantEntity.showdownId":
		if e.complexity.LeaderParticipantEntity.ShowdownId == nil {
			break
		}

		return e.complexity.LeaderParticipantEntity.ShowdownId(childComplexity), true

	case "LeaderParticipantEntity.userId":
		if e.complexity.LeaderParticipantEntity.UserId == nil {
			break
		}

		return e.complexity.LeaderParticipantEntity.UserId(childComplexity), true

	case "LeaderboardConnection.edges":
		if e.complexity.LeaderboardConnection.Edges == nil {
			break
		}

		return e.complexity.LeaderboardConnection.Edges(childComplexity), true

	case "LeaderboardConnection.pageInfo":
		if e.complexity.LeaderboardConnection.PageInfo == nil {
			break
		}

		return e.complexity.LeaderboardConnection.PageInfo(childComplexity), true

	case "LeaderboardEdge.cursor":
		if e.complexity.LeaderboardEdge.Cursor == nil {
			break
		}

		return e.complexity.LeaderboardEdge.Cursor(childComplexity), true

	case "LeaderboardEdge.node":
		if e.complexity.LeaderboardEdge.Node == nil {
			break
		}

		return e.complexity.LeaderboardEdge.Node(childComplexity), true

	case "LeaderboardPage.hasMore":
		if e.complexity.LeaderboardPage.HasMore == nil {
			break
		}

		return e.complexity.LeaderboardPage.HasMore(childComplexity), true

	case "LeaderboardPage.pageNumber":
		if e.complexity.LeaderboardPage.PageNumber == nil {
			break
		}

		return e.complexity.LeaderboardPage.PageNumber(childComplexity), true

	case "LeaderboardPage.pageSize":
		if e.complexity.LeaderboardPage.PageSize == nil {
			break
		}

		return e.complexity.LeaderboardPage.PageSize(childComplexity), true

	case "LeaderboardPage.results":
		if e.complexity.LeaderboardPage.Results == nil {
			break
		}

		return e.complexity.LeaderboardPage.Results(childComplexity), true

	case "LeaderboardPage.totalResults":
		if e.complexity.LeaderboardPage.TotalResults == nil {
			break
		}

		return e.complexity.LeaderboardPage.TotalResults(childComplexity), true

	case "League.chatRoomId":
		if e.complexity.League.ChatRoomId == nil {
			break
		}

		return e.complexity.League.ChatRoomId(childComplexity), true

	case "League.currentUserParticipation":
		if e.complexity.League.CurrentUserParticipation == nil {
			break
		}

		return e.complexity.League.CurrentUserParticipation(childComplexity), true

	case "League.currentUserResult":
		if e.complexity.League.CurrentUserResult == nil {
			break
		}

		return e.complexity.League.CurrentUserResult(childComplexity), true

	case "League.details":
		if e.complexity.League.Details == nil {
			break
		}

		return e.complexity.League.Details(childComplexity), true

	case "League.hostedBy":
		if e.complexity.League.HostedBy == nil {
			break
		}

		return e.complexity.League.HostedBy(childComplexity), true

	case "League.hostedByV2":
		if e.complexity.League.HostedByV2 == nil {
			break
		}

		return e.complexity.League.HostedByV2(childComplexity), true

	case "League.id":
		if e.complexity.League.ID == nil {
			break
		}

		return e.complexity.League.ID(childComplexity), true

	case "League.leagueEnd":
		if e.complexity.League.LeagueEnd == nil {
			break
		}

		return e.complexity.League.LeagueEnd(childComplexity), true

	case "League.leagueStart":
		if e.complexity.League.LeagueStart == nil {
			break
		}

		return e.complexity.League.LeagueStart(childComplexity), true

	case "League.name":
		if e.complexity.League.Name == nil {
			break
		}

		return e.complexity.League.Name(childComplexity), true

	case "League.registrationCount":
		if e.complexity.League.RegistrationCount == nil {
			break
		}

		return e.complexity.League.RegistrationCount(childComplexity), true

	case "League.registrationEnd":
		if e.complexity.League.RegistrationEnd == nil {
			break
		}

		return e.complexity.League.RegistrationEnd(childComplexity), true

	case "League.registrationForm":
		if e.complexity.League.RegistrationForm == nil {
			break
		}

		return e.complexity.League.RegistrationForm(childComplexity), true

	case "League.registrationStart":
		if e.complexity.League.RegistrationStart == nil {
			break
		}

		return e.complexity.League.RegistrationStart(childComplexity), true

	case "LeagueDetails.about":
		if e.complexity.LeagueDetails.About == nil {
			break
		}

		return e.complexity.LeagueDetails.About(childComplexity), true

	case "LeagueDetails.awards":
		if e.complexity.LeagueDetails.Awards == nil {
			break
		}

		return e.complexity.LeagueDetails.Awards(childComplexity), true

	case "LeagueDetails.instructions":
		if e.complexity.LeagueDetails.Instructions == nil {
			break
		}

		return e.complexity.LeagueDetails.Instructions(childComplexity), true

	case "LeagueDetails.requirements":
		if e.complexity.LeagueDetails.Requirements == nil {
			break
		}

		return e.complexity.LeagueDetails.Requirements(childComplexity), true

	case "LeagueInfo.coinsTillLastWeek":
		if e.complexity.LeagueInfo.CoinsTillLastWeek == nil {
			break
		}

		return e.complexity.LeagueInfo.CoinsTillLastWeek(childComplexity), true

	case "LeagueInfo.groupId":
		if e.complexity.LeagueInfo.GroupID == nil {
			break
		}

		return e.complexity.LeagueInfo.GroupID(childComplexity), true

	case "LeagueInfo.hasParticipated":
		if e.complexity.LeagueInfo.HasParticipated == nil {
			break
		}

		return e.complexity.LeagueInfo.HasParticipated(childComplexity), true

	case "LeagueInfo.league":
		if e.complexity.LeagueInfo.League == nil {
			break
		}

		return e.complexity.LeagueInfo.League(childComplexity), true

	case "LeagueInfo.progressState":
		if e.complexity.LeagueInfo.ProgressState == nil {
			break
		}

		return e.complexity.LeagueInfo.ProgressState(childComplexity), true

	case "LeagueInfo.updatedAt":
		if e.complexity.LeagueInfo.UpdatedAt == nil {
			break
		}

		return e.complexity.LeagueInfo.UpdatedAt(childComplexity), true

	case "LeagueLeaderboardEntry.activitySummary":
		if e.complexity.LeagueLeaderboardEntry.ActivitySummary == nil {
			break
		}

		return e.complexity.LeagueLeaderboardEntry.ActivitySummary(childComplexity), true

	case "LeagueLeaderboardEntry.rank":
		if e.complexity.LeagueLeaderboardEntry.Rank == nil {
			break
		}

		return e.complexity.LeagueLeaderboardEntry.Rank(childComplexity), true

	case "LeagueLeaderboardEntry.statikCoins":
		if e.complexity.LeagueLeaderboardEntry.StatikCoins == nil {
			break
		}

		return e.complexity.LeagueLeaderboardEntry.StatikCoins(childComplexity), true

	case "LeagueLeaderboardEntry.user":
		if e.complexity.LeagueLeaderboardEntry.User == nil {
			break
		}

		return e.complexity.LeagueLeaderboardEntry.User(childComplexity), true

	case "LeagueLeaderboardPage.participants":
		if e.complexity.LeagueLeaderboardPage.Participants == nil {
			break
		}

		return e.complexity.LeagueLeaderboardPage.Participants(childComplexity), true

	case "LeagueLeaderboardPage.totalCount":
		if e.complexity.LeagueLeaderboardPage.TotalCount == nil {
			break
		}

		return e.complexity.LeagueLeaderboardPage.TotalCount(childComplexity), true

	case "LeagueParticipant.id":
		if e.complexity.LeagueParticipant.ID == nil {
			break
		}

		return e.complexity.LeagueParticipant.ID(childComplexity), true

	case "LeagueParticipant.joinedAt":
		if e.complexity.LeagueParticipant.JoinedAt == nil {
			break
		}

		return e.complexity.LeagueParticipant.JoinedAt(childComplexity), true

	case "LeagueParticipant.leagueId":
		if e.complexity.LeagueParticipant.LeagueID == nil {
			break
		}

		return e.complexity.LeagueParticipant.LeagueID(childComplexity), true

	case "LeagueParticipant.registrationData":
		if e.complexity.LeagueParticipant.RegistrationData == nil {
			break
		}

		return e.complexity.LeagueParticipant.RegistrationData(childComplexity), true

	case "LeagueParticipant.userId":
		if e.complexity.LeagueParticipant.UserID == nil {
			break
		}

		return e.complexity.LeagueParticipant.UserID(childComplexity), true

	case "Mathlete.bestStreak":
		if e.complexity.Mathlete.BestStreak == nil {
			break
		}

		return e.complexity.Mathlete.BestStreak(childComplexity), true

	case "Mathlete.bestTime":
		if e.complexity.Mathlete.BestTime == nil {
			break
		}

		return e.complexity.Mathlete.BestTime(childComplexity), true

	case "Mathlete.numOfCorrectSubmissions":
		if e.complexity.Mathlete.NumOfCorrectSubmissions == nil {
			break
		}

		return e.complexity.Mathlete.NumOfCorrectSubmissions(childComplexity), true

	case "Mathlete.questionsSolved":
		if e.complexity.Mathlete.QuestionsSolved == nil {
			break
		}

		return e.complexity.Mathlete.QuestionsSolved(childComplexity), true

	case "Mathlete.userId":
		if e.complexity.Mathlete.UserID == nil {
			break
		}

		return e.complexity.Mathlete.UserID(childComplexity), true

	case "Message.attachment":
		if e.complexity.Message.Attachment == nil {
			break
		}

		return e.complexity.Message.Attachment(childComplexity), true

	case "Message.content":
		if e.complexity.Message.Content == nil {
			break
		}

		return e.complexity.Message.Content(childComplexity), true

	case "Message.createdAt":
		if e.complexity.Message.CreatedAt == nil {
			break
		}

		return e.complexity.Message.CreatedAt(childComplexity), true

	case "Message.groupId":
		if e.complexity.Message.GroupID == nil {
			break
		}

		return e.complexity.Message.GroupID(childComplexity), true

	case "Message._id":
		if e.complexity.Message.ID == nil {
			break
		}

		return e.complexity.Message.ID(childComplexity), true

	case "Message.sender":
		if e.complexity.Message.Sender == nil {
			break
		}

		return e.complexity.Message.Sender(childComplexity), true

	case "Message.senderInfo":
		if e.complexity.Message.SenderInfo == nil {
			break
		}

		return e.complexity.Message.SenderInfo(childComplexity), true

	case "MessageGroup.alias":
		if e.complexity.MessageGroup.Alias == nil {
			break
		}

		return e.complexity.MessageGroup.Alias(childComplexity), true

	case "MessageGroup.createdAt":
		if e.complexity.MessageGroup.CreatedAt == nil {
			break
		}

		return e.complexity.MessageGroup.CreatedAt(childComplexity), true

	case "MessageGroup.deepLinkRoute":
		if e.complexity.MessageGroup.DeepLinkRoute == nil {
			break
		}

		return e.complexity.MessageGroup.DeepLinkRoute(childComplexity), true

	case "MessageGroup.groupName":
		if e.complexity.MessageGroup.GroupName == nil {
			break
		}

		return e.complexity.MessageGroup.GroupName(childComplexity), true

	case "MessageGroup.groupType":
		if e.complexity.MessageGroup.GroupType == nil {
			break
		}

		return e.complexity.MessageGroup.GroupType(childComplexity), true

	case "MessageGroup._id":
		if e.complexity.MessageGroup.ID == nil {
			break
		}

		return e.complexity.MessageGroup.ID(childComplexity), true

	case "MessageGroup.lastMessage":
		if e.complexity.MessageGroup.LastMessage == nil {
			break
		}

		return e.complexity.MessageGroup.LastMessage(childComplexity), true

	case "MessageGroup.lastMessageRead":
		if e.complexity.MessageGroup.LastMessageRead == nil {
			break
		}

		return e.complexity.MessageGroup.LastMessageRead(childComplexity), true

	case "MessageGroup.members":
		if e.complexity.MessageGroup.Members == nil {
			break
		}

		return e.complexity.MessageGroup.Members(childComplexity), true

	case "MessageGroup.messages":
		if e.complexity.MessageGroup.Messages == nil {
			break
		}

		return e.complexity.MessageGroup.Messages(childComplexity), true

	case "MessageGroup.updatedAt":
		if e.complexity.MessageGroup.UpdatedAt == nil {
			break
		}

		return e.complexity.MessageGroup.UpdatedAt(childComplexity), true

	case "MessageGroup.userInfoIfIndividual":
		if e.complexity.MessageGroup.UserInfoIfIndividual == nil {
			break
		}

		return e.complexity.MessageGroup.UserInfoIfIndividual(childComplexity), true

	case "MessageRead.lastMessageRead":
		if e.complexity.MessageRead.LastMessageRead == nil {
			break
		}

		return e.complexity.MessageRead.LastMessageRead(childComplexity), true

	case "MessageRead.userId":
		if e.complexity.MessageRead.UserID == nil {
			break
		}

		return e.complexity.MessageRead.UserID(childComplexity), true

	case "MinifiedGame.config":
		if e.complexity.MinifiedGame.Config == nil {
			break
		}

		return e.complexity.MinifiedGame.Config(childComplexity), true

	case "MinifiedGame.endTime":
		if e.complexity.MinifiedGame.EndTime == nil {
			break
		}

		return e.complexity.MinifiedGame.EndTime(childComplexity), true

	case "MinifiedGame._id":
		if e.complexity.MinifiedGame.ID == nil {
			break
		}

		return e.complexity.MinifiedGame.ID(childComplexity), true

	case "MinifiedGame.leaderBoard":
		if e.complexity.MinifiedGame.LeaderBoard == nil {
			break
		}

		return e.complexity.MinifiedGame.LeaderBoard(childComplexity), true

	case "MinifiedGame.players":
		if e.complexity.MinifiedGame.Players == nil {
			break
		}

		return e.complexity.MinifiedGame.Players(childComplexity), true

	case "MinifiedGame.startTime":
		if e.complexity.MinifiedGame.StartTime == nil {
			break
		}

		return e.complexity.MinifiedGame.StartTime(childComplexity), true

	case "MinifiedPuzzleGame.config":
		if e.complexity.MinifiedPuzzleGame.Config == nil {
			break
		}

		return e.complexity.MinifiedPuzzleGame.Config(childComplexity), true

	case "MinifiedPuzzleGame.endTime":
		if e.complexity.MinifiedPuzzleGame.EndTime == nil {
			break
		}

		return e.complexity.MinifiedPuzzleGame.EndTime(childComplexity), true

	case "MinifiedPuzzleGame._id":
		if e.complexity.MinifiedPuzzleGame.ID == nil {
			break
		}

		return e.complexity.MinifiedPuzzleGame.ID(childComplexity), true

	case "MinifiedPuzzleGame.leaderBoard":
		if e.complexity.MinifiedPuzzleGame.LeaderBoard == nil {
			break
		}

		return e.complexity.MinifiedPuzzleGame.LeaderBoard(childComplexity), true

	case "MinifiedPuzzleGame.players":
		if e.complexity.MinifiedPuzzleGame.Players == nil {
			break
		}

		return e.complexity.MinifiedPuzzleGame.Players(childComplexity), true

	case "MinifiedPuzzleGame.startTime":
		if e.complexity.MinifiedPuzzleGame.StartTime == nil {
			break
		}

		return e.complexity.MinifiedPuzzleGame.StartTime(childComplexity), true

	case "Mutation.abortSearching":
		if e.complexity.Mutation.AbortSearching == nil {
			break
		}

		return e.complexity.Mutation.AbortSearching(childComplexity), true

	case "Mutation.abortSearchingForPuzzleGame":
		if e.complexity.Mutation.AbortSearchingForPuzzleGame == nil {
			break
		}

		return e.complexity.Mutation.AbortSearchingForPuzzleGame(childComplexity), true

	case "Mutation.acceptChallenge":
		if e.complexity.Mutation.AcceptChallenge == nil {
			break
		}

		args, err := ec.field_Mutation_acceptChallenge_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.AcceptChallenge(childComplexity, args["gameId"].(primitive.ObjectID)), true

	case "Mutation.acceptChallengeOfPuzzleGame":
		if e.complexity.Mutation.AcceptChallengeOfPuzzleGame == nil {
			break
		}

		args, err := ec.field_Mutation_acceptChallengeOfPuzzleGame_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.AcceptChallengeOfPuzzleGame(childComplexity, args["gameId"].(primitive.ObjectID)), true

	case "Mutation.acceptFriendRequest":
		if e.complexity.Mutation.AcceptFriendRequest == nil {
			break
		}

		args, err := ec.field_Mutation_acceptFriendRequest_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.AcceptFriendRequest(childComplexity, args["acceptRequestInput"].(*models.FriendRequestInput)), true

	case "Mutation.acceptRematch":
		if e.complexity.Mutation.AcceptRematch == nil {
			break
		}

		args, err := ec.field_Mutation_acceptRematch_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.AcceptRematch(childComplexity, args["gameId"].(primitive.ObjectID)), true

	case "Mutation.acceptRematchOfPuzzleGame":
		if e.complexity.Mutation.AcceptRematchOfPuzzleGame == nil {
			break
		}

		args, err := ec.field_Mutation_acceptRematchOfPuzzleGame_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.AcceptRematchOfPuzzleGame(childComplexity, args["gameId"].(primitive.ObjectID)), true

	case "Mutation.addClubMember":
		if e.complexity.Mutation.AddClubMember == nil {
			break
		}

		args, err := ec.field_Mutation_addClubMember_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.AddClubMember(childComplexity, args["clubId"].(primitive.ObjectID), args["userId"].(primitive.ObjectID)), true

	case "Mutation.attemptDailyChallenge":
		if e.complexity.Mutation.AttemptDailyChallenge == nil {
			break
		}

		args, err := ec.field_Mutation_attemptDailyChallenge_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.AttemptDailyChallenge(childComplexity, args["challengeId"].(primitive.ObjectID)), true

	case "Mutation.cancelGame":
		if e.complexity.Mutation.CancelGame == nil {
			break
		}

		args, err := ec.field_Mutation_cancelGame_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.CancelGame(childComplexity, args["gameId"].(primitive.ObjectID)), true

	case "Mutation.cancelPuzzleGame":
		if e.complexity.Mutation.CancelPuzzleGame == nil {
			break
		}

		args, err := ec.field_Mutation_cancelPuzzleGame_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.CancelPuzzleGame(childComplexity, args["gameId"].(primitive.ObjectID)), true

	case "Mutation.cancelRematchRequest":
		if e.complexity.Mutation.CancelRematchRequest == nil {
			break
		}

		args, err := ec.field_Mutation_cancelRematchRequest_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.CancelRematchRequest(childComplexity, args["gameId"].(primitive.ObjectID)), true

	case "Mutation.challengeUser":
		if e.complexity.Mutation.ChallengeUser == nil {
			break
		}

		args, err := ec.field_Mutation_challengeUser_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.ChallengeUser(childComplexity, args["challengeUserInput"].(*models.ChallengeUserInput)), true

	case "Mutation.challengeUserForPuzzleGame":
		if e.complexity.Mutation.ChallengeUserForPuzzleGame == nil {
			break
		}

		args, err := ec.field_Mutation_challengeUserForPuzzleGame_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.ChallengeUserForPuzzleGame(childComplexity, args["challengeUserInput"].(*models.ChallengeUserForPuzzleGameInput)), true

	case "Mutation.createAnnouncement":
		if e.complexity.Mutation.CreateAnnouncement == nil {
			break
		}

		args, err := ec.field_Mutation_createAnnouncement_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.CreateAnnouncement(childComplexity, args["input"].(models.CreateAnnouncementInput)), true

	case "Mutation.createClub":
		if e.complexity.Mutation.CreateClub == nil {
			break
		}

		args, err := ec.field_Mutation_createClub_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.CreateClub(childComplexity, args["input"].(models.CreateClubInput)), true

	case "Mutation.createClubAnnouncement":
		if e.complexity.Mutation.CreateClubAnnouncement == nil {
			break
		}

		args, err := ec.field_Mutation_createClubAnnouncement_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.CreateClubAnnouncement(childComplexity, args["input"].(models.CreateClubAnnouncementInput)), true

	case "Mutation.createClubEvent":
		if e.complexity.Mutation.CreateClubEvent == nil {
			break
		}

		args, err := ec.field_Mutation_createClubEvent_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.CreateClubEvent(childComplexity, args["input"].(models.CreateClubEventInput)), true

	case "Mutation.createContest":
		if e.complexity.Mutation.CreateContest == nil {
			break
		}

		args, err := ec.field_Mutation_createContest_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.CreateContest(childComplexity, args["input"].(models.CreateContestInput)), true

	case "Mutation.createForum":
		if e.complexity.Mutation.CreateForum == nil {
			break
		}

		args, err := ec.field_Mutation_createForum_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.CreateForum(childComplexity, args["input"].(models.CreateForumInput)), true

	case "Mutation.createForumReply":
		if e.complexity.Mutation.CreateForumReply == nil {
			break
		}

		args, err := ec.field_Mutation_createForumReply_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.CreateForumReply(childComplexity, args["input"].(models.CreateForumReplyInput)), true

	case "Mutation.createForumThread":
		if e.complexity.Mutation.CreateForumThread == nil {
			break
		}

		args, err := ec.field_Mutation_createForumThread_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.CreateForumThread(childComplexity, args["input"].(models.CreateForumThreadInput)), true

	case "Mutation.createGame":
		if e.complexity.Mutation.CreateGame == nil {
			break
		}

		args, err := ec.field_Mutation_createGame_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.CreateGame(childComplexity, args["gameConfig"].(*models.GameConfigInput)), true

	case "Mutation.createInstitution":
		if e.complexity.Mutation.CreateInstitution == nil {
			break
		}

		args, err := ec.field_Mutation_createInstitution_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.CreateInstitution(childComplexity, args["input"].(models.CreateInstitutionInput)), true

	case "Mutation.createLeague":
		if e.complexity.Mutation.CreateLeague == nil {
			break
		}

		args, err := ec.field_Mutation_createLeague_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.CreateLeague(childComplexity, args["input"].(models.CreateLeagueInput)), true

	case "Mutation.createPuzzleGame":
		if e.complexity.Mutation.CreatePuzzleGame == nil {
			break
		}

		args, err := ec.field_Mutation_createPuzzleGame_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.CreatePuzzleGame(childComplexity, args["gameConfig"].(*models.PuzzleGameConfigInput)), true

	case "Mutation.createShowdown":
		if e.complexity.Mutation.CreateShowdown == nil {
			break
		}

		args, err := ec.field_Mutation_createShowdown_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.CreateShowdown(childComplexity, args["input"].(models.CreateShowdownInput)), true

	case "Mutation.createUser":
		if e.complexity.Mutation.CreateUser == nil {
			break
		}

		args, err := ec.field_Mutation_createUser_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.CreateUser(childComplexity, args["userInput"].(*models.UserInput)), true

	case "Mutation.deleteAnnouncement":
		if e.complexity.Mutation.DeleteAnnouncement == nil {
			break
		}

		args, err := ec.field_Mutation_deleteAnnouncement_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.DeleteAnnouncement(childComplexity, args["id"].(primitive.ObjectID)), true

	case "Mutation.deleteClub":
		if e.complexity.Mutation.DeleteClub == nil {
			break
		}

		args, err := ec.field_Mutation_deleteClub_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.DeleteClub(childComplexity, args["id"].(primitive.ObjectID)), true

	case "Mutation.deleteClubAnnouncement":
		if e.complexity.Mutation.DeleteClubAnnouncement == nil {
			break
		}

		args, err := ec.field_Mutation_deleteClubAnnouncement_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.DeleteClubAnnouncement(childComplexity, args["id"].(primitive.ObjectID)), true

	case "Mutation.deleteClubEvent":
		if e.complexity.Mutation.DeleteClubEvent == nil {
			break
		}

		args, err := ec.field_Mutation_deleteClubEvent_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.DeleteClubEvent(childComplexity, args["id"].(primitive.ObjectID)), true

	case "Mutation.deleteUser":
		if e.complexity.Mutation.DeleteUser == nil {
			break
		}

		return e.complexity.Mutation.DeleteUser(childComplexity), true

	case "Mutation.deleteUserSavedPreset":
		if e.complexity.Mutation.DeleteUserSavedPreset == nil {
			break
		}

		args, err := ec.field_Mutation_deleteUserSavedPreset_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.DeleteUserSavedPreset(childComplexity, args["presetId"].(primitive.ObjectID)), true

	case "Mutation.endAbilityDuelsGame":
		if e.complexity.Mutation.EndAbilityDuelsGame == nil {
			break
		}

		args, err := ec.field_Mutation_endAbilityDuelsGame_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.EndAbilityDuelsGame(childComplexity, args["gameId"].(*primitive.ObjectID)), true

	case "Mutation.endGame":
		if e.complexity.Mutation.EndGame == nil {
			break
		}

		args, err := ec.field_Mutation_endGame_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.EndGame(childComplexity, args["gameId"].(*primitive.ObjectID)), true

	case "Mutation.endGameForShowdown":
		if e.complexity.Mutation.EndGameForShowdown == nil {
			break
		}

		args, err := ec.field_Mutation_endGameForShowdown_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.EndGameForShowdown(childComplexity, args["gameId"].(*primitive.ObjectID)), true

	case "Mutation.endPuzzleGame":
		if e.complexity.Mutation.EndPuzzleGame == nil {
			break
		}

		args, err := ec.field_Mutation_endPuzzleGame_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.EndPuzzleGame(childComplexity, args["gameId"].(primitive.ObjectID)), true

	case "Mutation.followUser":
		if e.complexity.Mutation.FollowUser == nil {
			break
		}

		args, err := ec.field_Mutation_followUser_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.FollowUser(childComplexity, args["followUserInput"].(*models.FollowUserInput)), true

	case "Mutation.getUpdatedUserStreaks":
		if e.complexity.Mutation.GetUpdatedUserStreaks == nil {
			break
		}

		return e.complexity.Mutation.GetUpdatedUserStreaks(childComplexity), true

	case "Mutation.googleLogin":
		if e.complexity.Mutation.GoogleLogin == nil {
			break
		}

		args, err := ec.field_Mutation_googleLogin_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.GoogleLogin(childComplexity, args["auth_code"].(string), args["token_type"].(*string), args["expires_in"].(*string), args["guestId"].(*primitive.ObjectID)), true

	case "Mutation.joinClub":
		if e.complexity.Mutation.JoinClub == nil {
			break
		}

		args, err := ec.field_Mutation_joinClub_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.JoinClub(childComplexity, args["clubId"].(primitive.ObjectID)), true

	case "Mutation.joinClubEvent":
		if e.complexity.Mutation.JoinClubEvent == nil {
			break
		}

		args, err := ec.field_Mutation_joinClubEvent_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.JoinClubEvent(childComplexity, args["eventId"].(primitive.ObjectID)), true

	case "Mutation.joinGame":
		if e.complexity.Mutation.JoinGame == nil {
			break
		}

		args, err := ec.field_Mutation_joinGame_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.JoinGame(childComplexity, args["joinGameInput"].(*models.JoinGameInput)), true

	case "Mutation.joinLeague":
		if e.complexity.Mutation.JoinLeague == nil {
			break
		}

		args, err := ec.field_Mutation_joinLeague_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.JoinLeague(childComplexity, args["joinLeagueInput"].(*models.JoinLeagueInput)), true

	case "Mutation.joinPuzzleGame":
		if e.complexity.Mutation.JoinPuzzleGame == nil {
			break
		}

		args, err := ec.field_Mutation_joinPuzzleGame_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.JoinPuzzleGame(childComplexity, args["gameId"].(primitive.ObjectID)), true

	case "Mutation.joinVirtualContest":
		if e.complexity.Mutation.JoinVirtualContest == nil {
			break
		}

		args, err := ec.field_Mutation_joinVirtualContest_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.JoinVirtualContest(childComplexity, args["contestId"].(primitive.ObjectID)), true

	case "Mutation.leaveClub":
		if e.complexity.Mutation.LeaveClub == nil {
			break
		}

		args, err := ec.field_Mutation_leaveClub_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.LeaveClub(childComplexity, args["clubId"].(primitive.ObjectID)), true

	case "Mutation.leaveClubEvent":
		if e.complexity.Mutation.LeaveClubEvent == nil {
			break
		}

		args, err := ec.field_Mutation_leaveClubEvent_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.LeaveClubEvent(childComplexity, args["eventId"].(primitive.ObjectID)), true

	case "Mutation.leaveGame":
		if e.complexity.Mutation.LeaveGame == nil {
			break
		}

		args, err := ec.field_Mutation_leaveGame_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.LeaveGame(childComplexity, args["gameId"].(primitive.ObjectID)), true

	case "Mutation.leavePuzzleGame":
		if e.complexity.Mutation.LeavePuzzleGame == nil {
			break
		}

		args, err := ec.field_Mutation_leavePuzzleGame_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.LeavePuzzleGame(childComplexity, args["gameId"].(primitive.ObjectID)), true

	case "Mutation.legacyGoogleLogin":
		if e.complexity.Mutation.LegacyGoogleLogin == nil {
			break
		}

		args, err := ec.field_Mutation_legacyGoogleLogin_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.LegacyGoogleLogin(childComplexity, args["idToken"].(string), args["guestId"].(*primitive.ObjectID)), true

	case "Mutation.loginAsGuest":
		if e.complexity.Mutation.LoginAsGuest == nil {
			break
		}

		args, err := ec.field_Mutation_loginAsGuest_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.LoginAsGuest(childComplexity, args["guestId"].(primitive.ObjectID)), true

	case "Mutation.markAllAnnouncementsAsRead":
		if e.complexity.Mutation.MarkAllAnnouncementsAsRead == nil {
			break
		}

		return e.complexity.Mutation.MarkAllAnnouncementsAsRead(childComplexity), true

	case "Mutation.markAnnouncementAsRead":
		if e.complexity.Mutation.MarkAnnouncementAsRead == nil {
			break
		}

		args, err := ec.field_Mutation_markAnnouncementAsRead_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.MarkAnnouncementAsRead(childComplexity, args["announcementId"].(primitive.ObjectID)), true

	case "Mutation.registerDeviceToken":
		if e.complexity.Mutation.RegisterDeviceToken == nil {
			break
		}

		args, err := ec.field_Mutation_registerDeviceToken_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.RegisterDeviceToken(childComplexity, args["pushNotificationToken"].(string), args["deviceId"].(*string), args["platform"].(*string)), true

	case "Mutation.registerForContest":
		if e.complexity.Mutation.RegisterForContest == nil {
			break
		}

		args, err := ec.field_Mutation_registerForContest_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.RegisterForContest(childComplexity, args["input"].(models.RegistrationFormValuesInput)), true

	case "Mutation.registerForShowdown":
		if e.complexity.Mutation.RegisterForShowdown == nil {
			break
		}

		args, err := ec.field_Mutation_registerForShowdown_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.RegisterForShowdown(childComplexity, args["input"].(models.ShowdownRegistrationFormValuesInput)), true

	case "Mutation.rejectChallenge":
		if e.complexity.Mutation.RejectChallenge == nil {
			break
		}

		args, err := ec.field_Mutation_rejectChallenge_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.RejectChallenge(childComplexity, args["gameId"].(primitive.ObjectID)), true

	case "Mutation.rejectChallengeOfPuzzleGame":
		if e.complexity.Mutation.RejectChallengeOfPuzzleGame == nil {
			break
		}

		args, err := ec.field_Mutation_rejectChallengeOfPuzzleGame_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.RejectChallengeOfPuzzleGame(childComplexity, args["gameId"].(primitive.ObjectID)), true

	case "Mutation.rejectFriendRequest":
		if e.complexity.Mutation.RejectFriendRequest == nil {
			break
		}

		args, err := ec.field_Mutation_rejectFriendRequest_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.RejectFriendRequest(childComplexity, args["rejectRequestInput"].(*models.FriendRequestInput)), true

	case "Mutation.rejectRematch":
		if e.complexity.Mutation.RejectRematch == nil {
			break
		}

		args, err := ec.field_Mutation_rejectRematch_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.RejectRematch(childComplexity, args["gameId"].(primitive.ObjectID)), true

	case "Mutation.rejectRematchOfPuzzleGame":
		if e.complexity.Mutation.RejectRematchOfPuzzleGame == nil {
			break
		}

		args, err := ec.field_Mutation_rejectRematchOfPuzzleGame_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.RejectRematchOfPuzzleGame(childComplexity, args["gameId"].(primitive.ObjectID)), true

	case "Mutation.removeClubMember":
		if e.complexity.Mutation.RemoveClubMember == nil {
			break
		}

		args, err := ec.field_Mutation_removeClubMember_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.RemoveClubMember(childComplexity, args["clubId"].(primitive.ObjectID), args["userId"].(primitive.ObjectID)), true

	case "Mutation.removeFollower":
		if e.complexity.Mutation.RemoveFollower == nil {
			break
		}

		args, err := ec.field_Mutation_removeFollower_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.RemoveFollower(childComplexity, args["removeFollowerInput"].(*models.RemoveFollowerInput)), true

	case "Mutation.removeFriend":
		if e.complexity.Mutation.RemoveFriend == nil {
			break
		}

		args, err := ec.field_Mutation_removeFriend_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.RemoveFriend(childComplexity, args["removeFriendInput"].(*models.RemoveFriendInput)), true

	case "Mutation.removePlayer":
		if e.complexity.Mutation.RemovePlayer == nil {
			break
		}

		args, err := ec.field_Mutation_removePlayer_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.RemovePlayer(childComplexity, args["gameId"].(primitive.ObjectID), args["playerId"].(primitive.ObjectID)), true

	case "Mutation.removePlayerFromPuzzleGame":
		if e.complexity.Mutation.RemovePlayerFromPuzzleGame == nil {
			break
		}

		args, err := ec.field_Mutation_removePlayerFromPuzzleGame_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.RemovePlayerFromPuzzleGame(childComplexity, args["gameId"].(primitive.ObjectID), args["playerId"].(primitive.ObjectID)), true

	case "Mutation.requestRematch":
		if e.complexity.Mutation.RequestRematch == nil {
			break
		}

		args, err := ec.field_Mutation_requestRematch_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.RequestRematch(childComplexity, args["gameId"].(primitive.ObjectID)), true

	case "Mutation.requestRematchForPuzzleGame":
		if e.complexity.Mutation.RequestRematchForPuzzleGame == nil {
			break
		}

		args, err := ec.field_Mutation_requestRematchForPuzzleGame_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.RequestRematchForPuzzleGame(childComplexity, args["gameId"].(primitive.ObjectID)), true

	case "Mutation.saveUserPreset":
		if e.complexity.Mutation.SaveUserPreset == nil {
			break
		}

		args, err := ec.field_Mutation_saveUserPreset_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.SaveUserPreset(childComplexity, args["identifier"].(*string), args["name"].(*string)), true

	case "Mutation.sendFeedback":
		if e.complexity.Mutation.SendFeedback == nil {
			break
		}

		args, err := ec.field_Mutation_sendFeedback_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.SendFeedback(childComplexity, args["input"].(models.Feedback)), true

	case "Mutation.sendFriendRequest":
		if e.complexity.Mutation.SendFriendRequest == nil {
			break
		}

		args, err := ec.field_Mutation_sendFriendRequest_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.SendFriendRequest(childComplexity, args["sendRequestInput"].(*models.FriendRequestInput)), true

	case "Mutation.sendOTP":
		if e.complexity.Mutation.SendOtp == nil {
			break
		}

		args, err := ec.field_Mutation_sendOTP_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.SendOtp(childComplexity, args["email"].(string)), true

	case "Mutation.signInWithApple":
		if e.complexity.Mutation.SignInWithApple == nil {
			break
		}

		args, err := ec.field_Mutation_signInWithApple_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.SignInWithApple(childComplexity, args["input"].(models.AppleSignInInput)), true

	case "Mutation.startGame":
		if e.complexity.Mutation.StartGame == nil {
			break
		}

		args, err := ec.field_Mutation_startGame_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.StartGame(childComplexity, args["startGameInput"].(*models.StartGameInput)), true

	case "Mutation.startGameForShowdown":
		if e.complexity.Mutation.StartGameForShowdown == nil {
			break
		}

		args, err := ec.field_Mutation_startGameForShowdown_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.StartGameForShowdown(childComplexity, args["input"].(*models.StartGameForShowdownInput)), true

	case "Mutation.startPuzzleGame":
		if e.complexity.Mutation.StartPuzzleGame == nil {
			break
		}

		args, err := ec.field_Mutation_startPuzzleGame_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.StartPuzzleGame(childComplexity, args["gameId"].(primitive.ObjectID)), true

	case "Mutation.startSearching":
		if e.complexity.Mutation.StartSearching == nil {
			break
		}

		args, err := ec.field_Mutation_startSearching_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.StartSearching(childComplexity, args["gameConfig"].(*models.GameConfigInput)), true

	case "Mutation.startSearchingForPuzzleGame":
		if e.complexity.Mutation.StartSearchingForPuzzleGame == nil {
			break
		}

		args, err := ec.field_Mutation_startSearchingForPuzzleGame_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.StartSearchingForPuzzleGame(childComplexity, args["gameConfig"].(*models.PuzzleGameConfigInput)), true

	case "Mutation.submitAnswer":
		if e.complexity.Mutation.SubmitAnswer == nil {
			break
		}

		args, err := ec.field_Mutation_submitAnswer_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.SubmitAnswer(childComplexity, args["answerInput"].(*models.SubmitAnswerInput)), true

	case "Mutation.submitChallengeResult":
		if e.complexity.Mutation.SubmitChallengeResult == nil {
			break
		}

		args, err := ec.field_Mutation_submitChallengeResult_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.SubmitChallengeResult(childComplexity, args["input"].(models.SubmitSolutionInput)), true

	case "Mutation.submitContestAnswer":
		if e.complexity.Mutation.SubmitContestAnswer == nil {
			break
		}

		args, err := ec.field_Mutation_submitContestAnswer_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.SubmitContestAnswer(childComplexity, args["contestId"].(primitive.ObjectID), args["questionId"].(string), args["answer"].(string)), true

	case "Mutation.submitFlashAnzanAnswer":
		if e.complexity.Mutation.SubmitFlashAnzanAnswer == nil {
			break
		}

		args, err := ec.field_Mutation_submitFlashAnzanAnswer_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.SubmitFlashAnzanAnswer(childComplexity, args["answerInput"].(*models.SubmitFlashAnzanAnswerInput)), true

	case "Mutation.submitPuzzleGameAnswer":
		if e.complexity.Mutation.SubmitPuzzleGameAnswer == nil {
			break
		}

		args, err := ec.field_Mutation_submitPuzzleGameAnswer_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.SubmitPuzzleGameAnswer(childComplexity, args["answerInput"].(*models.SubmitPuzzleGameAnswerInput)), true

	case "Mutation.submitPuzzleGameRush":
		if e.complexity.Mutation.SubmitPuzzleGameRush == nil {
			break
		}

		args, err := ec.field_Mutation_submitPuzzleGameRush_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.SubmitPuzzleGameRush(childComplexity, args["input"].(models.SubmitPuzzleRushGame)), true

	case "Mutation.submitPuzzleSolution":
		if e.complexity.Mutation.SubmitPuzzleSolution == nil {
			break
		}

		args, err := ec.field_Mutation_submitPuzzleSolution_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.SubmitPuzzleSolution(childComplexity, args["puzzleId"].(primitive.ObjectID), args["timeSpent"].(int)), true

	case "Mutation.submitRatingFixtureResponses":
		if e.complexity.Mutation.SubmitRatingFixtureResponses == nil {
			break
		}

		args, err := ec.field_Mutation_submitRatingFixtureResponses_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.SubmitRatingFixtureResponses(childComplexity, args["submission"].([]*int), args["timeTaken"].(int)), true

	case "Mutation.submitReferral":
		if e.complexity.Mutation.SubmitReferral == nil {
			break
		}

		args, err := ec.field_Mutation_submitReferral_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.SubmitReferral(childComplexity, args["referralCode"].(string)), true

	case "Mutation.submitUserPresetResult":
		if e.complexity.Mutation.SubmitUserPresetResult == nil {
			break
		}

		args, err := ec.field_Mutation_submitUserPresetResult_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.SubmitUserPresetResult(childComplexity, args["userPresetResultInput"].(*models.UserPresetResultInput)), true

	case "Mutation.submitVirtualContestAnswer":
		if e.complexity.Mutation.SubmitVirtualContestAnswer == nil {
			break
		}

		args, err := ec.field_Mutation_submitVirtualContestAnswer_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.SubmitVirtualContestAnswer(childComplexity, args["contestId"].(primitive.ObjectID), args["questionId"].(string), args["answer"].(string)), true

	case "Mutation.takePledge":
		if e.complexity.Mutation.TakePledge == nil {
			break
		}

		args, err := ec.field_Mutation_takePledge_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.TakePledge(childComplexity, args["duration"].(*int)), true

	case "Mutation.unFollowUser":
		if e.complexity.Mutation.UnFollowUser == nil {
			break
		}

		args, err := ec.field_Mutation_unFollowUser_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.UnFollowUser(childComplexity, args["unFollowUserInput"].(*models.UnFollowUserInput)), true

	case "Mutation.unregisterDeviceToken":
		if e.complexity.Mutation.UnregisterDeviceToken == nil {
			break
		}

		args, err := ec.field_Mutation_unregisterDeviceToken_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.UnregisterDeviceToken(childComplexity, args["pushNotificationToken"].(*string), args["deviceId"].(*string)), true

	case "Mutation.unregisterFromContest":
		if e.complexity.Mutation.UnregisterFromContest == nil {
			break
		}

		args, err := ec.field_Mutation_unregisterFromContest_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.UnregisterFromContest(childComplexity, args["contestId"].(primitive.ObjectID)), true

	case "Mutation.unregisterFromShowdown":
		if e.complexity.Mutation.UnregisterFromShowdown == nil {
			break
		}

		args, err := ec.field_Mutation_unregisterFromShowdown_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.UnregisterFromShowdown(childComplexity, args["showdownId"].(primitive.ObjectID)), true

	case "Mutation.updateAnnouncement":
		if e.complexity.Mutation.UpdateAnnouncement == nil {
			break
		}

		args, err := ec.field_Mutation_updateAnnouncement_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.UpdateAnnouncement(childComplexity, args["id"].(primitive.ObjectID), args["input"].(models.UpdateAnnouncementInput)), true

	case "Mutation.updateClub":
		if e.complexity.Mutation.UpdateClub == nil {
			break
		}

		args, err := ec.field_Mutation_updateClub_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.UpdateClub(childComplexity, args["input"].(models.UpdateClubInput)), true

	case "Mutation.updateClubEvent":
		if e.complexity.Mutation.UpdateClubEvent == nil {
			break
		}

		args, err := ec.field_Mutation_updateClubEvent_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.UpdateClubEvent(childComplexity, args["input"].(models.UpdateClubEventInput)), true

	case "Mutation.updateContestParticipantStartTime":
		if e.complexity.Mutation.UpdateContestParticipantStartTime == nil {
			break
		}

		args, err := ec.field_Mutation_updateContestParticipantStartTime_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.UpdateContestParticipantStartTime(childComplexity, args["contestId"].(primitive.ObjectID)), true

	case "Mutation.updateLastMessageRead":
		if e.complexity.Mutation.UpdateLastMessageRead == nil {
			break
		}

		args, err := ec.field_Mutation_updateLastMessageRead_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.UpdateLastMessageRead(childComplexity, args["groupId"].(primitive.ObjectID), args["lastMessageReadId"].(primitive.ObjectID)), true

	case "Mutation.updateLastReadFeedId":
		if e.complexity.Mutation.UpdateLastReadFeedID == nil {
			break
		}

		args, err := ec.field_Mutation_updateLastReadFeedId_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.UpdateLastReadFeedID(childComplexity, args["lastReadFeedId"].(primitive.ObjectID)), true

	case "Mutation.updateLikeStatus":
		if e.complexity.Mutation.UpdateLikeStatus == nil {
			break
		}

		args, err := ec.field_Mutation_updateLikeStatus_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.UpdateLikeStatus(childComplexity, args["feedId"].(primitive.ObjectID)), true

	case "Mutation.updateMemberRole":
		if e.complexity.Mutation.UpdateMemberRole == nil {
			break
		}

		args, err := ec.field_Mutation_updateMemberRole_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.UpdateMemberRole(childComplexity, args["clubId"].(primitive.ObjectID), args["userId"].(primitive.ObjectID), args["role"].(models.ClubMemberRole)), true

	case "Mutation.updateRatingBasedOnFixtureResponse":
		if e.complexity.Mutation.UpdateRatingBasedOnFixtureResponse == nil {
			break
		}

		args, err := ec.field_Mutation_updateRatingBasedOnFixtureResponse_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.UpdateRatingBasedOnFixtureResponse(childComplexity, args["userStance"].(models.UserStance)), true

	case "Mutation.updateUser":
		if e.complexity.Mutation.UpdateUser == nil {
			break
		}

		args, err := ec.field_Mutation_updateUser_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.UpdateUser(childComplexity, args["updateUserInput"].(*models.UpdateUserInput)), true

	case "Mutation.updateUserSettings":
		if e.complexity.Mutation.UpdateUserSettings == nil {
			break
		}

		args, err := ec.field_Mutation_updateUserSettings_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.UpdateUserSettings(childComplexity, args["settings"].(*models.UpdateSettingsInput)), true

	case "Mutation.uploadClubBannerImage":
		if e.complexity.Mutation.UploadClubBannerImage == nil {
			break
		}

		args, err := ec.field_Mutation_uploadClubBannerImage_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.UploadClubBannerImage(childComplexity, args["file"].(graphql.Upload), args["clubId"].(primitive.ObjectID)), true

	case "Mutation.uploadClubLogoImage":
		if e.complexity.Mutation.UploadClubLogoImage == nil {
			break
		}

		args, err := ec.field_Mutation_uploadClubLogoImage_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.UploadClubLogoImage(childComplexity, args["file"].(graphql.Upload), args["clubId"].(primitive.ObjectID)), true

	case "Mutation.uploadFiles":
		if e.complexity.Mutation.UploadFiles == nil {
			break
		}

		args, err := ec.field_Mutation_uploadFiles_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.UploadFiles(childComplexity, args["files"].([]*graphql.Upload)), true

	case "Mutation.uploadProfilePicture":
		if e.complexity.Mutation.UploadProfilePicture == nil {
			break
		}

		args, err := ec.field_Mutation_uploadProfilePicture_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.UploadProfilePicture(childComplexity, args["file"].(graphql.Upload)), true

	case "Mutation.useStreakFreezer":
		if e.complexity.Mutation.UseStreakFreezer == nil {
			break
		}

		return e.complexity.Mutation.UseStreakFreezer(childComplexity), true

	case "Mutation.verifyOTP":
		if e.complexity.Mutation.VerifyOtp == nil {
			break
		}

		args, err := ec.field_Mutation_verifyOTP_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.VerifyOtp(childComplexity, args["otp"].(string)), true

	case "Mutation.withdrawFriendRequest":
		if e.complexity.Mutation.WithdrawFriendRequest == nil {
			break
		}

		args, err := ec.field_Mutation_withdrawFriendRequest_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.WithdrawFriendRequest(childComplexity, args["withdrawFriendRequestInput"].(*models.WithdrawFriendRequestInput)), true

	case "MyInstituteUsersPage.hasMore":
		if e.complexity.MyInstituteUsersPage.HasMore == nil {
			break
		}

		return e.complexity.MyInstituteUsersPage.HasMore(childComplexity), true

	case "MyInstituteUsersPage.pageNumber":
		if e.complexity.MyInstituteUsersPage.PageNumber == nil {
			break
		}

		return e.complexity.MyInstituteUsersPage.PageNumber(childComplexity), true

	case "MyInstituteUsersPage.pageSize":
		if e.complexity.MyInstituteUsersPage.PageSize == nil {
			break
		}

		return e.complexity.MyInstituteUsersPage.PageSize(childComplexity), true

	case "MyInstituteUsersPage.results":
		if e.complexity.MyInstituteUsersPage.Results == nil {
			break
		}

		return e.complexity.MyInstituteUsersPage.Results(childComplexity), true

	case "MyInstituteUsersPage.totalResults":
		if e.complexity.MyInstituteUsersPage.TotalResults == nil {
			break
		}

		return e.complexity.MyInstituteUsersPage.TotalResults(childComplexity), true

	case "OnlineUsersPage.hasMore":
		if e.complexity.OnlineUsersPage.HasMore == nil {
			break
		}

		return e.complexity.OnlineUsersPage.HasMore(childComplexity), true

	case "OnlineUsersPage.pageNumber":
		if e.complexity.OnlineUsersPage.PageNumber == nil {
			break
		}

		return e.complexity.OnlineUsersPage.PageNumber(childComplexity), true

	case "OnlineUsersPage.pageSize":
		if e.complexity.OnlineUsersPage.PageSize == nil {
			break
		}

		return e.complexity.OnlineUsersPage.PageSize(childComplexity), true

	case "OnlineUsersPage.totalResults":
		if e.complexity.OnlineUsersPage.TotalResults == nil {
			break
		}

		return e.complexity.OnlineUsersPage.TotalResults(childComplexity), true

	case "OnlineUsersPage.users":
		if e.complexity.OnlineUsersPage.Users == nil {
			break
		}

		return e.complexity.OnlineUsersPage.Users(childComplexity), true

	case "PageInfo.endCursor":
		if e.complexity.PageInfo.EndCursor == nil {
			break
		}

		return e.complexity.PageInfo.EndCursor(childComplexity), true

	case "PageInfo.hasNextPage":
		if e.complexity.PageInfo.HasNextPage == nil {
			break
		}

		return e.complexity.PageInfo.HasNextPage(childComplexity), true

	case "PaginatedContests.contests":
		if e.complexity.PaginatedContests.Contests == nil {
			break
		}

		return e.complexity.PaginatedContests.Contests(childComplexity), true

	case "PaginatedContests.totalCount":
		if e.complexity.PaginatedContests.TotalCount == nil {
			break
		}

		return e.complexity.PaginatedContests.TotalCount(childComplexity), true

	case "PaginatedLeaderboard.count":
		if e.complexity.PaginatedLeaderboard.Count == nil {
			break
		}

		return e.complexity.PaginatedLeaderboard.Count(childComplexity), true

	case "PaginatedLeaderboard.participants":
		if e.complexity.PaginatedLeaderboard.Participants == nil {
			break
		}

		return e.complexity.PaginatedLeaderboard.Participants(childComplexity), true

	case "PaginatedLeagues.league":
		if e.complexity.PaginatedLeagues.League == nil {
			break
		}

		return e.complexity.PaginatedLeagues.League(childComplexity), true

	case "PaginatedLeagues.totalCount":
		if e.complexity.PaginatedLeagues.TotalCount == nil {
			break
		}

		return e.complexity.PaginatedLeagues.TotalCount(childComplexity), true

	case "PaginatedMessage.hasMore":
		if e.complexity.PaginatedMessage.HasMore == nil {
			break
		}

		return e.complexity.PaginatedMessage.HasMore(childComplexity), true

	case "PaginatedMessage.lastMessageId":
		if e.complexity.PaginatedMessage.LastMessageID == nil {
			break
		}

		return e.complexity.PaginatedMessage.LastMessageID(childComplexity), true

	case "PaginatedMessage.messages":
		if e.complexity.PaginatedMessage.Messages == nil {
			break
		}

		return e.complexity.PaginatedMessage.Messages(childComplexity), true

	case "PaginatedMessageGroups.groups":
		if e.complexity.PaginatedMessageGroups.Groups == nil {
			break
		}

		return e.complexity.PaginatedMessageGroups.Groups(childComplexity), true

	case "PaginatedMessageGroups.hasMore":
		if e.complexity.PaginatedMessageGroups.HasMore == nil {
			break
		}

		return e.complexity.PaginatedMessageGroups.HasMore(childComplexity), true

	case "PaginatedMessageGroups.isRead":
		if e.complexity.PaginatedMessageGroups.IsRead == nil {
			break
		}

		return e.complexity.PaginatedMessageGroups.IsRead(childComplexity), true

	case "PaginatedMessageGroups.nextPage":
		if e.complexity.PaginatedMessageGroups.NextPage == nil {
			break
		}

		return e.complexity.PaginatedMessageGroups.NextPage(childComplexity), true

	case "PaginatedShowdowns.count":
		if e.complexity.PaginatedShowdowns.Count == nil {
			break
		}

		return e.complexity.PaginatedShowdowns.Count(childComplexity), true

	case "PaginatedShowdowns.showdowns":
		if e.complexity.PaginatedShowdowns.Showdowns == nil {
			break
		}

		return e.complexity.PaginatedShowdowns.Showdowns(childComplexity), true

	case "ParticipantBasicInfo._id":
		if e.complexity.ParticipantBasicInfo.ID == nil {
			break
		}

		return e.complexity.ParticipantBasicInfo.ID(childComplexity), true

	case "ParticipantBasicInfo.rounds":
		if e.complexity.ParticipantBasicInfo.Rounds == nil {
			break
		}

		return e.complexity.ParticipantBasicInfo.Rounds(childComplexity), true

	case "ParticipantBasicInfo.showdownId":
		if e.complexity.ParticipantBasicInfo.ShowdownID == nil {
			break
		}

		return e.complexity.ParticipantBasicInfo.ShowdownID(childComplexity), true

	case "ParticipantBasicInfo.userID":
		if e.complexity.ParticipantBasicInfo.UserID == nil {
			break
		}

		return e.complexity.ParticipantBasicInfo.UserID(childComplexity), true

	case "ParticipantBasicInfo.userInfo":
		if e.complexity.ParticipantBasicInfo.UserInfo == nil {
			break
		}

		return e.complexity.ParticipantBasicInfo.UserInfo(childComplexity), true

	case "PhoneNumber.countryCode":
		if e.complexity.PhoneNumber.CountryCode == nil {
			break
		}

		return e.complexity.PhoneNumber.CountryCode(childComplexity), true

	case "PhoneNumber.number":
		if e.complexity.PhoneNumber.Number == nil {
			break
		}

		return e.complexity.PhoneNumber.Number(childComplexity), true

	case "PlatformStats.totalGames":
		if e.complexity.PlatformStats.TotalGames == nil {
			break
		}

		return e.complexity.PlatformStats.TotalGames(childComplexity), true

	case "PlatformStats.totalSignedInUsers":
		if e.complexity.PlatformStats.TotalSignedInUsers == nil {
			break
		}

		return e.complexity.PlatformStats.TotalSignedInUsers(childComplexity), true

	case "PlatformStats.totalUsers":
		if e.complexity.PlatformStats.TotalUsers == nil {
			break
		}

		return e.complexity.PlatformStats.TotalUsers(childComplexity), true

	case "PlayedPresets.avgTime":
		if e.complexity.PlayedPresets.AvgTime == nil {
			break
		}

		return e.complexity.PlayedPresets.AvgTime(childComplexity), true

	case "PlayedPresets.identifier":
		if e.complexity.PlayedPresets.Identifier == nil {
			break
		}

		return e.complexity.PlayedPresets.Identifier(childComplexity), true

	case "Player.rating":
		if e.complexity.Player.Rating == nil {
			break
		}

		return e.complexity.Player.Rating(childComplexity), true

	case "Player.statikCoins":
		if e.complexity.Player.StatikCoins == nil {
			break
		}

		return e.complexity.Player.StatikCoins(childComplexity), true

	case "Player.status":
		if e.complexity.Player.Status == nil {
			break
		}

		return e.complexity.Player.Status(childComplexity), true

	case "Player.timeLeft":
		if e.complexity.Player.TimeLeft == nil {
			break
		}

		return e.complexity.Player.TimeLeft(childComplexity), true

	case "Player.userId":
		if e.complexity.Player.UserID == nil {
			break
		}

		return e.complexity.Player.UserID(childComplexity), true

	case "PlayerSetting.maxRating":
		if e.complexity.PlayerSetting.MaxRating == nil {
			break
		}

		return e.complexity.PlayerSetting.MaxRating(childComplexity), true

	case "PlayerSetting.minRating":
		if e.complexity.PlayerSetting.MinRating == nil {
			break
		}

		return e.complexity.PlayerSetting.MinRating(childComplexity), true

	case "Puzzle.availableAnswers":
		if e.complexity.Puzzle.AvailableAnswers == nil {
			break
		}

		return e.complexity.Puzzle.AvailableAnswers(childComplexity), true

	case "Puzzle.cells":
		if e.complexity.Puzzle.Cells == nil {
			break
		}

		return e.complexity.Puzzle.Cells(childComplexity), true

	case "Puzzle.currentUserResult":
		if e.complexity.Puzzle.CurrentUserResult == nil {
			break
		}

		return e.complexity.Puzzle.CurrentUserResult(childComplexity), true

	case "Puzzle.difficulty":
		if e.complexity.Puzzle.Difficulty == nil {
			break
		}

		return e.complexity.Puzzle.Difficulty(childComplexity), true

	case "Puzzle.hasAttempted":
		if e.complexity.Puzzle.HasAttempted == nil {
			break
		}

		return e.complexity.Puzzle.HasAttempted(childComplexity), true

	case "Puzzle.id":
		if e.complexity.Puzzle.ID == nil {
			break
		}

		return e.complexity.Puzzle.ID(childComplexity), true

	case "Puzzle.puzzleDate":
		if e.complexity.Puzzle.PuzzleDate == nil {
			break
		}

		return e.complexity.Puzzle.PuzzleDate(childComplexity), true

	case "Puzzle.puzzleType":
		if e.complexity.Puzzle.PuzzleType == nil {
			break
		}

		return e.complexity.Puzzle.PuzzleType(childComplexity), true

	case "Puzzle.solvedBy":
		if e.complexity.Puzzle.SolvedBy == nil {
			break
		}

		return e.complexity.Puzzle.SolvedBy(childComplexity), true

	case "Puzzle.stats":
		if e.complexity.Puzzle.Stats == nil {
			break
		}

		return e.complexity.Puzzle.Stats(childComplexity), true

	case "Puzzle.typeSpecific":
		if e.complexity.Puzzle.TypeSpecific == nil {
			break
		}

		return e.complexity.Puzzle.TypeSpecific(childComplexity), true

	case "Puzzle.userStat":
		if e.complexity.Puzzle.UserStat == nil {
			break
		}

		return e.complexity.Puzzle.UserStat(childComplexity), true

	case "PuzzleGame.config":
		if e.complexity.PuzzleGame.Config == nil {
			break
		}

		return e.complexity.PuzzleGame.Config(childComplexity), true

	case "PuzzleGame.createdBy":
		if e.complexity.PuzzleGame.CreatedBy == nil {
			break
		}

		return e.complexity.PuzzleGame.CreatedBy(childComplexity), true

	case "PuzzleGame.endTime":
		if e.complexity.PuzzleGame.EndTime == nil {
			break
		}

		return e.complexity.PuzzleGame.EndTime(childComplexity), true

	case "PuzzleGame.gameStatus":
		if e.complexity.PuzzleGame.GameStatus == nil {
			break
		}

		return e.complexity.PuzzleGame.GameStatus(childComplexity), true

	case "PuzzleGame.gameType":
		if e.complexity.PuzzleGame.GameType == nil {
			break
		}

		return e.complexity.PuzzleGame.GameType(childComplexity), true

	case "PuzzleGame._id":
		if e.complexity.PuzzleGame.ID == nil {
			break
		}

		return e.complexity.PuzzleGame.ID(childComplexity), true

	case "PuzzleGame.isRatedGame":
		if e.complexity.PuzzleGame.IsRatedGame == nil {
			break
		}

		return e.complexity.PuzzleGame.IsRatedGame(childComplexity), true

	case "PuzzleGame.leaderBoard":
		if e.complexity.PuzzleGame.LeaderBoard == nil {
			break
		}

		return e.complexity.PuzzleGame.LeaderBoard(childComplexity), true

	case "PuzzleGame.players":
		if e.complexity.PuzzleGame.Players == nil {
			break
		}

		return e.complexity.PuzzleGame.Players(childComplexity), true

	case "PuzzleGame.questions":
		if e.complexity.PuzzleGame.Questions == nil {
			break
		}

		return e.complexity.PuzzleGame.Questions(childComplexity), true

	case "PuzzleGame.rematchRequestedBy":
		if e.complexity.PuzzleGame.RematchRequestedBy == nil {
			break
		}

		return e.complexity.PuzzleGame.RematchRequestedBy(childComplexity), true

	case "PuzzleGame.seriesId":
		if e.complexity.PuzzleGame.SeriesID == nil {
			break
		}

		return e.complexity.PuzzleGame.SeriesID(childComplexity), true

	case "PuzzleGame.startTime":
		if e.complexity.PuzzleGame.StartTime == nil {
			break
		}

		return e.complexity.PuzzleGame.StartTime(childComplexity), true

	case "PuzzleGameConfig.difficultyLevel":
		if e.complexity.PuzzleGameConfig.DifficultyLevel == nil {
			break
		}

		return e.complexity.PuzzleGameConfig.DifficultyLevel(childComplexity), true

	case "PuzzleGameConfig.gameType":
		if e.complexity.PuzzleGameConfig.GameType == nil {
			break
		}

		return e.complexity.PuzzleGameConfig.GameType(childComplexity), true

	case "PuzzleGameConfig.maxTimePerQuestion":
		if e.complexity.PuzzleGameConfig.MaxTimePerQuestion == nil {
			break
		}

		return e.complexity.PuzzleGameConfig.MaxTimePerQuestion(childComplexity), true

	case "PuzzleGameConfig.numOfQuestions":
		if e.complexity.PuzzleGameConfig.NumOfQuestions == nil {
			break
		}

		return e.complexity.PuzzleGameConfig.NumOfQuestions(childComplexity), true

	case "PuzzleGameConfig.numPlayers":
		if e.complexity.PuzzleGameConfig.NumPlayers == nil {
			break
		}

		return e.complexity.PuzzleGameConfig.NumPlayers(childComplexity), true

	case "PuzzleGameConfig.timeLimit":
		if e.complexity.PuzzleGameConfig.TimeLimit == nil {
			break
		}

		return e.complexity.PuzzleGameConfig.TimeLimit(childComplexity), true

	case "PuzzleGameQuestion._id":
		if e.complexity.PuzzleGameQuestion.Id == nil {
			break
		}

		return e.complexity.PuzzleGameQuestion.Id(childComplexity), true

	case "PuzzleGameQuestion.question":
		if e.complexity.PuzzleGameQuestion.Question == nil {
			break
		}

		return e.complexity.PuzzleGameQuestion.Question(childComplexity), true

	case "PuzzleGameQuestion.stats":
		if e.complexity.PuzzleGameQuestion.Stats == nil {
			break
		}

		return e.complexity.PuzzleGameQuestion.Stats(childComplexity), true

	case "PuzzleGameQuestion.submissions":
		if e.complexity.PuzzleGameQuestion.Submissions == nil {
			break
		}

		return e.complexity.PuzzleGameQuestion.Submissions(childComplexity), true

	case "PuzzleGameQuestionStats.fastestTime":
		if e.complexity.PuzzleGameQuestionStats.FastestTime == nil {
			break
		}

		return e.complexity.PuzzleGameQuestionStats.FastestTime(childComplexity), true

	case "PuzzleGameQuestionStats.userIds":
		if e.complexity.PuzzleGameQuestionStats.UserIds == nil {
			break
		}

		return e.complexity.PuzzleGameQuestionStats.UserIds(childComplexity), true

	case "PuzzleLeaderboardEntry.correct":
		if e.complexity.PuzzleLeaderboardEntry.Correct == nil {
			break
		}

		return e.complexity.PuzzleLeaderboardEntry.Correct(childComplexity), true

	case "PuzzleLeaderboardEntry.incorrect":
		if e.complexity.PuzzleLeaderboardEntry.Incorrect == nil {
			break
		}

		return e.complexity.PuzzleLeaderboardEntry.Incorrect(childComplexity), true

	case "PuzzleLeaderboardEntry.rank":
		if e.complexity.PuzzleLeaderboardEntry.Rank == nil {
			break
		}

		return e.complexity.PuzzleLeaderboardEntry.Rank(childComplexity), true

	case "PuzzleLeaderboardEntry.ratingChange":
		if e.complexity.PuzzleLeaderboardEntry.RatingChange == nil {
			break
		}

		return e.complexity.PuzzleLeaderboardEntry.RatingChange(childComplexity), true

	case "PuzzleLeaderboardEntry.statikCoinsEarned":
		if e.complexity.PuzzleLeaderboardEntry.StatikCoinsEarned == nil {
			break
		}

		return e.complexity.PuzzleLeaderboardEntry.StatikCoinsEarned(childComplexity), true

	case "PuzzleLeaderboardEntry.totalPoints":
		if e.complexity.PuzzleLeaderboardEntry.TotalPoints == nil {
			break
		}

		return e.complexity.PuzzleLeaderboardEntry.TotalPoints(childComplexity), true

	case "PuzzleLeaderboardEntry.userId":
		if e.complexity.PuzzleLeaderboardEntry.UserID == nil {
			break
		}

		return e.complexity.PuzzleLeaderboardEntry.UserID(childComplexity), true

	case "PuzzleMonthlySubmissionReport.puzzleSubmissions":
		if e.complexity.PuzzleMonthlySubmissionReport.PuzzleSubmissions == nil {
			break
		}

		return e.complexity.PuzzleMonthlySubmissionReport.PuzzleSubmissions(childComplexity), true

	case "PuzzleMonthlySubmissionReport.yearMonth":
		if e.complexity.PuzzleMonthlySubmissionReport.YearMonth == nil {
			break
		}

		return e.complexity.PuzzleMonthlySubmissionReport.YearMonth(childComplexity), true

	case "PuzzleQuestionSubmission.inCorrectAttempts":
		if e.complexity.PuzzleQuestionSubmission.InCorrectAttempts == nil {
			break
		}

		return e.complexity.PuzzleQuestionSubmission.InCorrectAttempts(childComplexity), true

	case "PuzzleQuestionSubmission.isCorrect":
		if e.complexity.PuzzleQuestionSubmission.IsCorrect == nil {
			break
		}

		return e.complexity.PuzzleQuestionSubmission.IsCorrect(childComplexity), true

	case "PuzzleQuestionSubmission.points":
		if e.complexity.PuzzleQuestionSubmission.Points == nil {
			break
		}

		return e.complexity.PuzzleQuestionSubmission.Points(childComplexity), true

	case "PuzzleQuestionSubmission.submissionTime":
		if e.complexity.PuzzleQuestionSubmission.SubmissionTime == nil {
			break
		}

		return e.complexity.PuzzleQuestionSubmission.SubmissionTime(childComplexity), true

	case "PuzzleQuestionSubmission.submittedValues":
		if e.complexity.PuzzleQuestionSubmission.SubmittedValues == nil {
			break
		}

		return e.complexity.PuzzleQuestionSubmission.SubmittedValues(childComplexity), true

	case "PuzzleQuestionSubmission.timeTaken":
		if e.complexity.PuzzleQuestionSubmission.TimeTaken == nil {
			break
		}

		return e.complexity.PuzzleQuestionSubmission.TimeTaken(childComplexity), true

	case "PuzzleQuestionSubmission.userId":
		if e.complexity.PuzzleQuestionSubmission.UserID == nil {
			break
		}

		return e.complexity.PuzzleQuestionSubmission.UserID(childComplexity), true

	case "PuzzleResult.completedAt":
		if e.complexity.PuzzleResult.CompletedAt == nil {
			break
		}

		return e.complexity.PuzzleResult.CompletedAt(childComplexity), true

	case "PuzzleResult.id":
		if e.complexity.PuzzleResult.ID == nil {
			break
		}

		return e.complexity.PuzzleResult.ID(childComplexity), true

	case "PuzzleResult.puzzleDate":
		if e.complexity.PuzzleResult.PuzzleDate == nil {
			break
		}

		return e.complexity.PuzzleResult.PuzzleDate(childComplexity), true

	case "PuzzleResult.puzzleId":
		if e.complexity.PuzzleResult.PuzzleID == nil {
			break
		}

		return e.complexity.PuzzleResult.PuzzleID(childComplexity), true

	case "PuzzleResult.puzzleType":
		if e.complexity.PuzzleResult.PuzzleType == nil {
			break
		}

		return e.complexity.PuzzleResult.PuzzleType(childComplexity), true

	case "PuzzleResult.statikCoinsEarned":
		if e.complexity.PuzzleResult.StatikCoinsEarned == nil {
			break
		}

		return e.complexity.PuzzleResult.StatikCoinsEarned(childComplexity), true

	case "PuzzleResult.timeSpent":
		if e.complexity.PuzzleResult.TimeSpent == nil {
			break
		}

		return e.complexity.PuzzleResult.TimeSpent(childComplexity), true

	case "PuzzleResult.userId":
		if e.complexity.PuzzleResult.UserID == nil {
			break
		}

		return e.complexity.PuzzleResult.UserID(childComplexity), true

	case "PuzzleStats.averageTime":
		if e.complexity.PuzzleStats.AverageTime == nil {
			break
		}

		return e.complexity.PuzzleStats.AverageTime(childComplexity), true

	case "PuzzleStats.bestTime":
		if e.complexity.PuzzleStats.BestTime == nil {
			break
		}

		return e.complexity.PuzzleStats.BestTime(childComplexity), true

	case "PuzzleStats.numOfSubmission":
		if e.complexity.PuzzleStats.NumOfSubmission == nil {
			break
		}

		return e.complexity.PuzzleStats.NumOfSubmission(childComplexity), true

	case "PuzzleTypeSpecificDetails.crossMath":
		if e.complexity.PuzzleTypeSpecificDetails.CrossMath == nil {
			break
		}

		return e.complexity.PuzzleTypeSpecificDetails.CrossMath(childComplexity), true

	case "PuzzleTypeSpecificDetails.hectoc":
		if e.complexity.PuzzleTypeSpecificDetails.Hectoc == nil {
			break
		}

		return e.complexity.PuzzleTypeSpecificDetails.Hectoc(childComplexity), true

	case "PuzzleTypeSpecificDetails.kenKen":
		if e.complexity.PuzzleTypeSpecificDetails.KenKen == nil {
			break
		}

		return e.complexity.PuzzleTypeSpecificDetails.KenKen(childComplexity), true

	case "PuzzleTypeSpecificDetails.puzzleType":
		if e.complexity.PuzzleTypeSpecificDetails.PuzzleType == nil {
			break
		}

		return e.complexity.PuzzleTypeSpecificDetails.PuzzleType(childComplexity), true

	case "PuzzleUserStats.averageTime":
		if e.complexity.PuzzleUserStats.AverageTime == nil {
			break
		}

		return e.complexity.PuzzleUserStats.AverageTime(childComplexity), true

	case "PuzzleUserStats.bestTime":
		if e.complexity.PuzzleUserStats.BestTime == nil {
			break
		}

		return e.complexity.PuzzleUserStats.BestTime(childComplexity), true

	case "PuzzleUserStats.id":
		if e.complexity.PuzzleUserStats.ID == nil {
			break
		}

		return e.complexity.PuzzleUserStats.ID(childComplexity), true

	case "PuzzleUserStats.numOfSubmission":
		if e.complexity.PuzzleUserStats.NumOfSubmission == nil {
			break
		}

		return e.complexity.PuzzleUserStats.NumOfSubmission(childComplexity), true

	case "PuzzleUserStats.puzzleType":
		if e.complexity.PuzzleUserStats.PuzzleType == nil {
			break
		}

		return e.complexity.PuzzleUserStats.PuzzleType(childComplexity), true

	case "PuzzleUserStats.userId":
		if e.complexity.PuzzleUserStats.UserID == nil {
			break
		}

		return e.complexity.PuzzleUserStats.UserID(childComplexity), true

	case "Query.checkBotBehavior":
		if e.complexity.Query.CheckBotBehavior == nil {
			break
		}

		args, err := ec.field_Query_checkBotBehavior_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.CheckBotBehavior(childComplexity, args["challengeId"].(primitive.ObjectID), args["userId"].(primitive.ObjectID)), true

	case "Query.checkIfPledgeTaken":
		if e.complexity.Query.CheckIfPledgeTaken == nil {
			break
		}

		return e.complexity.Query.CheckIfPledgeTaken(childComplexity), true

	case "Query.checkUserStreakStatus":
		if e.complexity.Query.CheckUserStreakStatus == nil {
			break
		}

		return e.complexity.Query.CheckUserStreakStatus(childComplexity), true

	case "Query.club":
		if e.complexity.Query.Club == nil {
			break
		}

		args, err := ec.field_Query_club_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.Club(childComplexity, args["id"].(primitive.ObjectID)), true

	case "Query.clubAnnouncements":
		if e.complexity.Query.ClubAnnouncements == nil {
			break
		}

		args, err := ec.field_Query_clubAnnouncements_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.ClubAnnouncements(childComplexity, args["page"].(*int), args["pageSize"].(*int), args["clubId"].(*primitive.ObjectID), args["from"].(*time.Time), args["to"].(*time.Time)), true

	case "Query.clubEvent":
		if e.complexity.Query.ClubEvent == nil {
			break
		}

		args, err := ec.field_Query_clubEvent_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.ClubEvent(childComplexity, args["id"].(primitive.ObjectID)), true

	case "Query.clubEvents":
		if e.complexity.Query.ClubEvents == nil {
			break
		}

		args, err := ec.field_Query_clubEvents_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.ClubEvents(childComplexity, args["page"].(*int), args["pageSize"].(*int), args["clubId"].(*primitive.ObjectID), args["clubEventType"].(*models.ClubEventType), args["from"].(*time.Time), args["to"].(*time.Time)), true

	case "Query.clubMembers":
		if e.complexity.Query.ClubMembers == nil {
			break
		}

		args, err := ec.field_Query_clubMembers_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.ClubMembers(childComplexity, args["clubId"].(primitive.ObjectID), args["clubMembershipStatus"].(models.ClubMembershipStatus), args["page"].(*int), args["pageSize"].(*int)), true

	case "Query.clubs":
		if e.complexity.Query.Clubs == nil {
			break
		}

		args, err := ec.field_Query_clubs_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.Clubs(childComplexity, args["page"].(*int), args["pageSize"].(*int), args["visibility"].(*models.Visibility), args["search"].(*string)), true

	case "Query.forum":
		if e.complexity.Query.Forum == nil {
			break
		}

		args, err := ec.field_Query_forum_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.Forum(childComplexity, args["id"].(primitive.ObjectID)), true

	case "Query.forumReplies":
		if e.complexity.Query.ForumReplies == nil {
			break
		}

		args, err := ec.field_Query_forumReplies_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.ForumReplies(childComplexity, args["threadId"].(primitive.ObjectID), args["page"].(*int), args["pageSize"].(*int)), true

	case "Query.forumThread":
		if e.complexity.Query.ForumThread == nil {
			break
		}

		args, err := ec.field_Query_forumThread_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.ForumThread(childComplexity, args["id"].(primitive.ObjectID)), true

	case "Query.forumThreads":
		if e.complexity.Query.ForumThreads == nil {
			break
		}

		args, err := ec.field_Query_forumThreads_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.ForumThreads(childComplexity, args["forumId"].(primitive.ObjectID), args["page"].(*int), args["pageSize"].(*int)), true

	case "Query.forums":
		if e.complexity.Query.Forums == nil {
			break
		}

		args, err := ec.field_Query_forums_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.Forums(childComplexity, args["clubId"].(primitive.ObjectID), args["page"].(*int), args["pageSize"].(*int)), true

	case "Query.getAllAnnouncements":
		if e.complexity.Query.GetAllAnnouncements == nil {
			break
		}

		args, err := ec.field_Query_getAllAnnouncements_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetAllAnnouncements(childComplexity, args["limit"].(*int), args["offset"].(*int), args["type"].(*models.AnnouncementType)), true

	case "Query.getAllMessageGroups":
		if e.complexity.Query.GetAllMessageGroups == nil {
			break
		}

		args, err := ec.field_Query_getAllMessageGroups_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetAllMessageGroups(childComplexity, args["input"].(*models.GetAllMessageGroupsInput)), true

	case "Query.getAnnouncement":
		if e.complexity.Query.GetAnnouncement == nil {
			break
		}

		args, err := ec.field_Query_getAnnouncement_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetAnnouncement(childComplexity, args["id"].(primitive.ObjectID)), true

	case "Query.getClubLeaderboard":
		if e.complexity.Query.GetClubLeaderboard == nil {
			break
		}

		args, err := ec.field_Query_getClubLeaderboard_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetClubLeaderboard(childComplexity, args["clubId"].(primitive.ObjectID), args["page"].(*int), args["pageSize"].(*int)), true

	case "Query.getClubMemberInfo":
		if e.complexity.Query.GetClubMemberInfo == nil {
			break
		}

		args, err := ec.field_Query_getClubMemberInfo_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetClubMemberInfo(childComplexity, args["clubId"].(primitive.ObjectID)), true

	case "Query.getContestById":
		if e.complexity.Query.GetContestByID == nil {
			break
		}

		args, err := ec.field_Query_getContestById_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetContestByID(childComplexity, args["contestId"].(primitive.ObjectID)), true

	case "Query.getContestLeaderboard":
		if e.complexity.Query.GetContestLeaderboard == nil {
			break
		}

		args, err := ec.field_Query_getContestLeaderboard_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetContestLeaderboard(childComplexity, args["contestId"].(primitive.ObjectID), args["pageNumber"].(*int), args["pageSize"].(*int)), true

	case "Query.getContestsByStatus":
		if e.complexity.Query.GetContestsByStatus == nil {
			break
		}

		args, err := ec.field_Query_getContestsByStatus_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetContestsByStatus(childComplexity, args["statuses"].([]models.ContestStatus), args["page"].(*int), args["pageSize"].(*int), args["sortDirection"].(*string)), true

	case "Query.getCurrentUser":
		if e.complexity.Query.GetCurrentUser == nil {
			break
		}

		return e.complexity.Query.GetCurrentUser(childComplexity), true

	case "Query.getDailyChallenge":
		if e.complexity.Query.GetDailyChallenge == nil {
			break
		}

		return e.complexity.Query.GetDailyChallenge(childComplexity), true

	case "Query.getDailyChallengeById":
		if e.complexity.Query.GetDailyChallengeByID == nil {
			break
		}

		args, err := ec.field_Query_getDailyChallengeById_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetDailyChallengeByID(childComplexity, args["id"].(primitive.ObjectID)), true

	case "Query.getDailyChallengeLeaderboard":
		if e.complexity.Query.GetDailyChallengeLeaderboard == nil {
			break
		}

		args, err := ec.field_Query_getDailyChallengeLeaderboard_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetDailyChallengeLeaderboard(childComplexity, args["challengeNumber"].(*int), args["pageNumber"].(*int), args["pageSize"].(*int)), true

	case "Query.getDailyChallengeLeaderboardByDivision":
		if e.complexity.Query.GetDailyChallengeLeaderboardByDivision == nil {
			break
		}

		args, err := ec.field_Query_getDailyChallengeLeaderboardByDivision_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetDailyChallengeLeaderboardByDivision(childComplexity, args["dateStr"].(*string), args["division"].(*models.ChallengeDivision), args["pageNumber"].(*int), args["pageSize"].(*int)), true

	case "Query.getDailyChallengeLeaderboardByDivison":
		if e.complexity.Query.GetDailyChallengeLeaderboardByDivison == nil {
			break
		}

		args, err := ec.field_Query_getDailyChallengeLeaderboardByDivison_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetDailyChallengeLeaderboardByDivison(childComplexity, args["dateStr"].(*string), args["division"].(*models.ChallengeDivision), args["pageNumber"].(*int), args["pageSize"].(*int)), true

	case "Query.getDailyChallenges":
		if e.complexity.Query.GetDailyChallenges == nil {
			break
		}

		return e.complexity.Query.GetDailyChallenges(childComplexity), true

	case "Query.getDailyPuzzle":
		if e.complexity.Query.GetDailyPuzzle == nil {
			break
		}

		args, err := ec.field_Query_getDailyPuzzle_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetDailyPuzzle(childComplexity, args["date"].(string)), true

	case "Query.getDailyPuzzleByType":
		if e.complexity.Query.GetDailyPuzzleByType == nil {
			break
		}

		args, err := ec.field_Query_getDailyPuzzleByType_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetDailyPuzzleByType(childComplexity, args["date"].(string), args["puzzleType"].(models.PuzzleType)), true

	case "Query.getFeaturedContests":
		if e.complexity.Query.GetFeaturedContests == nil {
			break
		}

		return e.complexity.Query.GetFeaturedContests(childComplexity), true

	case "Query.getFeaturedShowdown":
		if e.complexity.Query.GetFeaturedShowdown == nil {
			break
		}

		return e.complexity.Query.GetFeaturedShowdown(childComplexity), true

	case "Query.getFicturesByShowdownId":
		if e.complexity.Query.GetFicturesByShowdownID == nil {
			break
		}

		args, err := ec.field_Query_getFicturesByShowdownId_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetFicturesByShowdownID(childComplexity, args["showdownId"].(primitive.ObjectID)), true

	case "Query.getFollowers":
		if e.complexity.Query.GetFollowers == nil {
			break
		}

		args, err := ec.field_Query_getFollowers_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetFollowers(childComplexity, args["page"].(*int), args["pageSize"].(*int)), true

	case "Query.getFollowings":
		if e.complexity.Query.GetFollowings == nil {
			break
		}

		args, err := ec.field_Query_getFollowings_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetFollowings(childComplexity, args["page"].(*int), args["pageSize"].(*int)), true

	case "Query.getFriends":
		if e.complexity.Query.GetFriends == nil {
			break
		}

		args, err := ec.field_Query_getFriends_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetFriends(childComplexity, args["page"].(*int), args["pageSize"].(*int), args["sortOption"].(*models.SortOptions)), true

	case "Query.getFriendsLeaderboard":
		if e.complexity.Query.GetFriendsLeaderboard == nil {
			break
		}

		args, err := ec.field_Query_getFriendsLeaderboard_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetFriendsLeaderboard(childComplexity, args["page"].(*int), args["pageSize"].(*int), args["ratingType"].(*string)), true

	case "Query.getFriendsTop5CrossMathPuzzleRushStats":
		if e.complexity.Query.GetFriendsTop5CrossMathPuzzleRushStats == nil {
			break
		}

		return e.complexity.Query.GetFriendsTop5CrossMathPuzzleRushStats(childComplexity), true

	case "Query.getFriendsTopPlayers":
		if e.complexity.Query.GetFriendsTopPlayers == nil {
			break
		}

		return e.complexity.Query.GetFriendsTopPlayers(childComplexity), true

	case "Query.getGameById":
		if e.complexity.Query.GetGameByID == nil {
			break
		}

		args, err := ec.field_Query_getGameById_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetGameByID(childComplexity, args["gameId"].(*primitive.ObjectID)), true

	case "Query.getGameDetailedAnalysis":
		if e.complexity.Query.GetGameDetailedAnalysis == nil {
			break
		}

		args, err := ec.field_Query_getGameDetailedAnalysis_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetGameDetailedAnalysis(childComplexity, args["gameId"].(*primitive.ObjectID)), true

	case "Query.getGameSeriesById":
		if e.complexity.Query.GetGameSeriesByID == nil {
			break
		}

		args, err := ec.field_Query_getGameSeriesById_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetGameSeriesByID(childComplexity, args["gameSeriesId"].(primitive.ObjectID)), true

	case "Query.getGamesByUser":
		if e.complexity.Query.GetGamesByUser == nil {
			break
		}

		args, err := ec.field_Query_getGamesByUser_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetGamesByUser(childComplexity, args["payload"].(*models.GetGamesInput)), true

	case "Query.getGlobalPresets":
		if e.complexity.Query.GetGlobalPresets == nil {
			break
		}

		args, err := ec.field_Query_getGlobalPresets_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetGlobalPresets(childComplexity, args["page"].(*int), args["pageSize"].(*int)), true

	case "Query.getGlobalPresetsByIdentifier":
		if e.complexity.Query.GetGlobalPresetsByIdentifier == nil {
			break
		}

		args, err := ec.field_Query_getGlobalPresetsByIdentifier_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetGlobalPresetsByIdentifier(childComplexity, args["identifier"].(*string)), true

	case "Query.getGlobalTop5CrossMathPuzzleRushStats":
		if e.complexity.Query.GetGlobalTop5CrossMathPuzzleRushStats == nil {
			break
		}

		return e.complexity.Query.GetGlobalTop5CrossMathPuzzleRushStats(childComplexity), true

	case "Query.getGlobalTopPlayers":
		if e.complexity.Query.GetGlobalTopPlayers == nil {
			break
		}

		return e.complexity.Query.GetGlobalTopPlayers(childComplexity), true

	case "Query.getLeague":
		if e.complexity.Query.GetLeague == nil {
			break
		}

		args, err := ec.field_Query_getLeague_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetLeague(childComplexity, args["id"].(primitive.ObjectID)), true

	case "Query.getLeagueLeaderboard":
		if e.complexity.Query.GetLeagueLeaderboard == nil {
			break
		}

		args, err := ec.field_Query_getLeagueLeaderboard_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetLeagueLeaderboard(childComplexity, args["leagueId"].(primitive.ObjectID), args["page"].(int), args["pageSize"].(int)), true

	case "Query.getLeaguesByStatus":
		if e.complexity.Query.GetLeaguesByStatus == nil {
			break
		}

		args, err := ec.field_Query_getLeaguesByStatus_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetLeaguesByStatus(childComplexity, args["statuses"].([]models.LeagueStatus), args["page"].(*int), args["pageSize"].(*int), args["sortDirection"].(*string)), true

	case "Query.getMessageGroupDetailsById":
		if e.complexity.Query.GetMessageGroupDetailsByID == nil {
			break
		}

		args, err := ec.field_Query_getMessageGroupDetailsById_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetMessageGroupDetailsByID(childComplexity, args["groupId"].(primitive.ObjectID)), true

	case "Query.getMessageGroupIdForFriends":
		if e.complexity.Query.GetMessageGroupIDForFriends == nil {
			break
		}

		args, err := ec.field_Query_getMessageGroupIdForFriends_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetMessageGroupIDForFriends(childComplexity, args["friendID"].(primitive.ObjectID)), true

	case "Query.getMessagesByGroupId":
		if e.complexity.Query.GetMessagesByGroupID == nil {
			break
		}

		args, err := ec.field_Query_getMessagesByGroupId_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetMessagesByGroupID(childComplexity, args["groupId"].(primitive.ObjectID), args["lastMessageId"].(*primitive.ObjectID), args["pageSize"].(*int), args["sortDirection"].(*models.SortDirection)), true

	case "Query.getMyCrossMathPuzzleRushStats":
		if e.complexity.Query.GetMyCrossMathPuzzleRushStats == nil {
			break
		}

		return e.complexity.Query.GetMyCrossMathPuzzleRushStats(childComplexity), true

	case "Query.getPaginatedLeaderboard":
		if e.complexity.Query.GetPaginatedLeaderboard == nil {
			break
		}

		args, err := ec.field_Query_getPaginatedLeaderboard_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetPaginatedLeaderboard(childComplexity, args["input"].(*models.PaginatedLeaderboardInput)), true

	case "Query.getPendingFriendRequests":
		if e.complexity.Query.GetPendingFriendRequests == nil {
			break
		}

		args, err := ec.field_Query_getPendingFriendRequests_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetPendingFriendRequests(childComplexity, args["page"].(*int), args["pageSize"].(*int)), true

	case "Query.getPlatformStats":
		if e.complexity.Query.GetPlatformStats == nil {
			break
		}

		return e.complexity.Query.GetPlatformStats(childComplexity), true

	case "Query.getPuzzleGameById":
		if e.complexity.Query.GetPuzzleGameByID == nil {
			break
		}

		args, err := ec.field_Query_getPuzzleGameById_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetPuzzleGameByID(childComplexity, args["gameId"].(primitive.ObjectID)), true

	case "Query.getPuzzleGamesByUser":
		if e.complexity.Query.GetPuzzleGamesByUser == nil {
			break
		}

		args, err := ec.field_Query_getPuzzleGamesByUser_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetPuzzleGamesByUser(childComplexity, args["payload"].(*models.GetPuzzleGamesInput)), true

	case "Query.getPuzzleSubmissionsByMonth":
		if e.complexity.Query.GetPuzzleSubmissionsByMonth == nil {
			break
		}

		args, err := ec.field_Query_getPuzzleSubmissionsByMonth_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetPuzzleSubmissionsByMonth(childComplexity, args["yearMonths"].([]string)), true

	case "Query.getPuzzleSubmissionsByMonthByType":
		if e.complexity.Query.GetPuzzleSubmissionsByMonthByType == nil {
			break
		}

		args, err := ec.field_Query_getPuzzleSubmissionsByMonthByType_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetPuzzleSubmissionsByMonthByType(childComplexity, args["yearMonths"].([]string), args["puzzleType"].(models.PuzzleType)), true

	case "Query.getRatingFixtureQuestions":
		if e.complexity.Query.GetRatingFixtureQuestions == nil {
			break
		}

		return e.complexity.Query.GetRatingFixtureQuestions(childComplexity), true

	case "Query.getRatingFixtureSubmission":
		if e.complexity.Query.GetRatingFixtureSubmission == nil {
			break
		}

		return e.complexity.Query.GetRatingFixtureSubmission(childComplexity), true

	case "Query.getRegisteredContests":
		if e.complexity.Query.GetRegisteredContests == nil {
			break
		}

		return e.complexity.Query.GetRegisteredContests(childComplexity), true

	case "Query.getShowdownById":
		if e.complexity.Query.GetShowdownByID == nil {
			break
		}

		args, err := ec.field_Query_getShowdownById_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetShowdownByID(childComplexity, args["showdownId"].(primitive.ObjectID)), true

	case "Query.getShowdownByStatus":
		if e.complexity.Query.GetShowdownByStatus == nil {
			break
		}

		args, err := ec.field_Query_getShowdownByStatus_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetShowdownByStatus(childComplexity, args["status"].(*models.ShowdownContestStatus)), true

	case "Query.getShowdownsByStatus":
		if e.complexity.Query.GetShowdownsByStatus == nil {
			break
		}

		args, err := ec.field_Query_getShowdownsByStatus_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetShowdownsByStatus(childComplexity, args["statuses"].([]models.ShowdownContestStatus), args["page"].(*int), args["pageSize"].(*int), args["sortDirection"].(*string)), true

	case "Query.getTimeSpentByUser":
		if e.complexity.Query.GetTimeSpentByUser == nil {
			break
		}

		args, err := ec.field_Query_getTimeSpentByUser_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetTimeSpentByUser(childComplexity, args["date"].(*string)), true

	case "Query.getUnreadAnnouncements":
		if e.complexity.Query.GetUnreadAnnouncements == nil {
			break
		}

		args, err := ec.field_Query_getUnreadAnnouncements_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetUnreadAnnouncements(childComplexity, args["limit"].(*int), args["offset"].(*int)), true

	case "Query.getUpcomingShowdown":
		if e.complexity.Query.GetUpcomingShowdown == nil {
			break
		}

		return e.complexity.Query.GetUpcomingShowdown(childComplexity), true

	case "Query.getUserById":
		if e.complexity.Query.GetUserByID == nil {
			break
		}

		args, err := ec.field_Query_getUserById_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetUserByID(childComplexity, args["userId"].(primitive.ObjectID)), true

	case "Query.getUserByUsername":
		if e.complexity.Query.GetUserByUsername == nil {
			break
		}

		args, err := ec.field_Query_getUserByUsername_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetUserByUsername(childComplexity, args["username"].(*string)), true

	case "Query.getUserContestResult":
		if e.complexity.Query.GetUserContestResult == nil {
			break
		}

		args, err := ec.field_Query_getUserContestResult_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetUserContestResult(childComplexity, args["contestId"].(primitive.ObjectID)), true

	case "Query.getUserContestSubmissions":
		if e.complexity.Query.GetUserContestSubmissions == nil {
			break
		}

		args, err := ec.field_Query_getUserContestSubmissions_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetUserContestSubmissions(childComplexity, args["userId"].(*primitive.ObjectID), args["contestId"].(primitive.ObjectID)), true

	case "Query.getUserFeeds":
		if e.complexity.Query.GetUserFeeds == nil {
			break
		}

		args, err := ec.field_Query_getUserFeeds_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetUserFeeds(childComplexity, args["lastId"].(*primitive.ObjectID), args["pageSize"].(*int)), true

	case "Query.getUserGamesByRatingType":
		if e.complexity.Query.GetUserGamesByRatingType == nil {
			break
		}

		args, err := ec.field_Query_getUserGamesByRatingType_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetUserGamesByRatingType(childComplexity, args["payload"].(*models.GetGamesByRatingInput)), true

	case "Query.getUserLeagueGroupLeaderboard":
		if e.complexity.Query.GetUserLeagueGroupLeaderboard == nil {
			break
		}

		args, err := ec.field_Query_getUserLeagueGroupLeaderboard_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetUserLeagueGroupLeaderboard(childComplexity, args["page"].(*int), args["pageSize"].(*int)), true

	case "Query.getUserPresetStatsByDate":
		if e.complexity.Query.GetUserPresetStatsByDate == nil {
			break
		}

		args, err := ec.field_Query_getUserPresetStatsByDate_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetUserPresetStatsByDate(childComplexity, args["username"].(*string), args["durationFilter"].(*int), args["identifier"].(*string)), true

	case "Query.getUserPresetsByIdentifier":
		if e.complexity.Query.GetUserPresetsByIdentifier == nil {
			break
		}

		args, err := ec.field_Query_getUserPresetsByIdentifier_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetUserPresetsByIdentifier(childComplexity, args["identifier"].(*string)), true

	case "Query.getUserPuzzleStats":
		if e.complexity.Query.GetUserPuzzleStats == nil {
			break
		}

		return e.complexity.Query.GetUserPuzzleStats(childComplexity), true

	case "Query.getUserPuzzleStatsByType":
		if e.complexity.Query.GetUserPuzzleStatsByType == nil {
			break
		}

		args, err := ec.field_Query_getUserPuzzleStatsByType_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetUserPuzzleStatsByType(childComplexity, args["puzzleType"].(models.PuzzleType)), true

	case "Query.getUserRecentPresets":
		if e.complexity.Query.GetUserRecentPresets == nil {
			break
		}

		return e.complexity.Query.GetUserRecentPresets(childComplexity), true

	case "Query.getUserResolution":
		if e.complexity.Query.GetUserResolution == nil {
			break
		}

		return e.complexity.Query.GetUserResolution(childComplexity), true

	case "Query.getUserResult":
		if e.complexity.Query.GetUserResult == nil {
			break
		}

		args, err := ec.field_Query_getUserResult_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetUserResult(childComplexity, args["challengeNumber"].(*int)), true

	case "Query.getUserResultByDailyChallengeId":
		if e.complexity.Query.GetUserResultByDailyChallengeID == nil {
			break
		}

		args, err := ec.field_Query_getUserResultByDailyChallengeId_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetUserResultByDailyChallengeID(childComplexity, args["challengeId"].(primitive.ObjectID)), true

	case "Query.getUserResultByDivision":
		if e.complexity.Query.GetUserResultByDivision == nil {
			break
		}

		args, err := ec.field_Query_getUserResultByDivision_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetUserResultByDivision(childComplexity, args["dateStr"].(*string), args["division"].(*models.ChallengeDivision)), true

	case "Query.getUserResultByDivison":
		if e.complexity.Query.GetUserResultByDivison == nil {
			break
		}

		args, err := ec.field_Query_getUserResultByDivison_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetUserResultByDivison(childComplexity, args["dateStr"].(*string), args["division"].(*models.ChallengeDivision)), true

	case "Query.getUserSavedPresets":
		if e.complexity.Query.GetUserSavedPresets == nil {
			break
		}

		args, err := ec.field_Query_getUserSavedPresets_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetUserSavedPresets(childComplexity, args["page"].(*int), args["pageSize"].(*int)), true

	case "Query.getUserSettings":
		if e.complexity.Query.GetUserSettings == nil {
			break
		}

		return e.complexity.Query.GetUserSettings(childComplexity), true

	case "Query.getUserStreakHistoryByMonth":
		if e.complexity.Query.GetUserStreakHistoryByMonth == nil {
			break
		}

		args, err := ec.field_Query_getUserStreakHistoryByMonth_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetUserStreakHistoryByMonth(childComplexity, args["yearMonths"].([]string)), true

	case "Query.getUserStreakShieldTransactions":
		if e.complexity.Query.GetUserStreakShieldTransactions == nil {
			break
		}

		args, err := ec.field_Query_getUserStreakShieldTransactions_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetUserStreakShieldTransactions(childComplexity, args["page"].(*int), args["pageSize"].(*int)), true

	case "Query.getUsersAllPlayedPresets":
		if e.complexity.Query.GetUsersAllPlayedPresets == nil {
			break
		}

		args, err := ec.field_Query_getUsersAllPlayedPresets_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetUsersAllPlayedPresets(childComplexity, args["username"].(*string)), true

	case "Query.getUsersOfMyInstitute":
		if e.complexity.Query.GetUsersOfMyInstitute == nil {
			break
		}

		args, err := ec.field_Query_getUsersOfMyInstitute_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetUsersOfMyInstitute(childComplexity, args["page"].(*int), args["pageSize"].(*int)), true

	case "Query.getUsersWeeklyStatikCoins":
		if e.complexity.Query.GetUsersWeeklyStatikCoins == nil {
			break
		}

		return e.complexity.Query.GetUsersWeeklyStatikCoins(childComplexity), true

	case "Query.getUsersWeeklyStatikCoinsV2":
		if e.complexity.Query.GetUsersWeeklyStatikCoinsV2 == nil {
			break
		}

		args, err := ec.field_Query_getUsersWeeklyStatikCoinsV2_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.GetUsersWeeklyStatikCoinsV2(childComplexity, args["userId"].(primitive.ObjectID)), true

	case "Query.isUsernameAvailable":
		if e.complexity.Query.IsUsernameAvailable == nil {
			break
		}

		args, err := ec.field_Query_isUsernameAvailable_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.IsUsernameAvailable(childComplexity, args["username"].(string)), true

	case "Query.leaderboard":
		if e.complexity.Query.Leaderboard == nil {
			break
		}

		args, err := ec.field_Query_leaderboard_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.Leaderboard(childComplexity, args["countryCode"].(*string), args["searchKey"].(*string), args["first"].(*int), args["after"].(*string)), true

	case "Query.leaderboardNew":
		if e.complexity.Query.LeaderboardNew == nil {
			break
		}

		args, err := ec.field_Query_leaderboardNew_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.LeaderboardNew(childComplexity, args["countryCode"].(*string), args["searchKey"].(*string), args["page"].(*int), args["limit"].(*int), args["ratingType"].(*string)), true

	case "Query.leaderboardV3":
		if e.complexity.Query.LeaderboardV3 == nil {
			break
		}

		args, err := ec.field_Query_leaderboardV3_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.LeaderboardV3(childComplexity, args["countryCode"].(*string), args["searchKey"].(*string), args["page"].(*int), args["limit"].(*int)), true

	case "Query.login":
		if e.complexity.Query.Login == nil {
			break
		}

		args, err := ec.field_Query_login_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.Login(childComplexity, args["email"].(string), args["password"].(string)), true

	case "Query.onlineUsers":
		if e.complexity.Query.OnlineUsers == nil {
			break
		}

		args, err := ec.field_Query_onlineUsers_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.OnlineUsers(childComplexity, args["page"].(int), args["pageSize"].(int)), true

	case "Query.searchInstitutions":
		if e.complexity.Query.SearchInstitutions == nil {
			break
		}

		args, err := ec.field_Query_searchInstitutions_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.SearchInstitutions(childComplexity, args["query"].(string), args["limit"].(*int)), true

	case "Query.searchUsersInMyInstitute":
		if e.complexity.Query.SearchUsersInMyInstitute == nil {
			break
		}

		args, err := ec.field_Query_searchUsersInMyInstitute_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.SearchUsersInMyInstitute(childComplexity, args["searchKey"].(*string), args["page"].(*int), args["pageSize"].(*int)), true

	case "Query.statikCoinsLeaderboard":
		if e.complexity.Query.StatikCoinsLeaderboard == nil {
			break
		}

		args, err := ec.field_Query_statikCoinsLeaderboard_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.StatikCoinsLeaderboard(childComplexity, args["page"].(int), args["pageSize"].(*int), args["leaderboardType"].(*models.StatikCoinLeaderboardType)), true

	case "Query.verifyToken":
		if e.complexity.Query.VerifyToken == nil {
			break
		}

		args, err := ec.field_Query_verifyToken_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.VerifyToken(childComplexity, args["token"].(string)), true

	case "Question.answers":
		if e.complexity.Question.Answers == nil {
			break
		}

		return e.complexity.Question.Answers(childComplexity), true

	case "Question.description":
		if e.complexity.Question.Description == nil {
			break
		}

		return e.complexity.Question.Description(childComplexity), true

	case "Question.expression":
		if e.complexity.Question.Expression == nil {
			break
		}

		return e.complexity.Question.Expression(childComplexity), true

	case "Question.fastestTimeTaken":
		if e.complexity.Question.FastestTimeTaken == nil {
			break
		}

		return e.complexity.Question.FastestTimeTaken(childComplexity), true

	case "Question.id":
		if e.complexity.Question.ID == nil {
			break
		}

		return e.complexity.Question.ID(childComplexity), true

	case "Question.maxTimeLimit":
		if e.complexity.Question.MaxTimeLimit == nil {
			break
		}

		return e.complexity.Question.MaxTimeLimit(childComplexity), true

	case "Question.options":
		if e.complexity.Question.Options == nil {
			break
		}

		return e.complexity.Question.Options(childComplexity), true

	case "Question.presetIdentifier":
		if e.complexity.Question.PresetIdentifier == nil {
			break
		}

		return e.complexity.Question.PresetIdentifier(childComplexity), true

	case "Question.questionType":
		if e.complexity.Question.QuestionType == nil {
			break
		}

		return e.complexity.Question.QuestionType(childComplexity), true

	case "Question.rating":
		if e.complexity.Question.Rating == nil {
			break
		}

		return e.complexity.Question.Rating(childComplexity), true

	case "Question.tags":
		if e.complexity.Question.Tags == nil {
			break
		}

		return e.complexity.Question.Tags(childComplexity), true

	case "RatingFixtureOutput.newRating":
		if e.complexity.RatingFixtureOutput.NewRating == nil {
			break
		}

		return e.complexity.RatingFixtureOutput.NewRating(childComplexity), true

	case "Referral._id":
		if e.complexity.Referral.ID == nil {
			break
		}

		return e.complexity.Referral.ID(childComplexity), true

	case "Referral.referredAt":
		if e.complexity.Referral.ReferredAt == nil {
			break
		}

		return e.complexity.Referral.ReferredAt(childComplexity), true

	case "Referral.referredTo":
		if e.complexity.Referral.ReferredTo == nil {
			break
		}

		return e.complexity.Referral.ReferredTo(childComplexity), true

	case "Referral.referrer":
		if e.complexity.Referral.Referrer == nil {
			break
		}

		return e.complexity.Referral.Referrer(childComplexity), true

	case "ReferralDetails.referral":
		if e.complexity.ReferralDetails.Referral == nil {
			break
		}

		return e.complexity.ReferralDetails.Referral(childComplexity), true

	case "ReferralDetails.referredUser":
		if e.complexity.ReferralDetails.ReferredUser == nil {
			break
		}

		return e.complexity.ReferralDetails.ReferredUser(childComplexity), true

	case "RegistrationFieldData.name":
		if e.complexity.RegistrationFieldData.Name == nil {
			break
		}

		return e.complexity.RegistrationFieldData.Name(childComplexity), true

	case "RegistrationFieldData.values":
		if e.complexity.RegistrationFieldData.Values == nil {
			break
		}

		return e.complexity.RegistrationFieldData.Values(childComplexity), true

	case "RegistrationForm.fields":
		if e.complexity.RegistrationForm.Fields == nil {
			break
		}

		return e.complexity.RegistrationForm.Fields(childComplexity), true

	case "RegistrationForm._id":
		if e.complexity.RegistrationForm.ID == nil {
			break
		}

		return e.complexity.RegistrationForm.ID(childComplexity), true

	case "RematchRequestOutput.gameId":
		if e.complexity.RematchRequestOutput.GameID == nil {
			break
		}

		return e.complexity.RematchRequestOutput.GameID(childComplexity), true

	case "RematchRequestOutput.gameType":
		if e.complexity.RematchRequestOutput.GameType == nil {
			break
		}

		return e.complexity.RematchRequestOutput.GameType(childComplexity), true

	case "RematchRequestOutput.newGameId":
		if e.complexity.RematchRequestOutput.NewGameID == nil {
			break
		}

		return e.complexity.RematchRequestOutput.NewGameID(childComplexity), true

	case "RematchRequestOutput.requestedBy":
		if e.complexity.RematchRequestOutput.RequestedBy == nil {
			break
		}

		return e.complexity.RematchRequestOutput.RequestedBy(childComplexity), true

	case "RematchRequestOutput.status":
		if e.complexity.RematchRequestOutput.Status == nil {
			break
		}

		return e.complexity.RematchRequestOutput.Status(childComplexity), true

	case "RematchRequestOutput.user":
		if e.complexity.RematchRequestOutput.User == nil {
			break
		}

		return e.complexity.RematchRequestOutput.User(childComplexity), true

	case "RematchRequestOutput.waitingTime":
		if e.complexity.RematchRequestOutput.WaitingTime == nil {
			break
		}

		return e.complexity.RematchRequestOutput.WaitingTime(childComplexity), true

	case "RepliesPage.hasMore":
		if e.complexity.RepliesPage.HasMore == nil {
			break
		}

		return e.complexity.RepliesPage.HasMore(childComplexity), true

	case "RepliesPage.pageNumber":
		if e.complexity.RepliesPage.PageNumber == nil {
			break
		}

		return e.complexity.RepliesPage.PageNumber(childComplexity), true

	case "RepliesPage.pageSize":
		if e.complexity.RepliesPage.PageSize == nil {
			break
		}

		return e.complexity.RepliesPage.PageSize(childComplexity), true

	case "RepliesPage.results":
		if e.complexity.RepliesPage.Results == nil {
			break
		}

		return e.complexity.RepliesPage.Results(childComplexity), true

	case "RepliesPage.totalResults":
		if e.complexity.RepliesPage.TotalResults == nil {
			break
		}

		return e.complexity.RepliesPage.TotalResults(childComplexity), true

	case "Result.challengeId":
		if e.complexity.Result.ChallengeID == nil {
			break
		}

		return e.complexity.Result.ChallengeID(childComplexity), true

	case "Result.completedAt":
		if e.complexity.Result.CompletedAt == nil {
			break
		}

		return e.complexity.Result.CompletedAt(childComplexity), true

	case "Result.score":
		if e.complexity.Result.Score == nil {
			break
		}

		return e.complexity.Result.Score(childComplexity), true

	case "Result.submittedTimes":
		if e.complexity.Result.SubmittedTimes == nil {
			break
		}

		return e.complexity.Result.SubmittedTimes(childComplexity), true

	case "Result.userId":
		if e.complexity.Result.UserID == nil {
			break
		}

		return e.complexity.Result.UserID(childComplexity), true

	case "RoundConfig.gameDuration":
		if e.complexity.RoundConfig.GameDuration == nil {
			break
		}

		return e.complexity.RoundConfig.GameDuration(childComplexity), true

	case "RoundConfig.gameType":
		if e.complexity.RoundConfig.GameType == nil {
			break
		}

		return e.complexity.RoundConfig.GameType(childComplexity), true

	case "RoundConfig.maxGapBwGame":
		if e.complexity.RoundConfig.MaxGapBwGame == nil {
			break
		}

		return e.complexity.RoundConfig.MaxGapBwGame(childComplexity), true

	case "RoundConfig.maxWaitTime":
		if e.complexity.RoundConfig.MaxWaitTime == nil {
			break
		}

		return e.complexity.RoundConfig.MaxWaitTime(childComplexity), true

	case "RoundConfig.numOfGames":
		if e.complexity.RoundConfig.NumOfGames == nil {
			break
		}

		return e.complexity.RoundConfig.NumOfGames(childComplexity), true

	case "RoundConfig.numOfPlayer":
		if e.complexity.RoundConfig.NumOfPlayer == nil {
			break
		}

		return e.complexity.RoundConfig.NumOfPlayer(childComplexity), true

	case "SearchSubscriptionOutput.event":
		if e.complexity.SearchSubscriptionOutput.Event == nil {
			break
		}

		return e.complexity.SearchSubscriptionOutput.Event(childComplexity), true

	case "SearchSubscriptionOutput.game":
		if e.complexity.SearchSubscriptionOutput.Game == nil {
			break
		}

		return e.complexity.SearchSubscriptionOutput.Game(childComplexity), true

	case "SearchSubscriptionOutput.opponent":
		if e.complexity.SearchSubscriptionOutput.Opponent == nil {
			break
		}

		return e.complexity.SearchSubscriptionOutput.Opponent(childComplexity), true

	case "SearchUserOutput.friendshipStatus":
		if e.complexity.SearchUserOutput.FriendshipStatus == nil {
			break
		}

		return e.complexity.SearchUserOutput.FriendshipStatus(childComplexity), true

	case "SearchUserOutput.isFollowing":
		if e.complexity.SearchUserOutput.IsFollowing == nil {
			break
		}

		return e.complexity.SearchUserOutput.IsFollowing(childComplexity), true

	case "SearchUserOutput.userPublicDetails":
		if e.complexity.SearchUserOutput.UserPublicDetails == nil {
			break
		}

		return e.complexity.SearchUserOutput.UserPublicDetails(childComplexity), true

	case "Showdown.clubId":
		if e.complexity.Showdown.ClubID == nil {
			break
		}

		return e.complexity.Showdown.ClubID(childComplexity), true

	case "Showdown.createdAt":
		if e.complexity.Showdown.CreatedAt == nil {
			break
		}

		return e.complexity.Showdown.CreatedAt(childComplexity), true

	case "Showdown.currentRound":
		if e.complexity.Showdown.CurrentRound == nil {
			break
		}

		return e.complexity.Showdown.CurrentRound(childComplexity), true

	case "Showdown.currentUserParticipation":
		if e.complexity.Showdown.CurrentUserParticipation == nil {
			break
		}

		return e.complexity.Showdown.CurrentUserParticipation(childComplexity), true

	case "Showdown.description":
		if e.complexity.Showdown.Description == nil {
			break
		}

		return e.complexity.Showdown.Description(childComplexity), true

	case "Showdown.details":
		if e.complexity.Showdown.Details == nil {
			break
		}

		return e.complexity.Showdown.Details(childComplexity), true

	case "Showdown.duration":
		if e.complexity.Showdown.Duration == nil {
			break
		}

		return e.complexity.Showdown.Duration(childComplexity), true

	case "Showdown.endTime":
		if e.complexity.Showdown.EndTime == nil {
			break
		}

		return e.complexity.Showdown.EndTime(childComplexity), true

	case "Showdown.gapBwRounds":
		if e.complexity.Showdown.GapBwRounds == nil {
			break
		}

		return e.complexity.Showdown.GapBwRounds(childComplexity), true

	case "Showdown.hostedBy":
		if e.complexity.Showdown.HostedBy == nil {
			break
		}

		return e.complexity.Showdown.HostedBy(childComplexity), true

	case "Showdown._id":
		if e.complexity.Showdown.ID == nil {
			break
		}

		return e.complexity.Showdown.ID(childComplexity), true

	case "Showdown.Instructions":
		if e.complexity.Showdown.Instructions == nil {
			break
		}

		return e.complexity.Showdown.Instructions(childComplexity), true

	case "Showdown.isRatedEvent":
		if e.complexity.Showdown.IsRatedEvent == nil {
			break
		}

		return e.complexity.Showdown.IsRatedEvent(childComplexity), true

	case "Showdown.name":
		if e.complexity.Showdown.Name == nil {
			break
		}

		return e.complexity.Showdown.Name(childComplexity), true

	case "Showdown.recentParticipants":
		if e.complexity.Showdown.RecentParticipants == nil {
			break
		}

		return e.complexity.Showdown.RecentParticipants(childComplexity), true

	case "Showdown.registrationCount":
		if e.complexity.Showdown.RegistrationCount == nil {
			break
		}

		return e.complexity.Showdown.RegistrationCount(childComplexity), true

	case "Showdown.registrationEndTime":
		if e.complexity.Showdown.RegistrationEndTime == nil {
			break
		}

		return e.complexity.Showdown.RegistrationEndTime(childComplexity), true

	case "Showdown.registrationForm":
		if e.complexity.Showdown.RegistrationForm == nil {
			break
		}

		return e.complexity.Showdown.RegistrationForm(childComplexity), true

	case "Showdown.registrationStartTime":
		if e.complexity.Showdown.RegistrationStartTime == nil {
			break
		}

		return e.complexity.Showdown.RegistrationStartTime(childComplexity), true

	case "Showdown.roundConfig":
		if e.complexity.Showdown.RoundConfig == nil {
			break
		}

		return e.complexity.Showdown.RoundConfig(childComplexity), true

	case "Showdown.roundTime":
		if e.complexity.Showdown.RoundTime == nil {
			break
		}

		return e.complexity.Showdown.RoundTime(childComplexity), true

	case "Showdown.rounds":
		if e.complexity.Showdown.Rounds == nil {
			break
		}

		return e.complexity.Showdown.Rounds(childComplexity), true

	case "Showdown.startTime":
		if e.complexity.Showdown.StartTime == nil {
			break
		}

		return e.complexity.Showdown.StartTime(childComplexity), true

	case "Showdown.stats":
		if e.complexity.Showdown.Stats == nil {
			break
		}

		return e.complexity.Showdown.Stats(childComplexity), true

	case "Showdown.status":
		if e.complexity.Showdown.Status == nil {
			break
		}

		return e.complexity.Showdown.Status(childComplexity), true

	case "Showdown.updatedAt":
		if e.complexity.Showdown.UpdatedAt == nil {
			break
		}

		return e.complexity.Showdown.UpdatedAt(childComplexity), true

	case "ShowdownGameConfig.hasOpponentNotShown":
		if e.complexity.ShowdownGameConfig.HasOpponentNotShown == nil {
			break
		}

		return e.complexity.ShowdownGameConfig.HasOpponentNotShown(childComplexity), true

	case "ShowdownGameConfig.isRoundEnded":
		if e.complexity.ShowdownGameConfig.IsRoundEnded == nil {
			break
		}

		return e.complexity.ShowdownGameConfig.IsRoundEnded(childComplexity), true

	case "ShowdownGameConfig.nextGameId":
		if e.complexity.ShowdownGameConfig.NextGameID == nil {
			break
		}

		return e.complexity.ShowdownGameConfig.NextGameID(childComplexity), true

	case "ShowdownGameConfig.nextGameStartsAt":
		if e.complexity.ShowdownGameConfig.NextGameStartsAt == nil {
			break
		}

		return e.complexity.ShowdownGameConfig.NextGameStartsAt(childComplexity), true

	case "ShowdownGameConfig.numOfGames":
		if e.complexity.ShowdownGameConfig.NumOfGames == nil {
			break
		}

		return e.complexity.ShowdownGameConfig.NumOfGames(childComplexity), true

	case "ShowdownGameConfig.round":
		if e.complexity.ShowdownGameConfig.Round == nil {
			break
		}

		return e.complexity.ShowdownGameConfig.Round(childComplexity), true

	case "ShowdownGameConfig.showdownGamePlayer":
		if e.complexity.ShowdownGameConfig.ShowdownGamePlayer == nil {
			break
		}

		return e.complexity.ShowdownGameConfig.ShowdownGamePlayer(childComplexity), true

	case "ShowdownGameConfig.totalGamesPlayed":
		if e.complexity.ShowdownGameConfig.TotalGamesPlayed == nil {
			break
		}

		return e.complexity.ShowdownGameConfig.TotalGamesPlayed(childComplexity), true

	case "ShowdownGamePlayer.isTie":
		if e.complexity.ShowdownGamePlayer.IsTie == nil {
			break
		}

		return e.complexity.ShowdownGamePlayer.IsTie(childComplexity), true

	case "ShowdownGamePlayer.isWinner":
		if e.complexity.ShowdownGamePlayer.IsWinner == nil {
			break
		}

		return e.complexity.ShowdownGamePlayer.IsWinner(childComplexity), true

	case "ShowdownGamePlayer.score":
		if e.complexity.ShowdownGamePlayer.Score == nil {
			break
		}

		return e.complexity.ShowdownGamePlayer.Score(childComplexity), true

	case "ShowdownGamePlayer.userId":
		if e.complexity.ShowdownGamePlayer.UserID == nil {
			break
		}

		return e.complexity.ShowdownGamePlayer.UserID(childComplexity), true

	case "ShowdownGamePlayer.wins":
		if e.complexity.ShowdownGamePlayer.Wins == nil {
			break
		}

		return e.complexity.ShowdownGamePlayer.Wins(childComplexity), true

	case "ShowdownParticipant.createdAt":
		if e.complexity.ShowdownParticipant.CreatedAt == nil {
			break
		}

		return e.complexity.ShowdownParticipant.CreatedAt(childComplexity), true

	case "ShowdownParticipant.hadABye":
		if e.complexity.ShowdownParticipant.HadABye == nil {
			break
		}

		return e.complexity.ShowdownParticipant.HadABye(childComplexity), true

	case "ShowdownParticipant._id":
		if e.complexity.ShowdownParticipant.ID == nil {
			break
		}

		return e.complexity.ShowdownParticipant.ID(childComplexity), true

	case "ShowdownParticipant.rank":
		if e.complexity.ShowdownParticipant.Rank == nil {
			break
		}

		return e.complexity.ShowdownParticipant.Rank(childComplexity), true

	case "ShowdownParticipant.ratingChange":
		if e.complexity.ShowdownParticipant.RatingChange == nil {
			break
		}

		return e.complexity.ShowdownParticipant.RatingChange(childComplexity), true

	case "ShowdownParticipant.recentOpponents":
		if e.complexity.ShowdownParticipant.RecentOpponents == nil {
			break
		}

		return e.complexity.ShowdownParticipant.RecentOpponents(childComplexity), true

	case "ShowdownParticipant.registrationData":
		if e.complexity.ShowdownParticipant.RegistrationData == nil {
			break
		}

		return e.complexity.ShowdownParticipant.RegistrationData(childComplexity), true

	case "ShowdownParticipant.rounds":
		if e.complexity.ShowdownParticipant.Rounds == nil {
			break
		}

		return e.complexity.ShowdownParticipant.Rounds(childComplexity), true

	case "ShowdownParticipant.showdownId":
		if e.complexity.ShowdownParticipant.ShowdownID == nil {
			break
		}

		return e.complexity.ShowdownParticipant.ShowdownID(childComplexity), true

	case "ShowdownParticipant.stats":
		if e.complexity.ShowdownParticipant.Stats == nil {
			break
		}

		return e.complexity.ShowdownParticipant.Stats(childComplexity), true

	case "ShowdownParticipant.status":
		if e.complexity.ShowdownParticipant.Status == nil {
			break
		}

		return e.complexity.ShowdownParticipant.Status(childComplexity), true

	case "ShowdownParticipant.totalScore":
		if e.complexity.ShowdownParticipant.TotalScore == nil {
			break
		}

		return e.complexity.ShowdownParticipant.TotalScore(childComplexity), true

	case "ShowdownParticipant.updatedAt":
		if e.complexity.ShowdownParticipant.UpdatedAt == nil {
			break
		}

		return e.complexity.ShowdownParticipant.UpdatedAt(childComplexity), true

	case "ShowdownParticipant.userID":
		if e.complexity.ShowdownParticipant.UserID == nil {
			break
		}

		return e.complexity.ShowdownParticipant.UserID(childComplexity), true

	case "ShowdownParticipant.userInfo":
		if e.complexity.ShowdownParticipant.UserInfo == nil {
			break
		}

		return e.complexity.ShowdownParticipant.UserInfo(childComplexity), true

	case "ShowdownParticipantDetail.currentRound":
		if e.complexity.ShowdownParticipantDetail.CurrentRound == nil {
			break
		}

		return e.complexity.ShowdownParticipantDetail.CurrentRound(childComplexity), true

	case "ShowdownParticipantDetail.showdownParticipant":
		if e.complexity.ShowdownParticipantDetail.ShowdownParticipant == nil {
			break
		}

		return e.complexity.ShowdownParticipantDetail.ShowdownParticipant(childComplexity), true

	case "ShowdownParticipantStats.currentScore":
		if e.complexity.ShowdownParticipantStats.CurrentScore == nil {
			break
		}

		return e.complexity.ShowdownParticipantStats.CurrentScore(childComplexity), true

	case "ShowdownParticipantStats.draw":
		if e.complexity.ShowdownParticipantStats.Draw == nil {
			break
		}

		return e.complexity.ShowdownParticipantStats.Draw(childComplexity), true

	case "ShowdownParticipantStats.loss":
		if e.complexity.ShowdownParticipantStats.Loss == nil {
			break
		}

		return e.complexity.ShowdownParticipantStats.Loss(childComplexity), true

	case "ShowdownParticipantStats.win":
		if e.complexity.ShowdownParticipantStats.Win == nil {
			break
		}

		return e.complexity.ShowdownParticipantStats.Win(childComplexity), true

	case "ShowdownRound.games":
		if e.complexity.ShowdownRound.Games == nil {
			break
		}

		return e.complexity.ShowdownRound.Games(childComplexity), true

	case "ShowdownRound.hasFailedToPlay":
		if e.complexity.ShowdownRound.HasFailedToPlay == nil {
			break
		}

		return e.complexity.ShowdownRound.HasFailedToPlay(childComplexity), true

	case "ShowdownRound.hasJoined":
		if e.complexity.ShowdownRound.HasJoined == nil {
			break
		}

		return e.complexity.ShowdownRound.HasJoined(childComplexity), true

	case "ShowdownRound.hasOpponentNotShown":
		if e.complexity.ShowdownRound.HasOpponentNotShown == nil {
			break
		}

		return e.complexity.ShowdownRound.HasOpponentNotShown(childComplexity), true

	case "ShowdownRound.isBye":
		if e.complexity.ShowdownRound.IsBye == nil {
			break
		}

		return e.complexity.ShowdownRound.IsBye(childComplexity), true

	case "ShowdownRound.isRoundEnded":
		if e.complexity.ShowdownRound.IsRoundEnded == nil {
			break
		}

		return e.complexity.ShowdownRound.IsRoundEnded(childComplexity), true

	case "ShowdownRound.loose":
		if e.complexity.ShowdownRound.Loose == nil {
			break
		}

		return e.complexity.ShowdownRound.Loose(childComplexity), true

	case "ShowdownRound.opponent":
		if e.complexity.ShowdownRound.Opponent == nil {
			break
		}

		return e.complexity.ShowdownRound.Opponent(childComplexity), true

	case "ShowdownRound.playerStatus":
		if e.complexity.ShowdownRound.PlayerStatus == nil {
			break
		}

		return e.complexity.ShowdownRound.PlayerStatus(childComplexity), true

	case "ShowdownRound.round":
		if e.complexity.ShowdownRound.Round == nil {
			break
		}

		return e.complexity.ShowdownRound.Round(childComplexity), true

	case "ShowdownRound.score":
		if e.complexity.ShowdownRound.Score == nil {
			break
		}

		return e.complexity.ShowdownRound.Score(childComplexity), true

	case "ShowdownRound.totalGamesPlayed":
		if e.complexity.ShowdownRound.TotalGamesPlayed == nil {
			break
		}

		return e.complexity.ShowdownRound.TotalGamesPlayed(childComplexity), true

	case "ShowdownRound.wins":
		if e.complexity.ShowdownRound.Wins == nil {
			break
		}

		return e.complexity.ShowdownRound.Wins(childComplexity), true

	case "ShowdownStats.totalGamesPlayed":
		if e.complexity.ShowdownStats.TotalGamesPlayed == nil {
			break
		}

		return e.complexity.ShowdownStats.TotalGamesPlayed(childComplexity), true

	case "ShowdownStats.totalParticipants":
		if e.complexity.ShowdownStats.TotalParticipants == nil {
			break
		}

		return e.complexity.ShowdownStats.TotalParticipants(childComplexity), true

	case "ShowdownUserDetails.name":
		if e.complexity.ShowdownUserDetails.Name == nil {
			break
		}

		return e.complexity.ShowdownUserDetails.Name(childComplexity), true

	case "ShowdownUserDetails.profileImageUrl":
		if e.complexity.ShowdownUserDetails.ProfileImageURL == nil {
			break
		}

		return e.complexity.ShowdownUserDetails.ProfileImageURL(childComplexity), true

	case "ShowdownUserDetails.rating":
		if e.complexity.ShowdownUserDetails.Rating == nil {
			break
		}

		return e.complexity.ShowdownUserDetails.Rating(childComplexity), true

	case "ShowdownUserDetails.username":
		if e.complexity.ShowdownUserDetails.Username == nil {
			break
		}

		return e.complexity.ShowdownUserDetails.Username(childComplexity), true

	case "StatikCoinLeaderboardEntry.rank":
		if e.complexity.StatikCoinLeaderboardEntry.Rank == nil {
			break
		}

		return e.complexity.StatikCoinLeaderboardEntry.Rank(childComplexity), true

	case "StatikCoinLeaderboardEntry.statikCoins":
		if e.complexity.StatikCoinLeaderboardEntry.StatikCoins == nil {
			break
		}

		return e.complexity.StatikCoinLeaderboardEntry.StatikCoins(childComplexity), true

	case "StatikCoinLeaderboardEntry.user":
		if e.complexity.StatikCoinLeaderboardEntry.User == nil {
			break
		}

		return e.complexity.StatikCoinLeaderboardEntry.User(childComplexity), true

	case "StatikCoinLeaderboardPage.hasMore":
		if e.complexity.StatikCoinLeaderboardPage.HasMore == nil {
			break
		}

		return e.complexity.StatikCoinLeaderboardPage.HasMore(childComplexity), true

	case "StatikCoinLeaderboardPage.pageNumber":
		if e.complexity.StatikCoinLeaderboardPage.PageNumber == nil {
			break
		}

		return e.complexity.StatikCoinLeaderboardPage.PageNumber(childComplexity), true

	case "StatikCoinLeaderboardPage.pageSize":
		if e.complexity.StatikCoinLeaderboardPage.PageSize == nil {
			break
		}

		return e.complexity.StatikCoinLeaderboardPage.PageSize(childComplexity), true

	case "StatikCoinLeaderboardPage.results":
		if e.complexity.StatikCoinLeaderboardPage.Results == nil {
			break
		}

		return e.complexity.StatikCoinLeaderboardPage.Results(childComplexity), true

	case "StatikCoinLeaderboardPage.totalResults":
		if e.complexity.StatikCoinLeaderboardPage.TotalResults == nil {
			break
		}

		return e.complexity.StatikCoinLeaderboardPage.TotalResults(childComplexity), true

	case "StreakDay.activity":
		if e.complexity.StreakDay.Activity == nil {
			break
		}

		return e.complexity.StreakDay.Activity(childComplexity), true

	case "StreakDay.date":
		if e.complexity.StreakDay.Date == nil {
			break
		}

		return e.complexity.StreakDay.Date(childComplexity), true

	case "StreakDay.isShieldUsed":
		if e.complexity.StreakDay.IsShieldUsed == nil {
			break
		}

		return e.complexity.StreakDay.IsShieldUsed(childComplexity), true

	case "StreakEntry.date":
		if e.complexity.StreakEntry.Date == nil {
			break
		}

		return e.complexity.StreakEntry.Date(childComplexity), true

	case "StreakEntry.isShieldUsed":
		if e.complexity.StreakEntry.IsShieldUsed == nil {
			break
		}

		return e.complexity.StreakEntry.IsShieldUsed(childComplexity), true

	case "StreakHistory._id":
		if e.complexity.StreakHistory.ID == nil {
			break
		}

		return e.complexity.StreakHistory.ID(childComplexity), true

	case "StreakHistory.streakHistoryObj":
		if e.complexity.StreakHistory.StreakHistoryObj == nil {
			break
		}

		return e.complexity.StreakHistory.StreakHistoryObj(childComplexity), true

	case "StreakShieldTransaction.createdAt":
		if e.complexity.StreakShieldTransaction.CreatedAt == nil {
			break
		}

		return e.complexity.StreakShieldTransaction.CreatedAt(childComplexity), true

	case "StreakShieldTransaction.earnVia":
		if e.complexity.StreakShieldTransaction.EarnVia == nil {
			break
		}

		return e.complexity.StreakShieldTransaction.EarnVia(childComplexity), true

	case "StreakShieldTransaction._id":
		if e.complexity.StreakShieldTransaction.ID == nil {
			break
		}

		return e.complexity.StreakShieldTransaction.ID(childComplexity), true

	case "StreakShieldTransaction.quantity":
		if e.complexity.StreakShieldTransaction.Quantity == nil {
			break
		}

		return e.complexity.StreakShieldTransaction.Quantity(childComplexity), true

	case "StreakShieldTransaction.redeemedOn":
		if e.complexity.StreakShieldTransaction.RedeemedOn == nil {
			break
		}

		return e.complexity.StreakShieldTransaction.RedeemedOn(childComplexity), true

	case "StreakShieldTransaction.referralId":
		if e.complexity.StreakShieldTransaction.ReferralID == nil {
			break
		}

		return e.complexity.StreakShieldTransaction.ReferralID(childComplexity), true

	case "StreakShieldTransaction.transactionId":
		if e.complexity.StreakShieldTransaction.TransactionID == nil {
			break
		}

		return e.complexity.StreakShieldTransaction.TransactionID(childComplexity), true

	case "StreakShieldTransaction.transactionType":
		if e.complexity.StreakShieldTransaction.TransactionType == nil {
			break
		}

		return e.complexity.StreakShieldTransaction.TransactionType(childComplexity), true

	case "StreakShieldTransaction.updatedAt":
		if e.complexity.StreakShieldTransaction.UpdatedAt == nil {
			break
		}

		return e.complexity.StreakShieldTransaction.UpdatedAt(childComplexity), true

	case "StreakShieldTransaction.userId":
		if e.complexity.StreakShieldTransaction.UserID == nil {
			break
		}

		return e.complexity.StreakShieldTransaction.UserID(childComplexity), true

	case "StreakShieldTransactionOutput.referralDetails":
		if e.complexity.StreakShieldTransactionOutput.ReferralDetails == nil {
			break
		}

		return e.complexity.StreakShieldTransactionOutput.ReferralDetails(childComplexity), true

	case "StreakShieldTransactionOutput.transaction":
		if e.complexity.StreakShieldTransactionOutput.Transaction == nil {
			break
		}

		return e.complexity.StreakShieldTransactionOutput.Transaction(childComplexity), true

	case "StreakShieldTransactionPage.hasMore":
		if e.complexity.StreakShieldTransactionPage.HasMore == nil {
			break
		}

		return e.complexity.StreakShieldTransactionPage.HasMore(childComplexity), true

	case "StreakShieldTransactionPage.pageNumber":
		if e.complexity.StreakShieldTransactionPage.PageNumber == nil {
			break
		}

		return e.complexity.StreakShieldTransactionPage.PageNumber(childComplexity), true

	case "StreakShieldTransactionPage.pageSize":
		if e.complexity.StreakShieldTransactionPage.PageSize == nil {
			break
		}

		return e.complexity.StreakShieldTransactionPage.PageSize(childComplexity), true

	case "StreakShieldTransactionPage.results":
		if e.complexity.StreakShieldTransactionPage.Results == nil {
			break
		}

		return e.complexity.StreakShieldTransactionPage.Results(childComplexity), true

	case "StreakShieldTransactionPage.totalResults":
		if e.complexity.StreakShieldTransactionPage.TotalResults == nil {
			break
		}

		return e.complexity.StreakShieldTransactionPage.TotalResults(childComplexity), true

	case "StreakStatusResponse.canSaveStreak":
		if e.complexity.StreakStatusResponse.CanSaveStreak == nil {
			break
		}

		return e.complexity.StreakStatusResponse.CanSaveStreak(childComplexity), true

	case "StreakStatusResponse.hasStreak":
		if e.complexity.StreakStatusResponse.HasStreak == nil {
			break
		}

		return e.complexity.StreakStatusResponse.HasStreak(childComplexity), true

	case "StreakStatusResponse.lastSevenDays":
		if e.complexity.StreakStatusResponse.LastSevenDays == nil {
			break
		}

		return e.complexity.StreakStatusResponse.LastSevenDays(childComplexity), true

	case "StreakStatusResponse.lostStreakCount":
		if e.complexity.StreakStatusResponse.LostStreakCount == nil {
			break
		}

		return e.complexity.StreakStatusResponse.LostStreakCount(childComplexity), true

	case "StreakStatusResponse.missedDays":
		if e.complexity.StreakStatusResponse.MissedDays == nil {
			break
		}

		return e.complexity.StreakStatusResponse.MissedDays(childComplexity), true

	case "StreakStatusResponse.streakFreezers":
		if e.complexity.StreakStatusResponse.StreakFreezers == nil {
			break
		}

		return e.complexity.StreakStatusResponse.StreakFreezers(childComplexity), true

	case "Submission.inCorrectAttempts":
		if e.complexity.Submission.InCorrectAttempts == nil {
			break
		}

		return e.complexity.Submission.InCorrectAttempts(childComplexity), true

	case "Submission.isCorrect":
		if e.complexity.Submission.IsCorrect == nil {
			break
		}

		return e.complexity.Submission.IsCorrect(childComplexity), true

	case "Submission.points":
		if e.complexity.Submission.Points == nil {
			break
		}

		return e.complexity.Submission.Points(childComplexity), true

	case "Submission.submissionTime":
		if e.complexity.Submission.SubmissionTime == nil {
			break
		}

		return e.complexity.Submission.SubmissionTime(childComplexity), true

	case "Submission.submittedValues":
		if e.complexity.Submission.SubmittedValues == nil {
			break
		}

		return e.complexity.Submission.SubmittedValues(childComplexity), true

	case "Submission.timeTaken":
		if e.complexity.Submission.TimeTaken == nil {
			break
		}

		return e.complexity.Submission.TimeTaken(childComplexity), true

	case "Submission.userId":
		if e.complexity.Submission.UserID == nil {
			break
		}

		return e.complexity.Submission.UserID(childComplexity), true

	case "SubmitChallengeResult.message":
		if e.complexity.SubmitChallengeResult.Message == nil {
			break
		}

		return e.complexity.SubmitChallengeResult.Message(childComplexity), true

	case "SubmitChallengeResult.result":
		if e.complexity.SubmitChallengeResult.Result == nil {
			break
		}

		return e.complexity.SubmitChallengeResult.Result(childComplexity), true

	case "SubmitChallengeResult.success":
		if e.complexity.SubmitChallengeResult.Success == nil {
			break
		}

		return e.complexity.SubmitChallengeResult.Success(childComplexity), true

	case "Subscription.contestLeaderboardUpdated":
		if e.complexity.Subscription.ContestLeaderboardUpdated == nil {
			break
		}

		args, err := ec.field_Subscription_contestLeaderboardUpdated_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Subscription.ContestLeaderboardUpdated(childComplexity, args["contestId"].(primitive.ObjectID)), true

	case "Subscription.gameEvent":
		if e.complexity.Subscription.GameEvent == nil {
			break
		}

		args, err := ec.field_Subscription_gameEvent_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Subscription.GameEvent(childComplexity, args["gameId"].(*primitive.ObjectID)), true

	case "Subscription.rematchRequest":
		if e.complexity.Subscription.RematchRequest == nil {
			break
		}

		args, err := ec.field_Subscription_rematchRequest_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Subscription.RematchRequest(childComplexity, args["gameId"].(primitive.ObjectID)), true

	case "Subscription.searchPlayer":
		if e.complexity.Subscription.SearchPlayer == nil {
			break
		}

		args, err := ec.field_Subscription_searchPlayer_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Subscription.SearchPlayer(childComplexity, args["userId"].(*primitive.ObjectID)), true

	case "Subscription.userEvents":
		if e.complexity.Subscription.UserEvents == nil {
			break
		}

		args, err := ec.field_Subscription_userEvents_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Subscription.UserEvents(childComplexity, args["userId"].(*primitive.ObjectID)), true

	case "SubscriptionOutput.event":
		if e.complexity.SubscriptionOutput.Event == nil {
			break
		}

		return e.complexity.SubscriptionOutput.Event(childComplexity), true

	case "SubscriptionOutput.game":
		if e.complexity.SubscriptionOutput.Game == nil {
			break
		}

		return e.complexity.SubscriptionOutput.Game(childComplexity), true

	case "SubscriptionOutput.question":
		if e.complexity.SubscriptionOutput.Question == nil {
			break
		}

		return e.complexity.SubscriptionOutput.Question(childComplexity), true

	case "ThreadsPage.hasMore":
		if e.complexity.ThreadsPage.HasMore == nil {
			break
		}

		return e.complexity.ThreadsPage.HasMore(childComplexity), true

	case "ThreadsPage.pageNumber":
		if e.complexity.ThreadsPage.PageNumber == nil {
			break
		}

		return e.complexity.ThreadsPage.PageNumber(childComplexity), true

	case "ThreadsPage.pageSize":
		if e.complexity.ThreadsPage.PageSize == nil {
			break
		}

		return e.complexity.ThreadsPage.PageSize(childComplexity), true

	case "ThreadsPage.results":
		if e.complexity.ThreadsPage.Results == nil {
			break
		}

		return e.complexity.ThreadsPage.Results(childComplexity), true

	case "ThreadsPage.totalResults":
		if e.complexity.ThreadsPage.TotalResults == nil {
			break
		}

		return e.complexity.ThreadsPage.TotalResults(childComplexity), true

	case "TopPlayerEntry.rank":
		if e.complexity.TopPlayerEntry.Rank == nil {
			break
		}

		return e.complexity.TopPlayerEntry.Rank(childComplexity), true

	case "TopPlayerEntry.rating":
		if e.complexity.TopPlayerEntry.Rating == nil {
			break
		}

		return e.complexity.TopPlayerEntry.Rating(childComplexity), true

	case "TopPlayerEntry.user":
		if e.complexity.TopPlayerEntry.User == nil {
			break
		}

		return e.complexity.TopPlayerEntry.User(childComplexity), true

	case "TopPlayersLeaderboard.abilityRating":
		if e.complexity.TopPlayersLeaderboard.AbilityRating == nil {
			break
		}

		return e.complexity.TopPlayersLeaderboard.AbilityRating(childComplexity), true

	case "TopPlayersLeaderboard.globalRating":
		if e.complexity.TopPlayersLeaderboard.GlobalRating == nil {
			break
		}

		return e.complexity.TopPlayersLeaderboard.GlobalRating(childComplexity), true

	case "TopPlayersLeaderboard.memoryRating":
		if e.complexity.TopPlayersLeaderboard.MemoryRating == nil {
			break
		}

		return e.complexity.TopPlayersLeaderboard.MemoryRating(childComplexity), true

	case "User.accountStatus":
		if e.complexity.User.AccountStatus == nil {
			break
		}

		return e.complexity.User.AccountStatus(childComplexity), true

	case "User.additional":
		if e.complexity.User.Additional == nil {
			break
		}

		return e.complexity.User.Additional(childComplexity), true

	case "User.awardsAndAchievements":
		if e.complexity.User.AwardsAndAchievements == nil {
			break
		}

		return e.complexity.User.AwardsAndAchievements(childComplexity), true

	case "User.badge":
		if e.complexity.User.Badge == nil {
			break
		}

		return e.complexity.User.Badge(childComplexity), true

	case "User.bio":
		if e.complexity.User.Bio == nil {
			break
		}

		return e.complexity.User.Bio(childComplexity), true

	case "User.country":
		if e.complexity.User.Country == nil {
			break
		}

		return e.complexity.User.Country(childComplexity), true

	case "User.countryCode":
		if e.complexity.User.CountryCode == nil {
			break
		}

		return e.complexity.User.CountryCode(childComplexity), true

	case "User.countryRank":
		if e.complexity.User.CountryRank == nil {
			break
		}

		return e.complexity.User.CountryRank(childComplexity), true

	case "User.email":
		if e.complexity.User.Email == nil {
			break
		}

		return e.complexity.User.Email(childComplexity), true

	case "User.globalRank":
		if e.complexity.User.GlobalRank == nil {
			break
		}

		return e.complexity.User.GlobalRank(childComplexity), true

	case "User.hasFixedRating":
		if e.complexity.User.HasFixedRating == nil {
			break
		}

		return e.complexity.User.HasFixedRating(childComplexity), true

	case "User._id":
		if e.complexity.User.ID == nil {
			break
		}

		return e.complexity.User.ID(childComplexity), true

	case "User.institutionId":
		if e.complexity.User.InstitutionID == nil {
			break
		}

		return e.complexity.User.InstitutionID(childComplexity), true

	case "User.isBot":
		if e.complexity.User.IsBot == nil {
			break
		}

		return e.complexity.User.IsBot(childComplexity), true

	case "User.isDeleted":
		if e.complexity.User.IsDeleted == nil {
			break
		}

		return e.complexity.User.IsDeleted(childComplexity), true

	case "User.isGuest":
		if e.complexity.User.IsGuest == nil {
			break
		}

		return e.complexity.User.IsGuest(childComplexity), true

	case "User.isReferred":
		if e.complexity.User.IsReferred == nil {
			break
		}

		return e.complexity.User.IsReferred(childComplexity), true

	case "User.isShadowBanned":
		if e.complexity.User.IsShadowBanned == nil {
			break
		}

		return e.complexity.User.IsShadowBanned(childComplexity), true

	case "User.isSignup":
		if e.complexity.User.IsSignup == nil {
			break
		}

		return e.complexity.User.IsSignup(childComplexity), true

	case "User.lastReadFeedId":
		if e.complexity.User.LastReadFeedID == nil {
			break
		}

		return e.complexity.User.LastReadFeedID(childComplexity), true

	case "User.league":
		if e.complexity.User.League == nil {
			break
		}

		return e.complexity.User.League(childComplexity), true

	case "User.links":
		if e.complexity.User.Links == nil {
			break
		}

		return e.complexity.User.Links(childComplexity), true

	case "User.name":
		if e.complexity.User.Name == nil {
			break
		}

		return e.complexity.User.Name(childComplexity), true

	case "User.phoneNumber":
		if e.complexity.User.PhoneNumber == nil {
			break
		}

		return e.complexity.User.PhoneNumber(childComplexity), true

	case "User.previousCountryRank":
		if e.complexity.User.PreviousCountryRank == nil {
			break
		}

		return e.complexity.User.PreviousCountryRank(childComplexity), true

	case "User.previousGlobalRank":
		if e.complexity.User.PreviousGlobalRank == nil {
			break
		}

		return e.complexity.User.PreviousGlobalRank(childComplexity), true

	case "User.profileImageUrl":
		if e.complexity.User.ProfileImageURL == nil {
			break
		}

		return e.complexity.User.ProfileImageURL(childComplexity), true

	case "User.rating":
		if e.complexity.User.Rating == nil {
			break
		}

		return e.complexity.User.Rating(childComplexity), true

	case "User.ratingV2":
		if e.complexity.User.RatingV2 == nil {
			break
		}

		return e.complexity.User.RatingV2(childComplexity), true

	case "User.referralCode":
		if e.complexity.User.ReferralCode == nil {
			break
		}

		return e.complexity.User.ReferralCode(childComplexity), true

	case "User.shadowBanStatus":
		if e.complexity.User.ShadowBanStatus == nil {
			break
		}

		return e.complexity.User.ShadowBanStatus(childComplexity), true

	case "User.statikCoins":
		if e.complexity.User.StatikCoins == nil {
			break
		}

		return e.complexity.User.StatikCoins(childComplexity), true

	case "User.stats":
		if e.complexity.User.Stats == nil {
			break
		}

		return e.complexity.User.Stats(childComplexity), true

	case "User.suspiciousActivity":
		if e.complexity.User.SuspiciousActivity == nil {
			break
		}

		return e.complexity.User.SuspiciousActivity(childComplexity), true

	case "User.timezone":
		if e.complexity.User.Timezone == nil {
			break
		}

		return e.complexity.User.Timezone(childComplexity), true

	case "User.token":
		if e.complexity.User.Token == nil {
			break
		}

		return e.complexity.User.Token(childComplexity), true

	case "User.userStreaks":
		if e.complexity.User.UserStreaks == nil {
			break
		}

		return e.complexity.User.UserStreaks(childComplexity), true

	case "User.username":
		if e.complexity.User.Username == nil {
			break
		}

		return e.complexity.User.Username(childComplexity), true

	case "UserAdditional.hasUnlockedAllGames":
		if e.complexity.UserAdditional.HasUnlockedAllGames == nil {
			break
		}

		return e.complexity.UserAdditional.HasUnlockedAllGames(childComplexity), true

	case "UserAdditional.timeSpent":
		if e.complexity.UserAdditional.TimeSpent == nil {
			break
		}

		return e.complexity.UserAdditional.TimeSpent(childComplexity), true

	case "UserAvgTime.presetAvgTime":
		if e.complexity.UserAvgTime.PresetAvgTime == nil {
			break
		}

		return e.complexity.UserAvgTime.PresetAvgTime(childComplexity), true

	case "UserAvgTime.presetBestTime":
		if e.complexity.UserAvgTime.PresetBestTime == nil {
			break
		}

		return e.complexity.UserAvgTime.PresetBestTime(childComplexity), true

	case "UserAvgTime.questionAvgTime":
		if e.complexity.UserAvgTime.QuestionAvgTime == nil {
			break
		}

		return e.complexity.UserAvgTime.QuestionAvgTime(childComplexity), true

	case "UserAvgTime.userId":
		if e.complexity.UserAvgTime.UserID == nil {
			break
		}

		return e.complexity.UserAvgTime.UserID(childComplexity), true

	case "UserContestResult.correctSubmission":
		if e.complexity.UserContestResult.CorrectSubmission == nil {
			break
		}

		return e.complexity.UserContestResult.CorrectSubmission(childComplexity), true

	case "UserContestResult.incorrectSubmission":
		if e.complexity.UserContestResult.IncorrectSubmission == nil {
			break
		}

		return e.complexity.UserContestResult.IncorrectSubmission(childComplexity), true

	case "UserContestResult.isVirtualParticipant":
		if e.complexity.UserContestResult.IsVirtualParticipant == nil {
			break
		}

		return e.complexity.UserContestResult.IsVirtualParticipant(childComplexity), true

	case "UserContestResult.lastSubmissionTime":
		if e.complexity.UserContestResult.LastSubmissionTime == nil {
			break
		}

		return e.complexity.UserContestResult.LastSubmissionTime(childComplexity), true

	case "UserContestResult.questionsSolved":
		if e.complexity.UserContestResult.QuestionsSolved == nil {
			break
		}

		return e.complexity.UserContestResult.QuestionsSolved(childComplexity), true

	case "UserContestResult.rank":
		if e.complexity.UserContestResult.Rank == nil {
			break
		}

		return e.complexity.UserContestResult.Rank(childComplexity), true

	case "UserContestResult.startTime":
		if e.complexity.UserContestResult.StartTime == nil {
			break
		}

		return e.complexity.UserContestResult.StartTime(childComplexity), true

	case "UserContestResult.totalParticipants":
		if e.complexity.UserContestResult.TotalParticipants == nil {
			break
		}

		return e.complexity.UserContestResult.TotalParticipants(childComplexity), true

	case "UserContestResult.totalScore":
		if e.complexity.UserContestResult.TotalScore == nil {
			break
		}

		return e.complexity.UserContestResult.TotalScore(childComplexity), true

	case "UserContestResult.user":
		if e.complexity.UserContestResult.User == nil {
			break
		}

		return e.complexity.UserContestResult.User(childComplexity), true

	case "UserContestSubmissions.correctSubmission":
		if e.complexity.UserContestSubmissions.CorrectSubmission == nil {
			break
		}

		return e.complexity.UserContestSubmissions.CorrectSubmission(childComplexity), true

	case "UserContestSubmissions.incorrectSubmission":
		if e.complexity.UserContestSubmissions.IncorrectSubmission == nil {
			break
		}

		return e.complexity.UserContestSubmissions.IncorrectSubmission(childComplexity), true

	case "UserContestSubmissions.lastSubmissionTime":
		if e.complexity.UserContestSubmissions.LastSubmissionTime == nil {
			break
		}

		return e.complexity.UserContestSubmissions.LastSubmissionTime(childComplexity), true

	case "UserContestSubmissions.startTime":
		if e.complexity.UserContestSubmissions.StartTime == nil {
			break
		}

		return e.complexity.UserContestSubmissions.StartTime(childComplexity), true

	case "UserContestSubmissions.submissions":
		if e.complexity.UserContestSubmissions.Submissions == nil {
			break
		}

		return e.complexity.UserContestSubmissions.Submissions(childComplexity), true

	case "UserContestSubmissions.totalScore":
		if e.complexity.UserContestSubmissions.TotalScore == nil {
			break
		}

		return e.complexity.UserContestSubmissions.TotalScore(childComplexity), true

	case "UserContestSubmissions.user":
		if e.complexity.UserContestSubmissions.User == nil {
			break
		}

		return e.complexity.UserContestSubmissions.User(childComplexity), true

	case "UserDailyChallengeResultWithStats.error":
		if e.complexity.UserDailyChallengeResultWithStats.Error == nil {
			break
		}

		return e.complexity.UserDailyChallengeResultWithStats.Error(childComplexity), true

	case "UserDailyChallengeResultWithStats.result":
		if e.complexity.UserDailyChallengeResultWithStats.Result == nil {
			break
		}

		return e.complexity.UserDailyChallengeResultWithStats.Result(childComplexity), true

	case "UserDailyChallengeResultWithStats.success":
		if e.complexity.UserDailyChallengeResultWithStats.Success == nil {
			break
		}

		return e.complexity.UserDailyChallengeResultWithStats.Success(childComplexity), true

	case "UserDailyChallengeStats.averageAccuracy":
		if e.complexity.UserDailyChallengeStats.AverageAccuracy == nil {
			break
		}

		return e.complexity.UserDailyChallengeStats.AverageAccuracy(childComplexity), true

	case "UserDailyChallengeStats.averageTime":
		if e.complexity.UserDailyChallengeStats.AverageTime == nil {
			break
		}

		return e.complexity.UserDailyChallengeStats.AverageTime(childComplexity), true

	case "UserDailyChallengeStats.bestTime":
		if e.complexity.UserDailyChallengeStats.BestTime == nil {
			break
		}

		return e.complexity.UserDailyChallengeStats.BestTime(childComplexity), true

	case "UserDailyChallengeStats.division":
		if e.complexity.UserDailyChallengeStats.Division == nil {
			break
		}

		return e.complexity.UserDailyChallengeStats.Division(childComplexity), true

	case "UserDailyChallengeStats._id":
		if e.complexity.UserDailyChallengeStats.ID == nil {
			break
		}

		return e.complexity.UserDailyChallengeStats.ID(childComplexity), true

	case "UserDailyChallengeStats.streaks":
		if e.complexity.UserDailyChallengeStats.Streaks == nil {
			break
		}

		return e.complexity.UserDailyChallengeStats.Streaks(childComplexity), true

	case "UserDailyChallengeStats.totalAttempts":
		if e.complexity.UserDailyChallengeStats.TotalAttempts == nil {
			break
		}

		return e.complexity.UserDailyChallengeStats.TotalAttempts(childComplexity), true

	case "UserDailyChallengeStats.totalSubmission":
		if e.complexity.UserDailyChallengeStats.TotalSubmission == nil {
			break
		}

		return e.complexity.UserDailyChallengeStats.TotalSubmission(childComplexity), true

	case "UserDailyChallengeStats.userId":
		if e.complexity.UserDailyChallengeStats.UserID == nil {
			break
		}

		return e.complexity.UserDailyChallengeStats.UserID(childComplexity), true

	case "UserDailyChallengeStreaks.current":
		if e.complexity.UserDailyChallengeStreaks.Current == nil {
			break
		}

		return e.complexity.UserDailyChallengeStreaks.Current(childComplexity), true

	case "UserDailyChallengeStreaks.highest":
		if e.complexity.UserDailyChallengeStreaks.Highest == nil {
			break
		}

		return e.complexity.UserDailyChallengeStreaks.Highest(childComplexity), true

	case "UserDailyChallengeStreaks.lastPlayedDate":
		if e.complexity.UserDailyChallengeStreaks.LastPlayedDate == nil {
			break
		}

		return e.complexity.UserDailyChallengeStreaks.LastPlayedDate(childComplexity), true

	case "UserDetailWithActivity.currActivity":
		if e.complexity.UserDetailWithActivity.CurrActivity == nil {
			break
		}

		return e.complexity.UserDetailWithActivity.CurrActivity(childComplexity), true

	case "UserDetailWithActivity.userInfo":
		if e.complexity.UserDetailWithActivity.UserInfo == nil {
			break
		}

		return e.complexity.UserDetailWithActivity.UserInfo(childComplexity), true

	case "UserDetailsForMessage._id":
		if e.complexity.UserDetailsForMessage.ID == nil {
			break
		}

		return e.complexity.UserDetailsForMessage.ID(childComplexity), true

	case "UserDetailsForMessage.name":
		if e.complexity.UserDetailsForMessage.Name == nil {
			break
		}

		return e.complexity.UserDetailsForMessage.Name(childComplexity), true

	case "UserDetailsForMessage.profileImageUrl":
		if e.complexity.UserDetailsForMessage.ProfileImageURL == nil {
			break
		}

		return e.complexity.UserDetailsForMessage.ProfileImageURL(childComplexity), true

	case "UserDetailsForMessage.rating":
		if e.complexity.UserDetailsForMessage.Rating == nil {
			break
		}

		return e.complexity.UserDetailsForMessage.Rating(childComplexity), true

	case "UserDetailsForMessage.username":
		if e.complexity.UserDetailsForMessage.Username == nil {
			break
		}

		return e.complexity.UserDetailsForMessage.Username(childComplexity), true

	case "UserFeed.createdAt":
		if e.complexity.UserFeed.CreatedAt == nil {
			break
		}

		return e.complexity.UserFeed.CreatedAt(childComplexity), true

	case "UserFeed.expirationTime":
		if e.complexity.UserFeed.ExpirationTime == nil {
			break
		}

		return e.complexity.UserFeed.ExpirationTime(childComplexity), true

	case "UserFeed.feedData":
		if e.complexity.UserFeed.FeedData == nil {
			break
		}

		return e.complexity.UserFeed.FeedData(childComplexity), true

	case "UserFeed.feedReferenceId":
		if e.complexity.UserFeed.FeedReferenceID == nil {
			break
		}

		return e.complexity.UserFeed.FeedReferenceID(childComplexity), true

	case "UserFeed.feedType":
		if e.complexity.UserFeed.FeedType == nil {
			break
		}

		return e.complexity.UserFeed.FeedType(childComplexity), true

	case "UserFeed._id":
		if e.complexity.UserFeed.ID == nil {
			break
		}

		return e.complexity.UserFeed.ID(childComplexity), true

	case "UserFeed.imageUrl":
		if e.complexity.UserFeed.ImageURL == nil {
			break
		}

		return e.complexity.UserFeed.ImageURL(childComplexity), true

	case "UserFeed.isLiked":
		if e.complexity.UserFeed.IsLiked == nil {
			break
		}

		return e.complexity.UserFeed.IsLiked(childComplexity), true

	case "UserFeed.userId":
		if e.complexity.UserFeed.UserID == nil {
			break
		}

		return e.complexity.UserFeed.UserID(childComplexity), true

	case "UserGame.id":
		if e.complexity.UserGame.ID == nil {
			break
		}

		return e.complexity.UserGame.ID(childComplexity), true

	case "UserGame.sT":
		if e.complexity.UserGame.ST == nil {
			break
		}

		return e.complexity.UserGame.ST(childComplexity), true

	case "UserLeaderboardPage.edges":
		if e.complexity.UserLeaderboardPage.Edges == nil {
			break
		}

		return e.complexity.UserLeaderboardPage.Edges(childComplexity), true

	case "UserLeaderboardPage.totalCount":
		if e.complexity.UserLeaderboardPage.TotalCount == nil {
			break
		}

		return e.complexity.UserLeaderboardPage.TotalCount(childComplexity), true

	case "UserPreset.bestStreak":
		if e.complexity.UserPreset.BestStreak == nil {
			break
		}

		return e.complexity.UserPreset.BestStreak(childComplexity), true

	case "UserPreset.bestTime":
		if e.complexity.UserPreset.BestTime == nil {
			break
		}

		return e.complexity.UserPreset.BestTime(childComplexity), true

	case "UserPreset.curAvgAccuracy":
		if e.complexity.UserPreset.CurAvgAccuracy == nil {
			break
		}

		return e.complexity.UserPreset.CurAvgAccuracy(childComplexity), true

	case "UserPreset.curAvgTime":
		if e.complexity.UserPreset.CurAvgTime == nil {
			break
		}

		return e.complexity.UserPreset.CurAvgTime(childComplexity), true

	case "UserPreset.globalPresetId":
		if e.complexity.UserPreset.GlobalPresetID == nil {
			break
		}

		return e.complexity.UserPreset.GlobalPresetID(childComplexity), true

	case "UserPreset._id":
		if e.complexity.UserPreset.ID == nil {
			break
		}

		return e.complexity.UserPreset.ID(childComplexity), true

	case "UserPreset.identifier":
		if e.complexity.UserPreset.Identifier == nil {
			break
		}

		return e.complexity.UserPreset.Identifier(childComplexity), true

	case "UserPreset.incorrectSubmissions":
		if e.complexity.UserPreset.IncorrectSubmissions == nil {
			break
		}

		return e.complexity.UserPreset.IncorrectSubmissions(childComplexity), true

	case "UserPreset.last10IncorrectAttempts":
		if e.complexity.UserPreset.Last10IncorrectAttempts == nil {
			break
		}

		return e.complexity.UserPreset.Last10IncorrectAttempts(childComplexity), true

	case "UserPreset.last10Time":
		if e.complexity.UserPreset.Last10Time == nil {
			break
		}

		return e.complexity.UserPreset.Last10Time(childComplexity), true

	case "UserPreset.name":
		if e.complexity.UserPreset.Name == nil {
			break
		}

		return e.complexity.UserPreset.Name(childComplexity), true

	case "UserPreset.numOfCorrectSubmissions":
		if e.complexity.UserPreset.NumOfCorrectSubmissions == nil {
			break
		}

		return e.complexity.UserPreset.NumOfCorrectSubmissions(childComplexity), true

	case "UserPreset.questionsSolved":
		if e.complexity.UserPreset.QuestionsSolved == nil {
			break
		}

		return e.complexity.UserPreset.QuestionsSolved(childComplexity), true

	case "UserPreset.saved":
		if e.complexity.UserPreset.Saved == nil {
			break
		}

		return e.complexity.UserPreset.Saved(childComplexity), true

	case "UserPreset.savedConfig":
		if e.complexity.UserPreset.SavedConfig == nil {
			break
		}

		return e.complexity.UserPreset.SavedConfig(childComplexity), true

	case "UserPreset.userId":
		if e.complexity.UserPreset.UserID == nil {
			break
		}

		return e.complexity.UserPreset.UserID(childComplexity), true

	case "UserPresetDayStats.date":
		if e.complexity.UserPresetDayStats.Date == nil {
			break
		}

		return e.complexity.UserPresetDayStats.Date(childComplexity), true

	case "UserPresetDayStats.userPresetStats":
		if e.complexity.UserPresetDayStats.UserPresetStats == nil {
			break
		}

		return e.complexity.UserPresetDayStats.UserPresetStats(childComplexity), true

	case "UserPresetStats.avgAccuracy":
		if e.complexity.UserPresetStats.AvgAccuracy == nil {
			break
		}

		return e.complexity.UserPresetStats.AvgAccuracy(childComplexity), true

	case "UserPresetStats.avgTime":
		if e.complexity.UserPresetStats.AvgTime == nil {
			break
		}

		return e.complexity.UserPresetStats.AvgTime(childComplexity), true

	case "UserPresetStats.bestStreak":
		if e.complexity.UserPresetStats.BestStreak == nil {
			break
		}

		return e.complexity.UserPresetStats.BestStreak(childComplexity), true

	case "UserPresetStats.date":
		if e.complexity.UserPresetStats.Date == nil {
			break
		}

		return e.complexity.UserPresetStats.Date(childComplexity), true

	case "UserPresetStats.globalPresetId":
		if e.complexity.UserPresetStats.GlobalPresetID == nil {
			break
		}

		return e.complexity.UserPresetStats.GlobalPresetID(childComplexity), true

	case "UserPresetStats._id":
		if e.complexity.UserPresetStats.ID == nil {
			break
		}

		return e.complexity.UserPresetStats.ID(childComplexity), true

	case "UserPresetStats.identifier":
		if e.complexity.UserPresetStats.Identifier == nil {
			break
		}

		return e.complexity.UserPresetStats.Identifier(childComplexity), true

	case "UserPresetStats.inaccuracyPerformanceTrend":
		if e.complexity.UserPresetStats.InaccuracyPerformanceTrend == nil {
			break
		}

		return e.complexity.UserPresetStats.InaccuracyPerformanceTrend(childComplexity), true

	case "UserPresetStats.incorrectSubmissions":
		if e.complexity.UserPresetStats.IncorrectSubmissions == nil {
			break
		}

		return e.complexity.UserPresetStats.IncorrectSubmissions(childComplexity), true

	case "UserPresetStats.numOfCorrectSubmissions":
		if e.complexity.UserPresetStats.NumOfCorrectSubmissions == nil {
			break
		}

		return e.complexity.UserPresetStats.NumOfCorrectSubmissions(childComplexity), true

	case "UserPresetStats.questionsSolved":
		if e.complexity.UserPresetStats.QuestionsSolved == nil {
			break
		}

		return e.complexity.UserPresetStats.QuestionsSolved(childComplexity), true

	case "UserPresetStats.timePerformanceTrend":
		if e.complexity.UserPresetStats.TimePerformanceTrend == nil {
			break
		}

		return e.complexity.UserPresetStats.TimePerformanceTrend(childComplexity), true

	case "UserPresetStats.userId":
		if e.complexity.UserPresetStats.UserID == nil {
			break
		}

		return e.complexity.UserPresetStats.UserID(childComplexity), true

	case "UserPresetStats.userPresetId":
		if e.complexity.UserPresetStats.UserPresetID == nil {
			break
		}

		return e.complexity.UserPresetStats.UserPresetID(childComplexity), true

	case "UserPresetStatsGraph.globalStats":
		if e.complexity.UserPresetStatsGraph.GlobalStats == nil {
			break
		}

		return e.complexity.UserPresetStatsGraph.GlobalStats(childComplexity), true

	case "UserPresetStatsGraph.userStats":
		if e.complexity.UserPresetStatsGraph.UserStats == nil {
			break
		}

		return e.complexity.UserPresetStatsGraph.UserStats(childComplexity), true

	case "UserPresets.totalCount":
		if e.complexity.UserPresets.TotalCount == nil {
			break
		}

		return e.complexity.UserPresets.TotalCount(childComplexity), true

	case "UserPresets.userPresets":
		if e.complexity.UserPresets.UserPresets == nil {
			break
		}

		return e.complexity.UserPresets.UserPresets(childComplexity), true

	case "UserPublicDetails.badge":
		if e.complexity.UserPublicDetails.Badge == nil {
			break
		}

		return e.complexity.UserPublicDetails.Badge(childComplexity), true

	case "UserPublicDetails.countryCode":
		if e.complexity.UserPublicDetails.CountryCode == nil {
			break
		}

		return e.complexity.UserPublicDetails.CountryCode(childComplexity), true

	case "UserPublicDetails.countryRank":
		if e.complexity.UserPublicDetails.CountryRank == nil {
			break
		}

		return e.complexity.UserPublicDetails.CountryRank(childComplexity), true

	case "UserPublicDetails.globalRank":
		if e.complexity.UserPublicDetails.GlobalRank == nil {
			break
		}

		return e.complexity.UserPublicDetails.GlobalRank(childComplexity), true

	case "UserPublicDetails._id":
		if e.complexity.UserPublicDetails.ID == nil {
			break
		}

		return e.complexity.UserPublicDetails.ID(childComplexity), true

	case "UserPublicDetails.institutionId":
		if e.complexity.UserPublicDetails.InstitutionID == nil {
			break
		}

		return e.complexity.UserPublicDetails.InstitutionID(childComplexity), true

	case "UserPublicDetails.isGuest":
		if e.complexity.UserPublicDetails.IsGuest == nil {
			break
		}

		return e.complexity.UserPublicDetails.IsGuest(childComplexity), true

	case "UserPublicDetails.name":
		if e.complexity.UserPublicDetails.Name == nil {
			break
		}

		return e.complexity.UserPublicDetails.Name(childComplexity), true

	case "UserPublicDetails.previousCountryRank":
		if e.complexity.UserPublicDetails.PreviousCountryRank == nil {
			break
		}

		return e.complexity.UserPublicDetails.PreviousCountryRank(childComplexity), true

	case "UserPublicDetails.previousGlobalRank":
		if e.complexity.UserPublicDetails.PreviousGlobalRank == nil {
			break
		}

		return e.complexity.UserPublicDetails.PreviousGlobalRank(childComplexity), true

	case "UserPublicDetails.profileImageUrl":
		if e.complexity.UserPublicDetails.ProfileImageURL == nil {
			break
		}

		return e.complexity.UserPublicDetails.ProfileImageURL(childComplexity), true

	case "UserPublicDetails.rating":
		if e.complexity.UserPublicDetails.Rating == nil {
			break
		}

		return e.complexity.UserPublicDetails.Rating(childComplexity), true

	case "UserPublicDetails.ratingV2":
		if e.complexity.UserPublicDetails.RatingV2 == nil {
			break
		}

		return e.complexity.UserPublicDetails.RatingV2(childComplexity), true

	case "UserPublicDetails.stats":
		if e.complexity.UserPublicDetails.Stats == nil {
			break
		}

		return e.complexity.UserPublicDetails.Stats(childComplexity), true

	case "UserPublicDetails.userStreaks":
		if e.complexity.UserPublicDetails.UserStreaks == nil {
			break
		}

		return e.complexity.UserPublicDetails.UserStreaks(childComplexity), true

	case "UserPublicDetails.username":
		if e.complexity.UserPublicDetails.Username == nil {
			break
		}

		return e.complexity.UserPublicDetails.Username(childComplexity), true

	case "UserRating.abilityDuelsRating":
		if e.complexity.UserRating.AbilityDuelsRating == nil {
			break
		}

		return e.complexity.UserRating.AbilityDuelsRating(childComplexity), true

	case "UserRating.flashAnzanRating":
		if e.complexity.UserRating.FlashAnzanRating == nil {
			break
		}

		return e.complexity.UserRating.FlashAnzanRating(childComplexity), true

	case "UserRating.globalRating":
		if e.complexity.UserRating.GlobalRating == nil {
			break
		}

		return e.complexity.UserRating.GlobalRating(childComplexity), true

	case "UserRating.puzzleRating":
		if e.complexity.UserRating.PuzzleRating == nil {
			break
		}

		return e.complexity.UserRating.PuzzleRating(childComplexity), true

	case "UserRatingFixtureSubmission.createdAt":
		if e.complexity.UserRatingFixtureSubmission.CreatedAt == nil {
			break
		}

		return e.complexity.UserRatingFixtureSubmission.CreatedAt(childComplexity), true

	case "UserRatingFixtureSubmission.currentRating":
		if e.complexity.UserRatingFixtureSubmission.CurrentRating == nil {
			break
		}

		return e.complexity.UserRatingFixtureSubmission.CurrentRating(childComplexity), true

	case "UserRatingFixtureSubmission.id":
		if e.complexity.UserRatingFixtureSubmission.ID == nil {
			break
		}

		return e.complexity.UserRatingFixtureSubmission.ID(childComplexity), true

	case "UserRatingFixtureSubmission.proposedRating":
		if e.complexity.UserRatingFixtureSubmission.ProposedRating == nil {
			break
		}

		return e.complexity.UserRatingFixtureSubmission.ProposedRating(childComplexity), true

	case "UserRatingFixtureSubmission.submissions":
		if e.complexity.UserRatingFixtureSubmission.Submissions == nil {
			break
		}

		return e.complexity.UserRatingFixtureSubmission.Submissions(childComplexity), true

	case "UserRatingFixtureSubmission.timeTaken":
		if e.complexity.UserRatingFixtureSubmission.TimeTaken == nil {
			break
		}

		return e.complexity.UserRatingFixtureSubmission.TimeTaken(childComplexity), true

	case "UserRatingFixtureSubmission.updatedAt":
		if e.complexity.UserRatingFixtureSubmission.UpdatedAt == nil {
			break
		}

		return e.complexity.UserRatingFixtureSubmission.UpdatedAt(childComplexity), true

	case "UserRatingFixtureSubmission.userId":
		if e.complexity.UserRatingFixtureSubmission.UserID == nil {
			break
		}

		return e.complexity.UserRatingFixtureSubmission.UserID(childComplexity), true

	case "UserRatingFixtureSubmission.userScore":
		if e.complexity.UserRatingFixtureSubmission.UserScore == nil {
			break
		}

		return e.complexity.UserRatingFixtureSubmission.UserScore(childComplexity), true

	case "UserRatingFixtureSubmission.userStance":
		if e.complexity.UserRatingFixtureSubmission.UserStance == nil {
			break
		}

		return e.complexity.UserRatingFixtureSubmission.UserStance(childComplexity), true

	case "UserResolution.CreatedAt":
		if e.complexity.UserResolution.CreatedAt == nil {
			break
		}

		return e.complexity.UserResolution.CreatedAt(childComplexity), true

	case "UserResolution.duration":
		if e.complexity.UserResolution.Duration == nil {
			break
		}

		return e.complexity.UserResolution.Duration(childComplexity), true

	case "UserResolution.id":
		if e.complexity.UserResolution.ID == nil {
			break
		}

		return e.complexity.UserResolution.ID(childComplexity), true

	case "UserResolution.userId":
		if e.complexity.UserResolution.UserID == nil {
			break
		}

		return e.complexity.UserResolution.UserID(childComplexity), true

	case "UserResult.error":
		if e.complexity.UserResult.Error == nil {
			break
		}

		return e.complexity.UserResult.Error(childComplexity), true

	case "UserResult.result":
		if e.complexity.UserResult.Result == nil {
			break
		}

		return e.complexity.UserResult.Result(childComplexity), true

	case "UserResult.success":
		if e.complexity.UserResult.Success == nil {
			break
		}

		return e.complexity.UserResult.Success(childComplexity), true

	case "UserSettings.hapticFeedback":
		if e.complexity.UserSettings.HapticFeedback == nil {
			break
		}

		return e.complexity.UserSettings.HapticFeedback(childComplexity), true

	case "UserSettings._id":
		if e.complexity.UserSettings.ID == nil {
			break
		}

		return e.complexity.UserSettings.ID(childComplexity), true

	case "UserSettings.playSound":
		if e.complexity.UserSettings.PlaySound == nil {
			break
		}

		return e.complexity.UserSettings.PlaySound(childComplexity), true

	case "UserSettings.userId":
		if e.complexity.UserSettings.UserID == nil {
			break
		}

		return e.complexity.UserSettings.UserID(childComplexity), true

	case "UserStats.followersCount":
		if e.complexity.UserStats.FollowersCount == nil {
			break
		}

		return e.complexity.UserStats.FollowersCount(childComplexity), true

	case "UserStats.followingsCount":
		if e.complexity.UserStats.FollowingsCount == nil {
			break
		}

		return e.complexity.UserStats.FollowingsCount(childComplexity), true

	case "UserStats.friendsCount":
		if e.complexity.UserStats.FriendsCount == nil {
			break
		}

		return e.complexity.UserStats.FriendsCount(childComplexity), true

	case "UserStats.games":
		if e.complexity.UserStats.Games == nil {
			break
		}

		return e.complexity.UserStats.Games(childComplexity), true

	case "UserStats.hr":
		if e.complexity.UserStats.Hr == nil {
			break
		}

		return e.complexity.UserStats.Hr(childComplexity), true

	case "UserStats.last10BotGames":
		if e.complexity.UserStats.Last10BotGames == nil {
			break
		}

		return e.complexity.UserStats.Last10BotGames(childComplexity), true

	case "UserStats.ngp":
		if e.complexity.UserStats.Ngp == nil {
			break
		}

		return e.complexity.UserStats.Ngp(childComplexity), true

	case "UserStreaks.currentStreak":
		if e.complexity.UserStreaks.CurrentStreak == nil {
			break
		}

		return e.complexity.UserStreaks.CurrentStreak(childComplexity), true

	case "UserStreaks.lastPlayedDate":
		if e.complexity.UserStreaks.LastPlayedDate == nil {
			break
		}

		return e.complexity.UserStreaks.LastPlayedDate(childComplexity), true

	case "UserStreaks.lastSevenDays":
		if e.complexity.UserStreaks.LastSevenDays == nil {
			break
		}

		return e.complexity.UserStreaks.LastSevenDays(childComplexity), true

	case "UserStreaks.longestStreak":
		if e.complexity.UserStreaks.LongestStreak == nil {
			break
		}

		return e.complexity.UserStreaks.LongestStreak(childComplexity), true

	case "UserStreaks.streakFreezers":
		if e.complexity.UserStreaks.StreakFreezers == nil {
			break
		}

		return e.complexity.UserStreaks.StreakFreezers(childComplexity), true

	case "UserStreaks.streakHistory":
		if e.complexity.UserStreaks.StreakHistory == nil {
			break
		}

		return e.complexity.UserStreaks.StreakHistory(childComplexity), true

	case "UsersWeeklyStatikCoinsOutput.dailyCoins":
		if e.complexity.UsersWeeklyStatikCoinsOutput.DailyCoins == nil {
			break
		}

		return e.complexity.UsersWeeklyStatikCoinsOutput.DailyCoins(childComplexity), true

	case "UsersWeeklyStatikCoinsOutput.totalCoins":
		if e.complexity.UsersWeeklyStatikCoinsOutput.TotalCoins == nil {
			break
		}

		return e.complexity.UsersWeeklyStatikCoinsOutput.TotalCoins(childComplexity), true

	case "WeeklyLeagueLeaderboardEntry.progressState":
		if e.complexity.WeeklyLeagueLeaderboardEntry.ProgressState == nil {
			break
		}

		return e.complexity.WeeklyLeagueLeaderboardEntry.ProgressState(childComplexity), true

	case "WeeklyLeagueLeaderboardEntry.rank":
		if e.complexity.WeeklyLeagueLeaderboardEntry.Rank == nil {
			break
		}

		return e.complexity.WeeklyLeagueLeaderboardEntry.Rank(childComplexity), true

	case "WeeklyLeagueLeaderboardEntry.statikCoins":
		if e.complexity.WeeklyLeagueLeaderboardEntry.StatikCoins == nil {
			break
		}

		return e.complexity.WeeklyLeagueLeaderboardEntry.StatikCoins(childComplexity), true

	case "WeeklyLeagueLeaderboardEntry.user":
		if e.complexity.WeeklyLeagueLeaderboardEntry.User == nil {
			break
		}

		return e.complexity.WeeklyLeagueLeaderboardEntry.User(childComplexity), true

	case "WeeklyLeagueLeaderboardPage.currentUserLeague":
		if e.complexity.WeeklyLeagueLeaderboardPage.CurrentUserLeague == nil {
			break
		}

		return e.complexity.WeeklyLeagueLeaderboardPage.CurrentUserLeague(childComplexity), true

	case "WeeklyLeagueLeaderboardPage.hasMore":
		if e.complexity.WeeklyLeagueLeaderboardPage.HasMore == nil {
			break
		}

		return e.complexity.WeeklyLeagueLeaderboardPage.HasMore(childComplexity), true

	case "WeeklyLeagueLeaderboardPage.pageNumber":
		if e.complexity.WeeklyLeagueLeaderboardPage.PageNumber == nil {
			break
		}

		return e.complexity.WeeklyLeagueLeaderboardPage.PageNumber(childComplexity), true

	case "WeeklyLeagueLeaderboardPage.pageSize":
		if e.complexity.WeeklyLeagueLeaderboardPage.PageSize == nil {
			break
		}

		return e.complexity.WeeklyLeagueLeaderboardPage.PageSize(childComplexity), true

	case "WeeklyLeagueLeaderboardPage.results":
		if e.complexity.WeeklyLeagueLeaderboardPage.Results == nil {
			break
		}

		return e.complexity.WeeklyLeagueLeaderboardPage.Results(childComplexity), true

	case "WeeklyLeagueLeaderboardPage.totalResults":
		if e.complexity.WeeklyLeagueLeaderboardPage.TotalResults == nil {
			break
		}

		return e.complexity.WeeklyLeagueLeaderboardPage.TotalResults(childComplexity), true

	case "showdownDetails.format":
		if e.complexity.ShowdownDetails.Format == nil {
			break
		}

		return e.complexity.ShowdownDetails.Format(childComplexity), true

	case "showdownDetails.rules":
		if e.complexity.ShowdownDetails.Rules == nil {
			break
		}

		return e.complexity.ShowdownDetails.Rules(childComplexity), true

	}
	return 0, false
}

func (e *executableSchema) Exec(ctx context.Context) graphql.ResponseHandler {
	opCtx := graphql.GetOperationContext(ctx)
	ec := executionContext{opCtx, e, 0, 0, make(chan graphql.DeferredResult)}
	inputUnmarshalMap := graphql.BuildUnmarshalerMap(
		ec.unmarshalInputAppleSignInFullNameInput,
		ec.unmarshalInputAppleSignInInput,
		ec.unmarshalInputAwardsAndAchievementsInput,
		ec.unmarshalInputCTAInput,
		ec.unmarshalInputCellInput,
		ec.unmarshalInputChallengeUserForPuzzleGameInput,
		ec.unmarshalInputChallengeUserInput,
		ec.unmarshalInputContestDetailsInput,
		ec.unmarshalInputContestQuestionInput,
		ec.unmarshalInputCreateAnnouncementInput,
		ec.unmarshalInputCreateClubAnnouncementInput,
		ec.unmarshalInputCreateClubEventInput,
		ec.unmarshalInputCreateClubInput,
		ec.unmarshalInputCreateContestInput,
		ec.unmarshalInputCreateForumInput,
		ec.unmarshalInputCreateForumReplyInput,
		ec.unmarshalInputCreateForumThreadInput,
		ec.unmarshalInputCreateInstitutionInput,
		ec.unmarshalInputCreateLeagueInput,
		ec.unmarshalInputCreateShowdownInput,
		ec.unmarshalInputCreateStreakShieldTransactionInput,
		ec.unmarshalInputDefaultGameConfigInput,
		ec.unmarshalInputDefaultGameModeConfigInput,
		ec.unmarshalInputFeedback,
		ec.unmarshalInputFieldValidationInput,
		ec.unmarshalInputFlashAnzanAnswerInput,
		ec.unmarshalInputFollowUserInput,
		ec.unmarshalInputFormFieldInput,
		ec.unmarshalInputFriendRequestInput,
		ec.unmarshalInputGameCategorySpecificConfigInput,
		ec.unmarshalInputGameConfigInput,
		ec.unmarshalInputGameModeSpecificConfigInput,
		ec.unmarshalInputGameTypeSpecificAnswerInput,
		ec.unmarshalInputGameTypeSpecificConfigInput,
		ec.unmarshalInputGetAllMessageGroupsInput,
		ec.unmarshalInputGetGamesByRatingInput,
		ec.unmarshalInputGetGamesInput,
		ec.unmarshalInputGetPuzzleGamesInput,
		ec.unmarshalInputGroupPlayGameConfigInput,
		ec.unmarshalInputHostInfoInput,
		ec.unmarshalInputJoinGameInput,
		ec.unmarshalInputJoinLeagueInput,
		ec.unmarshalInputLeagueDetailsInput,
		ec.unmarshalInputPageInfoInput,
		ec.unmarshalInputPaginatedLeaderboardInput,
		ec.unmarshalInputPlayerSettingInput,
		ec.unmarshalInputPuzzleGameConfigInput,
		ec.unmarshalInputRegistrationFormFieldValueInput,
		ec.unmarshalInputRegistrationFormInput,
		ec.unmarshalInputRegistrationFormValuesInput,
		ec.unmarshalInputRemoveFollowerInput,
		ec.unmarshalInputRemoveFriendInput,
		ec.unmarshalInputRoundConfigInput,
		ec.unmarshalInputSendFriendRequestInput,
		ec.unmarshalInputShowdownDetailsInput,
		ec.unmarshalInputShowdownGameConfigInput,
		ec.unmarshalInputShowdownRegistrationFormValuesInput,
		ec.unmarshalInputSortOptions,
		ec.unmarshalInputStartGameForShowdownInput,
		ec.unmarshalInputStartGameInput,
		ec.unmarshalInputSubmitAnswerInput,
		ec.unmarshalInputSubmitFlashAnzanAnswerInput,
		ec.unmarshalInputSubmitPuzzleGameAnswerInput,
		ec.unmarshalInputSubmitPuzzleRushGame,
		ec.unmarshalInputSubmitSolutionInput,
		ec.unmarshalInputTimeRangeInput,
		ec.unmarshalInputUnFollowUserInput,
		ec.unmarshalInputUpdateAnnouncementInput,
		ec.unmarshalInputUpdateClubEventInput,
		ec.unmarshalInputUpdateClubInput,
		ec.unmarshalInputUpdateSettingsInput,
		ec.unmarshalInputUpdateUserInput,
		ec.unmarshalInputUserInput,
		ec.unmarshalInputUserPresetResult,
		ec.unmarshalInputUserPresetResultInput,
		ec.unmarshalInputWithdrawFriendRequestInput,
	)
	first := true

	switch opCtx.Operation.Operation {
	case ast.Query:
		return func(ctx context.Context) *graphql.Response {
			var response graphql.Response
			var data graphql.Marshaler
			if first {
				first = false
				ctx = graphql.WithUnmarshalerMap(ctx, inputUnmarshalMap)
				data = ec._Query(ctx, opCtx.Operation.SelectionSet)
			} else {
				if atomic.LoadInt32(&ec.pendingDeferred) > 0 {
					result := <-ec.deferredResults
					atomic.AddInt32(&ec.pendingDeferred, -1)
					data = result.Result
					response.Path = result.Path
					response.Label = result.Label
					response.Errors = result.Errors
				} else {
					return nil
				}
			}
			var buf bytes.Buffer
			data.MarshalGQL(&buf)
			response.Data = buf.Bytes()
			if atomic.LoadInt32(&ec.deferred) > 0 {
				hasNext := atomic.LoadInt32(&ec.pendingDeferred) > 0
				response.HasNext = &hasNext
			}

			return &response
		}
	case ast.Mutation:
		return func(ctx context.Context) *graphql.Response {
			if !first {
				return nil
			}
			first = false
			ctx = graphql.WithUnmarshalerMap(ctx, inputUnmarshalMap)
			data := ec._Mutation(ctx, opCtx.Operation.SelectionSet)
			var buf bytes.Buffer
			data.MarshalGQL(&buf)

			return &graphql.Response{
				Data: buf.Bytes(),
			}
		}
	case ast.Subscription:
		next := ec._Subscription(ctx, opCtx.Operation.SelectionSet)

		var buf bytes.Buffer
		return func(ctx context.Context) *graphql.Response {
			buf.Reset()
			data := next(ctx)

			if data == nil {
				return nil
			}
			data.MarshalGQL(&buf)

			return &graphql.Response{
				Data: buf.Bytes(),
			}
		}

	default:
		return graphql.OneShot(graphql.ErrorResponse(ctx, "unsupported GraphQL operation"))
	}
}

type executionContext struct {
	*graphql.OperationContext
	*executableSchema
	deferred        int32
	pendingDeferred int32
	deferredResults chan graphql.DeferredResult
}

func (ec *executionContext) processDeferredGroup(dg graphql.DeferredGroup) {
	atomic.AddInt32(&ec.pendingDeferred, 1)
	go func() {
		ctx := graphql.WithFreshResponseContext(dg.Context)
		dg.FieldSet.Dispatch(ctx)
		ds := graphql.DeferredResult{
			Path:   dg.Path,
			Label:  dg.Label,
			Result: dg.FieldSet,
			Errors: graphql.GetErrors(ctx),
		}
		// null fields should bubble up
		if dg.FieldSet.Invalids > 0 {
			ds.Result = graphql.Null
		}
		ec.deferredResults <- ds
	}()
}

func (ec *executionContext) introspectSchema() (*introspection.Schema, error) {
	if ec.DisableIntrospection {
		return nil, errors.New("introspection disabled")
	}
	return introspection.WrapSchema(ec.Schema()), nil
}

func (ec *executionContext) introspectType(name string) (*introspection.Type, error) {
	if ec.DisableIntrospection {
		return nil, errors.New("introspection disabled")
	}
	return introspection.WrapTypeFromDef(ec.Schema(), ec.Schema().Types[name]), nil
}

var sources = []*ast.Source{
	{Name: "../schema/announcement.graphqls", Input: `"""
Represents different types of announcements the system can display.
"""
enum AnnouncementType {
    GENERAL # General information or updates
    FEATURE_UPDATE # Announcing new or updated features
    PROMOTION # Special offers, discounts, etc.
    EVENT # Information about upcoming events or contests
    MAINTENANCE # Planned maintenance notifications
    SURVEY # Requesting user feedback via a survey
}

"""
Defines the action triggered by a Call-to-Action button.
"""
enum CTAActionType {
    OPEN_URL # Opens the provided URL in a browser/webview
    NAVIGATE_INTERNAL # Triggers navigation within the application
    DISMISS # Simply dismisses the announcement
}

"""
Represents a Call-to-Action (CTA) element within an announcement.
"""
type CallToAction {
    "The text displayed on the button."
    text: String!
    "The URL or internal route associated with the action (required for OPEN_URL, NAVIGATE_INTERNAL)."
    target: String
    "The type of action this CTA performs."
    actionType: CTAActionType!
    "Optional style hint for the button (e.g., 'primary', 'secondary'). Frontend interprets this."
    style: String
}

"""
Represents a system announcement intended for users.
"""
type Announcement {
    "Unique identifier for the announcement."
    id: ID!
    "The category or type of the announcement."
    type: AnnouncementType!
    "The main title or headline of the announcement (optional)."
    title: String
    description: String!
    "Rive animation url"
    riveAnimationUrl: String
    "URL for an optional image associated with the announcement."
    imageUrl: String
    "URL for optional video or audio content."
    mediaUrl: String
    "List of Call-to-Action buttons for this announcement."
    ctas: [CallToAction!]
    "Priority for ordering announcements (higher value means higher priority). Defaults to 0."
    priority: Int
    "Timestamp when the announcement was created in the system."
    createdAt: Time!
    "Timestamp when the announcement should become visible to users (optional, defaults to createdAt)."
    publishedAt: Time
    "Timestamp when the announcement should expire and no longer be shown (optional)."
    expiresAt: Time
}

"""
Input for creating a CallToAction.
"""
input CTAInput {
    "The text displayed on the button."
    text: String!
    "The URL or internal route associated with the action (required for OPEN_URL, NAVIGATE_INTERNAL)."
    target: String
    "The type of action this CTA performs."
    actionType: CTAActionType!
    "Optional style hint for the button (e.g., 'primary', 'secondary'). Frontend interprets this."
    style: String
}

"""
Input for creating a new announcement.
"""
input CreateAnnouncementInput {
    type: AnnouncementType!
    title: String
    description: String!
    riveAnimationUrl: String
    imageUrl: String
    mediaUrl: String
    ctas: [CTAInput!]
    priority: Int
    publishedAt: Time
    expiresAt: Time
}

"""
Input for updating an existing announcement. Fields are optional.
"""
input UpdateAnnouncementInput {
    type: AnnouncementType
    title: String
    description: String
    imageUrl: String
    mediaUrl: String
    ctas: [CTAInput!]
    priority: Int
    publishedAt: Time
    expiresAt: Time
    riveAnimationUrl: String
}

"""
Standard response for mutations that primarily indicate success/failure.
"""
type AnnouncementMutationResponse {
    success: Boolean!
    message: String
}

# --- Queries ---

extend type Query {
    """
    Fetches unread announcements for the currently authenticated user, respecting
    publication dates, expiry dates, and targeting. Ordered by priority then creation date.
    """
    getUnreadAnnouncements(limit: Int = 10, offset: Int = 0): [Announcement!]!
    @auth

    """
    Fetches a specific announcement by its ID.
    """
    getAnnouncement(id: ID!): Announcement @auth

    """
    Fetches all announcements (primarily for admin interfaces). Supports filtering and pagination.
    Requires appropriate permissions.
    """
    getAllAnnouncements(
        limit: Int = 50
        offset: Int = 0
        type: AnnouncementType
    ): [Announcement!]! @auth
}

extend type Mutation {
    """
    Marks a specific announcement as read for the current user.
    """
    markAnnouncementAsRead(announcementId: ID!): AnnouncementMutationResponse!
    @auth

    """
    Marks all currently unread announcements as read for the current user.
    """
    markAllAnnouncementsAsRead: AnnouncementMutationResponse! @auth

    # --- Admin Mutations (Require appropriate permissions) ---

    """
    Creates a new announcement. Requires admin privileges.
    """
    createAnnouncement(input: CreateAnnouncementInput!): Announcement! @auth

    """
    Updates an existing announcement. Requires admin privileges.
    """
    updateAnnouncement(id: ID!, input: UpdateAnnouncementInput!): Announcement!
    @auth

    """
    Deletes an announcement. Requires admin privileges.
    """
    deleteAnnouncement(id: ID!): AnnouncementMutationResponse! @auth
}
`, BuiltIn: false},
	{Name: "../schema/base.graphqls", Input: `directive @auth on FIELD_DEFINITION | INPUT_FIELD_DEFINITION

scalar Date
scalar Time
scalar Upload

type Query

type Mutation

type Subscription
`, BuiltIn: false},
	{Name: "../schema/club.graphqls", Input: `enum ClubCategory {
    MENTAL_MATH
}

enum ClubMembershipStatus {
    PENDING
    ACCEPTED
    REJECTED
}

enum ClubMemberRole {
    OWNER
    ADMIN
    MEMBER
}

type Club {
    id: ID!
    name: String!
    description: String
    visibility: Visibility!
    logoImage: String
    bannerImage: String
    category: ClubCategory
    createdAt: Time!
    createdBy: ID!
    forumId: ID!
    chatRoomId: ID!
    updatedAt: Time!
    membersCount: Int!
    clubEventsCount: Int!
    isAdmin: Boolean
    isClubMember: Boolean
    hasRequestedToJoin: Boolean
}

type CreatorInfo {
    username: String!
    profileImageUrl: String
    rating: Int
}

type ClubMember {
    id: ID!
    clubId: ID!
    userId: ID!
    role: ClubMemberRole!
    joinedAt: Time!
    clubMembershipStatus: ClubMembershipStatus!
    memberInfo: CreatorInfo
}

input CreateClubInput {
    name: String!
    description: String
    visibility: Visibility!
    logoImage: Upload
    bannerImage: Upload
    category: ClubCategory
}

input UpdateClubInput {
    clubId: ID!
    name: String
    description: String
    visibility: Visibility
}

type ClubMembersPage {
    results: [ClubMember!]!
    pageNumber: Int!
    pageSize: Int!
    hasMore: Boolean
    totalResults: Int
}

type ClubLeaderboard {
    results: [ClubLeaderboardEntry!]!
    pageNumber: Int!
    pageSize: Int!
    hasMore: Boolean
    totalResults: Int
}

type ClubsPage {
    results: [Club!]!
    pageNumber: Int!
    pageSize: Int!
    hasMore: Boolean
    totalResults: Int
}

type ClubLeaderboardEntry {
    user: UserPublicDetails!
    rank: Int!
}

extend type Query {
    club(id: ID!): Club @auth
    clubs(
        page: Int = 1
        pageSize: Int = 20
        visibility: Visibility
        search: String
    ): ClubsPage! @auth
    clubMembers(
        clubId: ID!
        clubMembershipStatus: ClubMembershipStatus!
        page: Int = 1
        pageSize: Int = 20
    ): ClubMembersPage! @auth
    getClubMemberInfo(clubId: ID!): ClubMember @auth
    getClubLeaderboard(
        clubId: ID!
        page: Int = 1
        pageSize: Int = 20
    ): ClubLeaderboard! @auth
}

extend type Mutation {
    createClub(input: CreateClubInput!): Club! @auth
    updateClub(input: UpdateClubInput!): Boolean! @auth
    uploadClubLogoImage(file: Upload!, clubId: ID!): File! @auth
    uploadClubBannerImage(file: Upload!, clubId: ID!): File! @auth
    deleteClub(id: ID!): Boolean! @auth

    joinClub(clubId: ID!): Boolean! @auth
    leaveClub(clubId: ID!): Boolean! @auth

    addClubMember(clubId: ID!, userId: ID!): Boolean! @auth
    updateMemberRole(
        clubId: ID!
        userId: ID!
        role: ClubMemberRole!
    ): ClubMember! @auth
    removeClubMember(clubId: ID!, userId: ID!): Boolean! @auth
}
`, BuiltIn: false},
	{Name: "../schema/clubAnnouncement.graphqls", Input: `type ClubAnnouncement {
    id: ID!
    clubId: ID!
    title: String!
    content: String!
    createdAt: Time!
    createdBy: ID!
    creatorInfo: ClubAnnouncementCreatorInfo
}

type ClubAnnouncementCreatorInfo {
    username: String!
    profileImageUrl: String
}

input CreateClubAnnouncementInput {
    clubId: ID!
    title: String!
    content: String!
}

type ClubAnnouncementsPage {
    results: [ClubAnnouncement!]!
    pageNumber: Int!
    pageSize: Int!
    hasMore: Boolean
    totalResults: Int
}

extend type Query {
    clubAnnouncements(
        page: Int = 1
        pageSize: Int = 20
        clubId: ID
        from: Time
        to: Time
    ): ClubAnnouncementsPage! @auth
}

extend type Mutation {
    createClubAnnouncement(
        input: CreateClubAnnouncementInput!
    ): ClubAnnouncement! @auth
    deleteClubAnnouncement(id: ID!): Boolean! @auth
}
`, BuiltIn: false},
	{Name: "../schema/clubEvent.graphqls", Input: `enum ClubEventType {
    CONTEST_80_IN_8
    SUMDAY_SHOWDOWN
}

type ClubEvent {
    id: ID!
    clubEventPlayId: ID!
    participationCount: Int!
    clubId: ID!
    title: String!
    visibility: Visibility
    description: String
    clubEventType: ClubEventType!
    startTime: Time!
    gameConfig: GameConfig!
    ratedEvent: Boolean
    openToAll: Boolean
    playerSetting: PlayerSetting
    createdBy: ID!
    createdAt: Time!
    updatedAt: Time!
}

type ClubEventParticipant {
    id: ID!
    clubEventId: ID!
    userId: ID!
    joinedAt: Time!
}

type PlayerSetting {
    minRating: Int!
    maxRating: Int!
}

input PlayerSettingInput {
    minRating: Int!
    maxRating: Int!
}

input CreateClubEventInput {
    clubId: ID!
    title: String!
    description: String
    clubEventType: ClubEventType!
    startTime: Time!
    gameConfig: GameConfigInput
    openToAll: Boolean
    playerSetting: PlayerSettingInput
    ratedEvent: Boolean
    visibility: Visibility
}

input UpdateClubEventInput {
    eventId: ID!
    title: String
    description: String
    startTime: Time
    endTime: Time
}

type ClubEventsPage {
    results: [ClubEvent!]!
    pageNumber: Int!
    pageSize: Int!
    hasMore: Boolean
    totalResults: Int
}

extend type Query {
    clubEvent(id: ID!): ClubEvent @auth
    clubEvents(
        page: Int = 1
        pageSize: Int = 20
        clubId: ID
        clubEventType: ClubEventType
        from: Time
        to: Time
    ): ClubEventsPage! @auth
}

extend type Mutation {
    createClubEvent(input: CreateClubEventInput!): ClubEvent! @auth
    updateClubEvent(input: UpdateClubEventInput!): ClubEvent! @auth
    deleteClubEvent(id: ID!): Boolean! @auth

    joinClubEvent(eventId: ID!): ClubEventParticipant! @auth
    leaveClubEvent(eventId: ID!): Boolean! @auth
}
`, BuiltIn: false},
	{Name: "../schema/contest.graphqls", Input: `type Contest {
    _id: ID!
    clubId: ID
    name: String!
    description: String
    hostedBy: String @deprecated(reason: "Use hostedByV2 instead")
    hostedByV2: HostDetails
    details: ContestDetails
    startTime: Time!
    endTime: Time!
    registrationCount: Int
    contestDuration: Int!
    registrationStartTime: Time
    registrationEndTime: Time
    encryptedQuestions: [String]
    registrationForm: RegistrationForm
    status: ContestStatus!
    currentUserParticipation: CurrentUserParticipation
    recentParticipants: [UserPublicDetails!]
}

type HostDetails {
    name: String
    logo: String
}

type ContestDetails {
    about: String
    requirements: String
    instructions: String
}

type ContestQuestion {
    _id: String!
    question: Question!
    points: Int!
}

type ContestParticipant {
    user: UserPublicDetails!
    registrationData: [RegistrationFieldData]
    score: Int!
    startTime: Time
    correctSubmission: Int
    incorrectSubmission: Int
    rank: Int
    lastSubmissionTime: Time
    isVirtualParticipant: Boolean
}

type CurrentUserParticipation {
    contestId: ID
    userId: ID
    registrationData: [RegistrationFieldData]
    score: Int
    lastSubmissionTime: Time
}

enum ContestStatus {
    UPCOMING
    REGISTRATION_OPEN
    ONGOING
    ENDED
}

input ContestDetailsInput {
    about: String
    requirements: String
    instructions: String
}

input CreateContestInput {
    name: String!
    description: String
    hostName: String
    hostLogo: String
    startTime: Time!
    endTime: Time!
    contestDuration: Int!
    registrationStartTime: Time
    details: ContestDetailsInput
    registrationEndTime: Time
    registrationForm: RegistrationFormInput!
}

type RegistrationFieldData {
    name: String!
    values: [String]
}

input ContestQuestionInput {
    questionId: ID!
    points: Int!
}

input RegistrationFormValuesInput {
    contestId: ID!
    formData: [RegistrationFormFieldValueInput!]!
}

input RegistrationFormFieldValueInput {
    name: String!
    values: [String]
}

type ContestLeaderboard {
    participants: [ContestParticipant!]!
    totalParticipants: Int!
}

type ContestSubmission {
    questionId: String!
    answer: String!
    isCorrect: Boolean!
    submissionTime: Time!
    points: Float!
}

type UserContestSubmissions {
    totalScore: Float!
    startTime: Time
    lastSubmissionTime: Time
    correctSubmission: Int
    incorrectSubmission: Int
    submissions: [ContestSubmission!]!
    user: UserPublicDetails
}

type UserContestResult {
    totalScore: Float!
    startTime: Time
    lastSubmissionTime: Time
    correctSubmission: Int
    incorrectSubmission: Int
    user: UserPublicDetails
    rank: Int
    questionsSolved: Int
    totalParticipants: Int
    isVirtualParticipant: Boolean
}

type PaginatedContests {
    contests: [Contest!]!
    totalCount: Int!
}

extend type Query {
    getFeaturedContests: [Contest!]! @auth
    getContestById(contestId: ID!): Contest! @auth
    getContestLeaderboard(
        contestId: ID!
        pageNumber: Int
        pageSize: Int
    ): ContestLeaderboard! @auth
    getRegisteredContests: [Contest!]! @auth
    getUserContestSubmissions(
        userId: ID
        contestId: ID!
    ): UserContestSubmissions! @auth
    getUserContestResult(contestId: ID!): UserContestResult! @auth
    getContestsByStatus(
        statuses: [ContestStatus!]!
        page: Int = 1
        pageSize: Int = 20
        sortDirection: String
    ): PaginatedContests! @auth
}

extend type Mutation {
    createContest(input: CreateContestInput!): Contest! @auth
    registerForContest(input: RegistrationFormValuesInput!): Boolean! @auth
    submitContestAnswer(
        contestId: ID!
        questionId: String!
        answer: String!
    ): Boolean! @auth
    unregisterFromContest(contestId: ID!): Boolean! @auth
    updateContestParticipantStartTime(contestId: ID!): Boolean! @auth

    submitVirtualContestAnswer(
        contestId: ID!
        questionId: String!
        answer: String!
    ): Boolean! @auth
    joinVirtualContest(contestId: ID!): Boolean! @auth
}

extend type Subscription {
    contestLeaderboardUpdated(contestId: ID!): ContestLeaderboard!
    @auth
    @deprecated(reason: "handled by custom websocket handler")
}
`, BuiltIn: false},
	{Name: "../schema/dailyChallenge.graphqls", Input: `type DailyChallenge {
    _id: ID!
    challengeStatus: String
    questions: [GameQuestion!]!
    startTime: Time
    endTime: Time
    hasAttempted: Boolean
    encryptedQuestions: [String]
    challengeNumber: Int
    division: CHALLENGE_DIVISION
    stats: DailyChallengeStat
}

type DailyChallengeStat {
    totalAttempts: Int
    totalSubmission: Int
    averageTime: Int
    bestTime: Int
    averageAccuracy: Float
}

type Result {
    userId: ID!
    challengeId: ID
    submittedTimes: [Int]
    score: Int
    completedAt: Time
}

type ChallengeResult {
    user: UserPublicDetails!
    rank: Int!
    score: Float!
}

type LeaderboardPage {
    results: [ChallengeResult!]!
    pageNumber: Int!
    pageSize: Int!
    hasMore: Boolean
    totalResults: Int
}

enum RESULT_STATUS {
    ATTEMPTED
    COMPLETED
}

enum CHALLENGE_DIVISION {
    OPEN
    DIV1
    DIV2
    DIV3
}

type DailyChallengeResult {
    userId: ID
    challengeId: ID
    score: Int
    completedAt: Time
    rank: Int
    statikCoinsEarned: Int
    resultStatus: RESULT_STATUS
}

type UserResult {
    success: Boolean
    error: String
    result: DailyChallengeResult
}

type UserDailyChallengeStats {
    _id: ID!
    userId: ID!
    division: CHALLENGE_DIVISION!
    totalAttempts: Int!
    totalSubmission: Int!
    averageTime: Int!
    bestTime: Int!
    streaks: UserDailyChallengeStreaks!
    averageAccuracy: Float
}

type UserDailyChallengeStreaks {
    current: Int!
    highest: Int!
    lastPlayedDate: Time
}

type DailyChallengeResultWithStats {
    result: DailyChallengeResult
    stats: UserDailyChallengeStats
}

type UserDailyChallengeResultWithStats {
    success: Boolean
    error: String
    result: DailyChallengeResultWithStats
}

type BotDetectionResult {
    isBotBehavior: Boolean!
    userId: ID!
    challengeId: ID!
}

# Define the Query type for getting the daily challenge
extend type Query {
    getUserResult(challengeNumber: Int): UserResult @auth
    getDailyChallenge: DailyChallenge @auth
    getDailyChallenges: [DailyChallenge] @auth
    getDailyChallengeById(id: ID!): DailyChallenge @auth
    getDailyChallengeLeaderboard(
        challengeNumber: Int
        pageNumber: Int
        pageSize: Int
    ): LeaderboardPage @auth
    checkBotBehavior(challengeId: ID!, userId: ID!): BotDetectionResult! @auth
    getUserResultByDivision(
        dateStr: String
        division: CHALLENGE_DIVISION
    ): UserResult @auth
    getUserResultByDailyChallengeId(
        challengeId: ID!
    ): UserDailyChallengeResultWithStats @auth
    getDailyChallengeLeaderboardByDivision(
        dateStr: String
        division: CHALLENGE_DIVISION
        pageNumber: Int
        pageSize: Int
    ): LeaderboardPage @auth
    getUserResultByDivison(
        dateStr: String
        division: CHALLENGE_DIVISION
    ): UserResult @auth @deprecated(reason: "Use getUserResultByDivision instead")
    getDailyChallengeLeaderboardByDivison(
        dateStr: String
        division: CHALLENGE_DIVISION
        pageNumber: Int
        pageSize: Int
    ): LeaderboardPage
    @auth
    @deprecated(reason: "Use getDailyChallengeLeaderboardByDivision instead")
}

# Define the input type for submitting the solution
input SubmitSolutionInput {
    challengeId: ID!
    submittedTimes: [Int]
    challengeNumber: Int
}

type SubmitChallengeResult {
    success: Boolean
    message: String
    result: Result
}

# Define the Mutation type for submitting the solution
extend type Mutation {
    submitChallengeResult(input: SubmitSolutionInput!): SubmitChallengeResult
    @auth
    attemptDailyChallenge(challengeId: ID!): Boolean! @auth
}
`, BuiltIn: false},
	{Name: "../schema/feed.graphqls", Input: `type FeedResponse {
    feeds: [UserFeed!]!
    lastId: ID
    hasMore: Boolean!
    userDetails: [UserPublicDetails!]!
    isRead: Boolean!
}

enum FEED_TYPE {
    CONNECTION
    MATIKS
    CELEBRATION
    DAILY_CHALLENGE
    LEAGUE
}

type UserFeed {
    _id: ID!
    userId: ID!
    feedType: FEED_TYPE!
    feedReferenceId: ID!
    isLiked: Boolean!
    expirationTime: Time!
    feedData: FeedData!
    createdAt: Time!
    imageUrl: String
}

type FeedData {
    _id: ID!
    sentAt: Time!
    title: String!
    description: String!
    expirationTime: Time!
    sentFor: ID!
    lastLikedByUserName: String
    likesCount: Int!
    additionalInfo: FeedAdditionalInfo
    feedForFriends: FeedForFriends
    createdAt: Time!
    updatedAt: Time!
}

type FeedForFriends {
    title: String!
    body: String!
}

type FeedAdditionalInfo {
    connectionRequest: ConnectionRequest
}

type ConnectionRequest {
    sentBy: ID!
}

extend type Query {
    getUserFeeds(lastId: ID, pageSize: Int): FeedResponse! @auth
}

extend type Mutation {
    updateLikeStatus(feedId: ID!): Boolean! @auth
    updateLastReadFeedId(lastReadFeedId: ID!): Boolean! @auth
}
`, BuiltIn: false},
	{Name: "../schema/forum.graphqls", Input: `type Forum {
    id: ID!
    clubId: ID!
    title: String!
    description: String
    createdAt: Time!
    updatedAt: Time!
    createdBy: ID!
    creatorInfo: CreatorInfo
}

type ForumThread {
    id: ID!
    forumId: ID!
    title: String!
    content: String!
    createdAt: Time!
    createdBy: ID!
    creatorInfo: CreatorInfo
}

type ForumReply {
    id: ID!
    threadId: ID!
    content: String!
    createdAt: Time!
    createdBy: ID!
    creatorInfo: CreatorInfo
}

input CreateForumInput {
    clubId: ID!
    title: String!
    description: String
}

input CreateForumThreadInput {
    forumId: ID!
    title: String!
    content: String!
}

input CreateForumReplyInput {
    threadId: ID!
    content: String!
}

type ThreadsPage {
    results: [ForumThread!]!
    pageNumber: Int!
    pageSize: Int!
    hasMore: Boolean
    totalResults: Int
}

type RepliesPage {
    results: [ForumReply!]!
    pageNumber: Int!
    pageSize: Int!
    hasMore: Boolean
    totalResults: Int
}

type ForumPage {
    results: [Forum!]!
    pageNumber: Int!
    pageSize: Int!
    hasMore: Boolean
    totalResults: Int
}

extend type Query {
    forum(id: ID!): Forum @auth
    forums(clubId: ID!, page: Int = 1, pageSize: Int = 20): ForumPage! @auth
    forumThread(id: ID!): ForumThread @auth
    forumThreads(forumId: ID!, page: Int = 1, pageSize: Int = 20): ThreadsPage!
    @auth
    forumReplies(threadId: ID!, page: Int = 1, pageSize: Int = 20): RepliesPage!
    @auth
}

extend type Mutation {
    createForum(input: CreateForumInput!): Forum! @auth
    createForumThread(input: CreateForumThreadInput!): ForumThread! @auth
    createForumReply(input: CreateForumReplyInput!): ForumReply! @auth
}
`, BuiltIn: false},
	{Name: "../schema/friends.graphqls", Input: `type FriendRequest {
    _id: ID!
    senderId: ID!
    receiverId: ID!
    status: FRIEND_REQUEST_STATUS
    sentAt: Time
    respondedAt: Time
}

type Friends {
    _id: ID!
    senderId: ID!
    receiverId: ID!
    acceptedAt: Time
}

type FollowersAndFollowee {
    _id: ID!
    followerId: ID!
    followeeId: ID!
    followedAt: Time
}

type FriendRequestOutput {
    _id: ID!
    senderId: ID!
    receiverId: ID!
    status: FRIEND_REQUEST_STATUS
    sentAt: Time
    respondedAt: Time
    sender: UserPublicDetails
}

type FriendsOutput {
    _id: ID!
    senderId: ID!
    receiverId: ID!
    acceptedAt: Time
    friendInfo: UserPublicDetails
    isOnline: Boolean
    currActivity: UserActivityType!
}

type FollowersAndFolloweeOutput {
    _id: ID!
    followerId: ID!
    followeeId: ID!
    followedAt: Time
    userInfo: UserPublicDetails
}

type FollowersAndFolloweePage {
    results: [FollowersAndFolloweeOutput!]!
    pageNumber: Int!
    pageSize: Int!
    hasMore: Boolean
    totalResults: Int
}

type FriendRequestPage {
    results: [FriendRequestOutput!]!
    pageNumber: Int!
    pageSize: Int!
    hasMore: Boolean
    totalResults: Int
}

type FriendsPage {
    results: [FriendsOutput!]!
    pageNumber: Int!
    pageSize: Int!
    hasMore: Boolean
    totalResults: Int
}

enum FRIEND_REQUEST_STATUS {
    ACCEPTED
    REJECTED
    PENDING
}

enum FRIENDSHIP_STATUS {
    REQUEST_SENT #Current User has sent request for Friendship
    ACCEPTED #Both Are Friends
    PENDING_REQUEST #Searched User has asked for Friendship
    NOT_FRIEND
}

input SendFriendRequestInput {
    userId: ID!
}

input FriendRequestInput {
    userId: ID!
}

input FollowUserInput {
    userId: ID!
}

input UnFollowUserInput {
    userId: ID!
}

input RemoveFollowerInput {
    userId: ID!
}

input WithdrawFriendRequestInput {
    userId: ID!
}

input RemoveFriendInput {
    userId: ID!
}

extend type Query {
    getFollowers(page: Int = 1, pageSize: Int = 20): FollowersAndFolloweePage
    @auth

    getFollowings(page: Int = 1, pageSize: Int = 20): FollowersAndFolloweePage
    @auth

    getFriends(
        page: Int = 1
        pageSize: Int = 20
        sortOption: SortOptions
    ): FriendsPage @auth

    getPendingFriendRequests(
        page: Int = 1
        pageSize: Int = 20
    ): FriendRequestPage @auth
}

extend type Mutation {
    sendFriendRequest(sendRequestInput: FriendRequestInput): Boolean! @auth
    withdrawFriendRequest(
        withdrawFriendRequestInput: WithdrawFriendRequestInput
    ): Boolean! @auth

    acceptFriendRequest(acceptRequestInput: FriendRequestInput): Boolean! @auth
    rejectFriendRequest(rejectRequestInput: FriendRequestInput): Boolean! @auth

    followUser(followUserInput: FollowUserInput): Boolean! @auth
    unFollowUser(unFollowUserInput: UnFollowUserInput): Boolean! @auth
    removeFollower(removeFollowerInput: RemoveFollowerInput): Boolean! @auth

    removeFriend(removeFriendInput: RemoveFriendInput): Boolean! @auth
}
`, BuiltIn: false},
	{Name: "../schema/game.graphqls", Input: `type Game {
    _id: ID!
    createdBy: ID!

    gameCategory: GAME_CATEGORY
    gameMode: GAME_MODE
    gameType: GAME_TYPE!
    gameStatus: GAME_STATUS!
    players: [Player!]!
    rematchRequestedBy: ID

    config: GameConfig @deprecated
    questions: [GameQuestion] @deprecated
    showdownGameConfig: ShowdownGameConfig @deprecated

    minifiedQuestions: [String]
    encryptedQuestions: [String]
    leaderBoard: [LeaderBoardEntry]
    startTime: Time
    endTime: Time
    seriesId: ID
    showdownId: ID
}

type GameTypeSpecificConfig {
    type: GAME_TYPE!
}

type GameCategorySpecificConfig {
    category: GAME_CATEGORY!
    blitz: DefaultGameConfig
    classical: DefaultGameConfig
    memory: DefaultGameConfig
    puzzle: PuzzleGameConfig
}

type GroupPlayGameConfig {
    maxTimePerQuestion: Int
    difficultyLevel: [Int!]
    maxGapBwGame: Int
    maxPlayers: Int
    minPlayers: Int
    questionTags: [String]
}

type DefaultGameConfig {
    timeLimit: Int
}

type DefaultGameModeConfig {
    numPlayers: Int
}

type GameModeSpecificConfig {
    mode: GAME_MODE!
    sumdayShowdown: ShowdownGameConfig
    onlineSearch: DefaultGameModeConfig
    onlineChallenge: DefaultGameModeConfig
    practice: DefaultGameModeConfig
    rushWithTime: DefaultGameModeConfig
    rushWithoutTime: DefaultGameModeConfig
    groupPlay: GroupPlayGameConfig
    playViaLink: DefaultGameModeConfig
    survivalSaturday: DefaultGameModeConfig
}

enum GAME_MODE {
    ONLINE_SEARCH
    ONLINE_CHALLENGE
    PRACTICE
    RUSH_WITH_TIME
    RUSH_WITHOUT_TIME
    GROUP_PLAY
    PLAY_VIA_LINK
    SUMDAY_SHOWDOWN
    SURVIVAL_SATURDAY
}

enum GAME_CATEGORY {
    BLITZ
    CLASSICAL
    MEMORY
    PUZZLE
}

type Player {
    userId: ID!
    rating: Int
    statikCoins: Int
    status: PLAYER_STATUS
    timeLeft: Int
}

type GameQuestion {
    question: Question
    submissions: [Submission]
    stats: GameQuestionStats
}

type GameQuestionStats {
    fastestTime: Int
    userIds: [ID]
}

type Submission {
    userId: ID
    timeTaken: Int
    points: Int
    submissionTime: Date
    isCorrect: Boolean
    inCorrectAttempts: Int
    submittedValues: [String]
}

type LeaderBoardEntry {
    userId: ID
    correct: Int
    incorrect: Int
    totalPoints: Float
    ratingChange: Int
    statikCoinsEarned: Int
    rank: Int
}

type GameConfig {
    "timeLimit in seconds"
    timeLimit: Int @deprecated
    numPlayers: Int @deprecated
    gameType: GAME_TYPE @deprecated
    questionTags: [String] @deprecated
    difficultyLevel: [Int!] @deprecated
    maxTimePerQuestion: Int @deprecated

    categorySpecificConfig: GameCategorySpecificConfig
    gameTypeSpecificConfig: GameTypeSpecificConfig
    modeSpecificConfig: GameModeSpecificConfig
}

input GameConfigInput {
    timeLimit: Int @deprecated
    numPlayers: Int @deprecated
    gameType: GAME_TYPE @deprecated
    questionTags: [String!] @deprecated
    difficultyLevel: [Int!] @deprecated
    maxTimePerQuestion: Int @deprecated

    categorySpecificConfig: GameCategorySpecificConfigInput
    gameTypeSpecificConfig: GameTypeSpecificConfigInput
    modeSpecificConfig: GameModeSpecificConfigInput
}

input DefaultGameConfigInput {
    timeLimit: Int
}

input GameCategorySpecificConfigInput {
    category: GAME_CATEGORY!
    blitz: DefaultGameConfigInput
    classical: DefaultGameConfigInput
    memory: DefaultGameConfigInput
    puzzle: PuzzleGameConfigInput
}

input GameTypeSpecificConfigInput {
    type: GAME_TYPE!
}

input DefaultGameModeConfigInput {
    numPlayers: Int
}

input GroupPlayGameConfigInput {
    maxTimePerQuestion: Int
    difficultyLevel: [Int!]
    maxGapBwGame: Int
    maxPlayers: Int
    minPlayers: Int
    questionTags: [String]
}

input ShowdownGameConfigInput {
    numberOfGames: Int
}

input GameModeSpecificConfigInput {
    mode: GAME_MODE!
    sumdayShowdown: ShowdownGameConfigInput
    onlineSearch: DefaultGameModeConfigInput
    onlineChallenge: DefaultGameModeConfigInput
    practice: DefaultGameModeConfigInput
    rushWithTime: DefaultGameModeConfigInput
    rushWithoutTime: DefaultGameModeConfigInput
    groupPlay: GroupPlayGameConfigInput
    playViaLink: DefaultGameModeConfigInput
    survivalSaturday: DefaultGameModeConfigInput
}

enum GAME_STATUS {
    CREATED
    READY
    STARTED
    PAUSED
    ENDED
    CANCELLED
}

enum GAME_TYPE {
    PLAY_ONLINE @deprecated
    PLAY_WITH_FRIEND @deprecated
    ONLINE_CHALLENGE @deprecated
    PRACTICE @deprecated
    SUMDAY_SHOWDOWN @deprecated
    GROUP_PLAY @deprecated
    ABILITY_DUELS @deprecated

    #Blitz Games Category
    FASTEST_FINGER
    DMAS
    DMAS_TIME_BANK

    # Classical Games Category
    DMAS_ABILITY

    # Memory Games Category
    FLASH_ANZAN

    # Puzzle Games Category
    CROSS_MATH_PUZZLE
    KEN_KEN_PUZZLE
}

enum PLAYER_STATUS {
    INVITED
    ACCEPTED
    REJECTED
}

input JoinGameInput {
    gameId: ID
}

input SubmitAnswerInput {
    gameId: ID
    questionId: String
    submittedValue: String
    isCorrect: Boolean
    inCorrectAttempts: Int
    timeOfSubmission: Date
    gameTypeSpecificAnswer: GameTypeSpecificAnswerInput
}

input FlashAnzanAnswerInput {
    maxScore: Int
}

input GameTypeSpecificAnswerInput {
    type: GAME_TYPE!
    flashAnzan: FlashAnzanAnswerInput
}

input SubmitFlashAnzanAnswerInput {
    gameId: ID
    questionIdentifier: String
    submittedValue: String
    isCorrect: Boolean
    timeOfSubmission: Date
    maxScore: Int
}

input StartGameInput {
    gameId: ID
}

type MinifiedGame {
    _id: ID!
    players: [Player!]!
    config: GameConfig
    leaderBoard: [LeaderBoardEntry]
    startTime: Time
    endTime: Time
}

type GetGamesOutput {
    games: [MinifiedGame]
    users: [UserPublicDetails]
}

type GetGamesByRatingOutput {
    games: [MinifiedGame]
    puzzleGames: [MinifiedPuzzleGame]
    users: [UserPublicDetails]
    totalCount: Int
}

input TimeRangeInput {
    startTime: Time
    endTime: Time
}

input PageInfoInput {
    pageNumber: Int
    rows: Int
}

input GetGamesInput {
    userId: ID
    timeRange: TimeRangeInput
    pageInfo: PageInfoInput
}

input GetGamesByRatingInput {
    userId: ID
    pageInfo: PageInfoInput
    ratingType: String
}

type GameDetailedAnalysis {
    game: Game!
    questions: [GameQuestionAnalysis!]!
}

type GameQuestionAnalysis {
    question: Question!
    avgTimes: [UserAvgTime!]!
    globalAvgTime: Float!
    globalBestTime: Float!
}

type UserAvgTime {
    userId: String!
    questionAvgTime: Float!
    presetAvgTime: Float!
    presetBestTime: Float!
}

input ChallengeUserInput {
    userId: ID
    gameConfig: GameConfigInput
}

type ChallengeOutput {
    gameId: ID!
    challengedBy: ID!
    gameConfig: GameConfig!
    createdAt: Time!
    status: ChallengeStatus
    opponent: User
}

enum ChallengeStatus {
    CHALLENGE_SENT
    CHALLENGE_ACCEPTED
    CHALLENGE_EXPIRED
    CHALLENGE_REJECTED

    GAME_CANCELLED
}

extend type Query {
    getGameById(gameId: ID): Game @auth
    getGamesByUser(payload: GetGamesInput): GetGamesOutput @auth
    getUserGamesByRatingType(
        payload: GetGamesByRatingInput
    ): GetGamesByRatingOutput @auth
    getGameDetailedAnalysis(gameId: ID): GameDetailedAnalysis @auth
}

extend type Mutation {
    createGame(gameConfig: GameConfigInput): Game @auth
    submitAnswer(answerInput: SubmitAnswerInput): Game @auth

    joinGame(joinGameInput: JoinGameInput): Game @auth
    leaveGame(gameId: ID!): Game @auth

    startGame(startGameInput: StartGameInput): Game @auth
    endGame(gameId: ID): Game
    removePlayer(gameId: ID!, playerId: ID!): Boolean! @auth

    startSearching(gameConfig: GameConfigInput): Boolean @auth
    abortSearching: Boolean @auth
    cancelGame(gameId: ID!): Boolean @auth

    requestRematch(gameId: ID!): Boolean! @auth
    acceptRematch(gameId: ID!): Game! @auth
    rejectRematch(gameId: ID!): Boolean! @auth
    cancelRematchRequest(gameId: ID!): Boolean! @auth

    challengeUser(challengeUserInput: ChallengeUserInput): Game @auth
    acceptChallenge(gameId: ID!): Game @auth
    rejectChallenge(gameId: ID!): Boolean @auth

    # Deprecated
    submitFlashAnzanAnswer(answerInput: SubmitFlashAnzanAnswerInput): Game @auth @deprecated
    endAbilityDuelsGame(gameId: ID): Game @auth @deprecated
    endGameForShowdown(gameId: ID): Game @auth @deprecated
}

type SubscriptionOutput {
    game: Game
    event: String
    question: Question
}

type SearchSubscriptionOutput {
    game: Game
    event: String
    opponent: User
}

extend type Subscription {
    searchPlayer(userId: ID): SearchSubscriptionOutput @deprecated
    gameEvent(gameId: ID): SubscriptionOutput @deprecated
    rematchRequest(gameId: ID!): RematchRequestOutput! @deprecated
}
`, BuiltIn: false},
	{Name: "../schema/gameSeries.graphqls", Input: `type GameSeries {
    _id: ID!
    gameIds: [ID!]!
    playerIds: [ID!]!
}

extend type Query {
    getGameSeriesById(gameSeriesId: ID!): GameSeries @auth
}
`, BuiltIn: false},
	{Name: "../schema/institution.graphqls", Input: `type Institution {
    id: ID!
    name: String!
    domains: [String!]!
    country: String
    state: String
    city: String
    slug: String!
}

input CreateInstitutionInput {
    name: String!
    domains: [String!]
    country: String
    state: String
    city: String
}

extend type Query {
    searchInstitutions(query: String!, limit: Int = 10): [Institution!]!
}

extend type Mutation {
    createInstitution(input: CreateInstitutionInput!): Institution!
}
`, BuiltIn: false},
	{Name: "../schema/leagues.graphqls", Input: `type League {
    id: ID!
    name: String!
    details: LeagueDetails
    hostedBy: String @deprecated(reason: "Use hostedByV2 instead")
    hostedByV2: HostDetails
    registrationStart: Time!
    registrationEnd: Time!
    leagueStart: Time!
    leagueEnd: Time!
    registrationCount: Int
    chatRoomId: ID!
    currentUserParticipation: LeagueParticipant
    currentUserResult: LeagueLeaderboardEntry
    registrationForm: RegistrationForm
}

type LeagueDetails {
    about: String
    requirements: String
    instructions: String
    awards: String
}

type LeagueParticipant {
    id: ID!
    userId: ID!
    leagueId: ID!
    joinedAt: Time!
    registrationData: [RegistrationFieldData]
}

enum LeagueStatus {
    LIVE
    ENDED
    REGISTRATION_OPEN
    UPCOMING
}

type PaginatedLeagues {
    league: [League!]!
    totalCount: Int
}

type ActivitySummaryEntry {
    activity: String!
    coins: Int!
}

type LeagueLeaderboardEntry {
    user: User!
    statikCoins: Int!
    rank: Int!
    activitySummary: [ActivitySummaryEntry!]!
}

type LeagueLeaderboardPage {
    participants: [LeagueLeaderboardEntry!]!
    totalCount: Int
}

input CreateLeagueInput {
    name: String!
    hostedBy: String
    registrationStart: Time!
    registrationEnd: Time!
    leagueStart: Time!
    leagueEnd: Time!
    registrationFormInput: RegistrationFormInput
    details: LeagueDetailsInput
    hostLogo: String
}

input JoinLeagueInput {
    leagueId: ID!
    formData: [RegistrationFormFieldValueInput!]!
}

input LeagueDetailsInput {
    about: String
    requirements: String
    instructions: String
    awards: String
}

extend type Query {
    getLeague(id: ID!): League! @auth
    getLeaguesByStatus(
        statuses: [LeagueStatus!]!
        page: Int = 1
        pageSize: Int = 20
        sortDirection: String
    ): PaginatedLeagues @auth
    getLeagueLeaderboard(
        leagueId: ID!
        page: Int!
        pageSize: Int!
    ): LeagueLeaderboardPage! @auth
}

extend type Mutation {
    createLeague(input: CreateLeagueInput!): League! @auth
    joinLeague(joinLeagueInput: JoinLeagueInput): Boolean! @auth
}
`, BuiltIn: false},
	{Name: "../schema/messages.graphqls", Input: `type MessageGroup {
    _id: ID!
    groupName: String
    lastMessageRead: [MessageRead]
    members: [ID]!
    alias: String!
    messages: [Message]
    groupType: GroupType!
    createdAt: Time!
    updatedAt: Time!
    lastMessage: Message
    userInfoIfIndividual: UserDetailsForMessage
    deepLinkRoute: String
}

type MessageRead {
    userId: ID!
    lastMessageRead: ID!
}

type UserDetailsForMessage {
    _id: ID!
    name: String
    username: String!
    profileImageUrl: String
    rating: Int
}

enum SortDirection {
    ASC
    DESC
}

enum GroupType {
    INDIVIDUAL
    COMMUNITY
    GAME
    SHOWDOWN
    LEAGUE
}

type CreateMessageInput {
    groupId: ID!
    content: String!
    attachment: [Attachment]
    sender: ID!
}

type Message {
    _id: ID!
    groupId: ID!
    content: String!
    attachment: [Attachment]
    sender: ID!
    createdAt: Time!
    senderInfo: UserDetailsForMessage
}

type Attachment {
    type: AttachmentType!
    url: String!
}

enum AttachmentType {
    IMAGE
    FILE
}

input GetAllMessageGroupsInput {
    page: Int!
    pageSize: Int
}

type PaginatedMessageGroups {
    groups: [MessageGroup]
    nextPage: Int
    hasMore: Boolean!
    isRead: Boolean!
}

type PaginatedMessage {
    messages: [Message]
    lastMessageId: ID
    hasMore: Boolean!
}

extend type Query {
    getMessagesByGroupId(
        groupId: ID!
        lastMessageId: ID
        pageSize: Int
        sortDirection: SortDirection
    ): PaginatedMessage @auth
    getMessageGroupDetailsById(groupId: ID!): MessageGroup @auth
    getAllMessageGroups(input: GetAllMessageGroupsInput): PaginatedMessageGroups
    @auth
    getMessageGroupIdForFriends(friendID: ID!): ID! @auth
}

extend type Mutation {
    updateLastMessageRead(groupId: ID!, lastMessageReadId: ID!): Boolean! @auth
}
`, BuiltIn: false},
	{Name: "../schema/misc.graphqls", Input: `type PlatformStats {
    totalUsers: Int!
    totalGames: Int!
    totalSignedInUsers: Int!
}

enum Visibility {
    PUBLIC
    PRIVATE
}

enum SortOrder {
    ASC
    DESC
}

input SortOptions {
    sortBy: String!
    sortDirection: Int!
}

extend type Query {
    getPlatformStats: PlatformStats!
}

type File {
    name: String!
    content: String!
    contentType: String!
    url: String!
}

extend type Mutation {
    uploadFiles(files: [Upload!]!): [File!]! @auth
    sendOTP(email: String!): Boolean! @auth
    verifyOTP(otp: String!): Boolean! @auth
}
`, BuiltIn: false},
	{Name: "../schema/notifications.graphqls", Input: `input Feedback {
    email: String!
    phone: String!
    message: String!
}

extend type Mutation {
    registerDeviceToken(
        pushNotificationToken: String!
        deviceId: String
        platform: String
    ): DeviceTokenRegistrationResponse @auth
    unregisterDeviceToken(
        pushNotificationToken: String
        deviceId: String
    ): DeviceTokenRegistrationResponse @auth
    sendFeedback(input: Feedback!): Boolean @auth
}
`, BuiltIn: false},
	{Name: "../schema/presets.graphqls", Input: `type GlobalPreset {
    _id: ID!
    identifier: String!
    globalAverageTime: Float
    bestTime: Float
    totalQuestionsSolved: Int
    bestStreak: Int
    numOfCorrectSubmissions: Int
    globalAccuracy: Float
    incorrectSubmissions: Int
    top10Mathletes: [Mathlete]
}

type UserPreset {
    _id: ID!
    globalPresetId: ID!
    userId: ID!
    identifier: String!
    name: String
    questionsSolved: Int
    curAvgTime: Float
    curAvgAccuracy: Float
    incorrectSubmissions: Int
    bestTime: Float
    last10Time: [Int]
    last10IncorrectAttempts: [Int]
    bestStreak: Int
    numOfCorrectSubmissions: Int
    saved: Boolean
    savedConfig: String
}

type UserPresetStats {
    _id: ID!
    date: Time
    userPresetId: ID!
    globalPresetId: ID!
    userId: ID!
    identifier: String!
    avgTime: Float
    avgAccuracy: Float
    questionsSolved: Int
    numOfCorrectSubmissions: Int
    incorrectSubmissions: Int
    bestStreak: Int
    timePerformanceTrend: [Int]
    inaccuracyPerformanceTrend: [Int]
}

type Mathlete {
    userId: ID!
    questionsSolved: Int
    bestTime: Float
    bestStreak: Int
    numOfCorrectSubmissions: Int
}

type GlobalPresets {
    globalPresets: [GlobalPreset]
    totalCount: Int!
}

type UserPresets {
    userPresets: [UserPreset]
    totalCount: Int!
}

type UserPresetDayStats {
    date: Time
    userPresetStats: UserPresetStats
}

enum PresetCategory {
    ADD
    ADDSUB
    MULT
    DIV
}

input UserPresetResultInput {
    userPresetResults: [UserPresetResult]
}

input UserPresetResult {
    identifier: String
    numOfQuestions: Int
    submittedTimes: [Int]
    incorrectAttempts: [Int]
    bestStreak: Int
    numOfCorrectSubmissions: Int
    date: Time
    savedConfig: String
}

type PlayedPresets {
    identifier: String
    avgTime: Float
}

type AllPlayedPresetsOutput {
    presets: [PlayedPresets]
    count: Int
}

type UserPresetStatsGraph {
    userStats: [UserPresetDayStats]
    globalStats: [UserPresetDayStats]
}

extend type Mutation {
    submitUserPresetResult(userPresetResultInput: UserPresetResultInput): Boolean
    @auth
    saveUserPreset(identifier: String, name: String): UserPreset @auth
    deleteUserSavedPreset(presetId: ID!): Boolean @auth
}

extend type Query {
    getGlobalPresets(page: Int, pageSize: Int): GlobalPresets @auth
    getGlobalPresetsByIdentifier(identifier: String): GlobalPreset @auth
    getUserPresetsByIdentifier(identifier: String): UserPreset @auth
    getUserPresetStatsByDate(
        username: String
        durationFilter: Int
        identifier: String
    ): [UserPresetDayStats] @auth
    # TODO: Implement this
    # getGlobalPresetsStatsByDate(durationFilet: Int, identifier: String): [GlobalPresetDayStats] @auth
    getUserRecentPresets: UserPresets @auth
    getUserSavedPresets(page: Int = 1, pageSize: Int = 10): UserPresets @auth
    getUsersAllPlayedPresets(username: String): AllPlayedPresetsOutput @auth
}
`, BuiltIn: false},
	{Name: "../schema/puzzle.graphqls", Input: `type Cell {
    isVisible: Boolean!
    value: String!
    type: CellType!
}

enum PuzzleType {
    KenKen
    CrossMath
    Hectoc
}

enum CellType {
    Operator
    Operand
    EmptyBlock
}

type CrossMathPuzzle {
    puzzleString: String
}

type KenKenPuzzle {
    puzzleString: String
}

type HectocPuzzle {
    puzzleString: String
}

type PuzzleTypeSpecificDetails {
    puzzleType: PuzzleType!
    crossMath: CrossMathPuzzle
    kenKen: KenKenPuzzle
    hectoc: HectocPuzzle
}

type Puzzle {
    id: ID!
    difficulty: String!
    solvedBy: Int
    cells: [[Cell!]!]! @deprecated
    puzzleType: PuzzleType
    puzzleDate: String
    availableAnswers: [String!]
    hasAttempted: Boolean
    currentUserResult: PuzzleResult
    stats: PuzzleStats
    userStat: PuzzleUserStats
    typeSpecific: PuzzleTypeSpecificDetails
}

type PuzzleStats {
    numOfSubmission: Int!
    averageTime: Int!
    bestTime: Int!
}

type PuzzleUserStats {
    id: ID!
    userId: ID!
    bestTime: Int
    averageTime: Int
    numOfSubmission: Int
    puzzleType: PuzzleType
}

type PuzzleResult {
    id: ID!
    userId: ID
    puzzleId: ID
    timeSpent: Int
    completedAt: Time
    statikCoinsEarned: Int
    puzzleDate: String!
    puzzleType: PuzzleType
}

# Add this new type for the overall monthly submission report
type PuzzleMonthlySubmissionReport {
    yearMonth: String!
    puzzleSubmissions: [PuzzleResult!]
}

extend type Query {
    getDailyPuzzle(date: String!): Puzzle @auth @deprecated
    getPuzzleSubmissionsByMonth(
        yearMonths: [String!]!
    ): [PuzzleMonthlySubmissionReport!] @auth @deprecated
    getUserPuzzleStats: PuzzleUserStats! @auth @deprecated

    getDailyPuzzleByType(date: String!, puzzleType: PuzzleType!): Puzzle @auth
    getPuzzleSubmissionsByMonthByType(
        yearMonths: [String!]!
        puzzleType: PuzzleType!
    ): [PuzzleMonthlySubmissionReport!] @auth
    getUserPuzzleStatsByType(puzzleType: PuzzleType!): PuzzleUserStats! @auth
}

extend type Mutation {
    submitPuzzleSolution(puzzleId: ID!, timeSpent: Int!): PuzzleResult! @auth
}

input CellInput {
    pos: Int!
    value: String!
}
`, BuiltIn: false},
	{Name: "../schema/puzzleGame.graphqls", Input: `type PuzzleGame {
    _id: ID!
    players: [Player!]!
    gameStatus: PUZZLE_GAME_STATUS!
    rematchRequestedBy: ID
    gameType: PUZZLE_GAME_TYPE!
    createdBy: ID!
    config: PuzzleGameConfig
    questions: [PuzzleGameQuestion]
    leaderBoard: [PuzzleLeaderboardEntry]
    isRatedGame: Boolean
    startTime: Time
    endTime: Time
    seriesId: ID
}

type PuzzleGameQuestion {
    _id: ID!
    question: String
    submissions: [PuzzleQuestionSubmission]
    stats: PuzzleGameQuestionStats
}

type PuzzleGameQuestionStats {
    fastestTime: Int
    userIds: [ID]
}

type PuzzleQuestionSubmission {
    userId: ID
    timeTaken: Int
    points: Int
    submissionTime: Date
    isCorrect: Boolean
    inCorrectAttempts: Int
    submittedValues: [String]
}

type PuzzleLeaderboardEntry {
    userId: ID
    correct: Int
    incorrect: Int
    totalPoints: Float
    ratingChange: Int
    statikCoinsEarned: Int
    rank: Int
}

type PuzzleGameConfig {
    timeLimit: Int #"timeLimit in seconds"
    numPlayers: Int @deprecated
    numOfQuestions: Int
    gameType: PUZZLE_GAME_TYPE @deprecated
    difficultyLevel: [Int!] @deprecated
    maxTimePerQuestion: Int @deprecated
}

input PuzzleGameConfigInput {
    timeLimit: Int
    numPlayers: Int @deprecated
    numOfQuestions: Int
    gameType: PUZZLE_GAME_TYPE @deprecated
    difficultyLevel: [Int!] @deprecated
    maxTimePerQuestion: Int @deprecated
}

enum PUZZLE_GAME_STATUS {
    CREATED
    READY
    STARTED
    PAUSED
    ENDED
    CANCELLED
}

enum PUZZLE_GAME_TYPE {
    CROSS_MATH_PUZZLE_DUEL
    CROSS_MATH_PUZZLE_WITH_FRIEND
}

input SubmitPuzzleGameAnswerInput {
    gameId: ID
    questionId: String
    isCorrect: Boolean
    inCorrectAttempts: Int
    timeOfSubmission: Date
}

type MinifiedPuzzleGame {
    _id: ID!
    players: [Player!]!
    config: PuzzleGameConfig
    leaderBoard: [PuzzleLeaderboardEntry]
    startTime: Time
    endTime: Time
}

type GetPuzzleGamesOutput {
    games: [MinifiedPuzzleGame]
    users: [UserPublicDetails]
}

type CrossMathPuzzleRush {
    _id: ID!
    userId: ID!
    bestAllTime: Int
    isNewBestScore: Boolean
    createdAt: Time
    updatedAt: Time
}

input SubmitPuzzleRushGame {
    score: Int
    timeSpent: Int
}

type CrossMathPuzzleRushPlayerInfo {
    rank: Int
    score: Int
    userInfo: UserPublicDetails
}

type CrossMathPuzzleRushStats {
    bestAllTime: Int
    globalRank: Int
    friendsRank: Int
}

input GetPuzzleGamesInput {
    userId: ID
    timeRange: TimeRangeInput
    pageInfo: PageInfoInput
}

input ChallengeUserForPuzzleGameInput {
    userId: ID
    gameConfig: PuzzleGameConfigInput
}

extend type Query {
    getPuzzleGameById(gameId: ID!): PuzzleGame @auth
    getPuzzleGamesByUser(payload: GetPuzzleGamesInput): GetPuzzleGamesOutput @auth
    getMyCrossMathPuzzleRushStats: CrossMathPuzzleRushStats @auth
    getGlobalTop5CrossMathPuzzleRushStats: [CrossMathPuzzleRushPlayerInfo!] @auth
    getFriendsTop5CrossMathPuzzleRushStats: [CrossMathPuzzleRushPlayerInfo!] @auth
}

extend type Mutation {
    createPuzzleGame(gameConfig: PuzzleGameConfigInput): PuzzleGame @auth
    submitPuzzleGameAnswer(answerInput: SubmitPuzzleGameAnswerInput): PuzzleGame
    @auth
    joinPuzzleGame(gameId: ID!): PuzzleGame @auth
    removePlayerFromPuzzleGame(gameId: ID!, playerId: ID!): Boolean! @auth
    leavePuzzleGame(gameId: ID!): PuzzleGame @auth
    startPuzzleGame(gameId: ID!): PuzzleGame @auth
    endPuzzleGame(gameId: ID!): PuzzleGame @auth
    startSearchingForPuzzleGame(gameConfig: PuzzleGameConfigInput): Boolean @auth
    abortSearchingForPuzzleGame: Boolean @auth
    cancelPuzzleGame(gameId: ID!): Boolean @auth

    requestRematchForPuzzleGame(gameId: ID!): Boolean! @auth
    acceptRematchOfPuzzleGame(gameId: ID!): PuzzleGame! @auth
    rejectRematchOfPuzzleGame(gameId: ID!): Boolean! @auth

    challengeUserForPuzzleGame(
        challengeUserInput: ChallengeUserForPuzzleGameInput
    ): PuzzleGame @auth
    acceptChallengeOfPuzzleGame(gameId: ID!): PuzzleGame @auth
    rejectChallengeOfPuzzleGame(gameId: ID!): Boolean @auth

    submitPuzzleGameRush(input: SubmitPuzzleRushGame!): CrossMathPuzzleRush @auth
}
`, BuiltIn: false},
	{Name: "../schema/question.graphqls", Input: `type Question {
    id: String
    expression: [String]!
    description: String
    options: [String]
    answers: [String]
    questionType: QuestionType
    rating: Int
    maxTimeLimit: Int
    tags: [String]
    fastestTimeTaken: Int
    presetIdentifier: String
}

"""
TODO: derive the questionutils types dynamically
"""
enum QuestionType {
    SINGLE_CHOICE
    MULTI_CHOICE
    FILL_IN_THE_BLANKS
}
`, BuiltIn: false},
	{Name: "../schema/refferal.graphqls", Input: `type Referral {
    _id: ID!
    referrer: ID!
    referredTo: ID!
    referredAt: Time
}
`, BuiltIn: false},
	{Name: "../schema/registrationForm.graphqls", Input: `type RegistrationForm {
    _id: ID!
    fields: [FormField!]!
}

type FormField {
    _id: ID!
    name: String!
    type: FieldType!
    label: String!
    required: Boolean!
    options: [String]
    validation: FieldValidation
}

enum FieldType {
    TEXT
    NUMBER
    EMAIL
    MOBILE
    SINGLE_SELECT
    MULTI_SELECT
    CHECKBOX
    RADIO
    IMAGE_INPUT
    FILE_INPUT
}

type FieldValidation {
    regex: String
    min: Int
    max: Int
    emailSuffix: String
    emailSuffixes: [String!]
    needManualVerification: Boolean
}

input RegistrationFormInput {
    fields: [FormFieldInput!]!
}

input FormFieldInput {
    name: String!
    type: FieldType!
    label: String!
    required: Boolean!
    options: [String]
    validation: FieldValidationInput
}

input FieldValidationInput {
    regex: String
    min: Int
    max: Int
    emailSuffix: String
    emailSuffixes: [String!]
    needManualVerification: Boolean
}
`, BuiltIn: false},
	{Name: "../schema/showdown.graphqls", Input: `type Showdown {
    _id: ID
    clubId: ID
    name: String!
    description: String!
    Instructions: [String]
    hostedBy: HostInfo

    isRatedEvent: Boolean
    rounds: Int!
    startTime: Time!
    endTime: Time!
    registrationCount: Int
    registrationStartTime: Time
    registrationEndTime: Time
    registrationForm: RegistrationForm
    currentRound: Int!
    details: showdownDetails!

    duration: Int!
    roundTime: Int!
    gapBwRounds: Int!
    status: SHOWDOWN_CONTEST_STATUS!

    roundConfig: RoundConfig!
    stats: ShowdownStats
    currentUserParticipation: CurrentShowdonParticipant
    createdAt: Time!
    updatedAt: Time!
    recentParticipants: [UserPublicDetails!]
}

type showdownDetails {
    format: String
    rules: String
}

type ShowdownStats {
    totalParticipants: Int
    totalGamesPlayed: Int
}

type ShowdownParticipant {
    _id: ID
    userID: ID!
    showdownId: ID!
    status: SHOWDOWN_PARTICIPANT_STATUS!
    registrationData: [RegistrationFieldData]
    stats: ShowdownParticipantStats
    rounds: [ShowdownRound]!
    recentOpponents: [ID!]!
    rank: Int
    totalScore: Float!
    ratingChange: Int!
    # points: Int!
    hadABye: Boolean!
    createdAt: Time!
    updatedAt: Time!
    userInfo: ShowdownUserDetails
}

type ParticipantBasicInfo {
    _id: ID
    userID: ID!
    showdownId: ID!
    rounds: [ShowdownRound]!
    userInfo: ShowdownUserDetails
}

type ShowdownUserDetails {
    name: String
    username: String!
    profileImageUrl: String
    rating: Int
}

type ShowdownParticipantStats {
    currentScore: Int
    win: Int
    loss: Int
    draw: Int
}

type CurrentShowdonParticipant {
    showdownId: ID
    userId: ID
    registrationData: [RegistrationFieldData]
    lastSubmissionTime: Time
    stats: ShowdownParticipantStats!
    rank: Int
    rounds: [ShowdownRound]
    hadABye: Boolean!
    currentGame: ID
    recentOpponent: ID
    totalScore: Float!
    currentRound: ShowdownRound
}

type ShowdownGameConfig {
    isRoundEnded: Boolean!
    hasOpponentNotShown: Boolean!
    nextGameId: ID
    round: Int!
    nextGameStartsAt: Time
    totalGamesPlayed: Int!
    showdownGamePlayer: [ShowdownGamePlayer]!
    numOfGames: Int!
}

type ShowdownGamePlayer {
    isTie: Boolean!
    isWinner: Boolean!
    userId: ID!
    wins: Float!
    score: Float!
}

type HostInfo {
    id: ID
    hostType: HOST_TYPE
    hostLogo: String
}

type RoundConfig {
    gameDuration: Int!
    numOfPlayer: Int!
    gameType: GAME_TYPE!
    maxGapBwGame: Int!
    maxWaitTime: Int!
    numOfGames: Int!
}

type Fictures {
    id: ID!
    showdownId: ID!
    users: [ShowdownParticipantDetail]!
    round: Int!
    participants: [ID!]!
}

type ShowdownParticipantDetail {
    showdownParticipant: ShowdownParticipant
    currentRound: ShowdownRound!
}

type FicturesCollection {
    currentUserFicture: Fictures
}

type ShowdownRound {
    opponent: ID!
    round: Int!
    score: Float!
    games: [ID!]!
    wins: Float!
    loose: Float!
    totalGamesPlayed: Int!
    playerStatus: RoundPlayerStatus!
    isBye: Boolean!
    isRoundEnded: Boolean!
    hasJoined: Boolean!
    hasOpponentNotShown: Boolean!
    hasFailedToPlay: Boolean!
}

enum RoundPlayerStatus {
    OPPONENT_ABSENT
    BYE
    DID_NOT_PLAY
    BOTH_DID_NOT_PLAY
    PENDING_JOIN
    ROUND_COMPLETED
}

type LeaderParticipantEntity {
    _id: ID!
    participant: ParticipantBasicInfo!
    score: Float!
    rank: Int!
    showdownId: ID!
    userId: ID!
}

type PaginatedLeaderboard {
    participants: [LeaderParticipantEntity]!
    count: Int!
}

enum SHOWDOWN_CONTEST_STATUS {
    UPCOMING
    REGISTRATION_OPEN
    REGISTRATION_ENDED
    FICTURES_CREATED
    LIVE
    FICTURES_PENDING
    ENDED
    ARCHIVED
}

enum SHOWDOWN_PARTICIPANT_STATUS {
    ACTIVE
    DISQUALIFIED
}

enum HOST_TYPE {
    MATIKS
    ORGANIZATION
    USER
}

input RoundConfigInput {
    gameDuration: Int!
    numOfPlayer: Int!
    gameType: GAME_TYPE!
    maxGapBwGame: Int!
    maxWaitTime: Int!
    numOfGames: Int!
}

input HostInfoInput {
    id: ID
    hostType: HOST_TYPE
    hostLogo: String
}

input ShowdownDetailsInput {
    format: String
    rules: String
}

input CreateShowdownInput {
    name: String!
    description: String!
    hostedBy: HostInfoInput!
    isRatedEvent: Boolean
    startTime: Time!
    details: ShowdownDetailsInput!
    rounds: Int!
    gapBwRounds: Int!
    registrationStartTime: Time!
    registrationEndTime: Time!
    roundConfig: RoundConfigInput!
    registrationForm: RegistrationFormInput!
}

input ShowdownRegistrationFormValuesInput {
    showdownId: ID!
    # formData: [RegistrationFormFieldValueInput]!
}

input StartGameForShowdownInput {
    showdownId: ID!
    userId: ID!
    gameId: ID!
}

input PaginatedLeaderboardInput {
    showdownId: ID!
    page: Int!
}

type PaginatedShowdowns {
    showdowns: [Showdown!]!
    count: Int!
}

extend type Query {
    getShowdownById(showdownId: ID!): Showdown! @auth
    getUpcomingShowdown: Showdown! @auth
    getShowdownByStatus(status: SHOWDOWN_CONTEST_STATUS): Showdown! @auth
    getFicturesByShowdownId(showdownId: ID!): FicturesCollection! @auth
    getPaginatedLeaderboard(
        input: PaginatedLeaderboardInput
    ): PaginatedLeaderboard! @auth
    getFeaturedShowdown: [Showdown] @auth
    getShowdownsByStatus(
        statuses: [SHOWDOWN_CONTEST_STATUS!]!
        page: Int = 1
        pageSize: Int = 20
        sortDirection: String
    ): PaginatedShowdowns! @auth
}

extend type Mutation {
    createShowdown(input: CreateShowdownInput!): Showdown! @auth
    registerForShowdown(input: ShowdownRegistrationFormValuesInput!): Boolean!
    @auth
    unregisterFromShowdown(showdownId: ID!): Boolean! @auth
    startGameForShowdown(input: StartGameForShowdownInput): Game @auth
}
`, BuiltIn: false},
	{Name: "../schema/streakShieldTransaction.graphqls", Input: `enum TRANSACTION_TYPE {
    DEBITED
    CREDITED
}

enum EARN_VIA {
    REFERRAL
    PURCHASED
    OTHER
}

type StreakShieldTransaction {
    _id: ID!
    userId: ID!
    quantity: Int!
    transactionType: TRANSACTION_TYPE!
    earnVia: EARN_VIA
    referralId: ID
    transactionId: String
    redeemedOn: [Time]
    createdAt: Time!
    updatedAt: Time!
}

input CreateStreakShieldTransactionInput {
    userId: ID!
    quantity: Int!
    transactionType: TRANSACTION_TYPE!
    earnVia: EARN_VIA
    referralId: ID
    transactionId: String
}

type ReferralDetails {
    referral: Referral!
    referredUser: UserPublicDetails!
}

type StreakShieldTransactionOutput {
    transaction: StreakShieldTransaction!
    referralDetails: ReferralDetails
}

type StreakShieldTransactionPage {
    results: [StreakShieldTransactionOutput!]!
    pageNumber: Int!
    pageSize: Int!
    hasMore: Boolean
    totalResults: Int
}

extend type Query {
    getUserStreakShieldTransactions(
        page: Int = 1
        pageSize: Int = 20
    ): StreakShieldTransactionPage! @auth
}
`, BuiltIn: false},
	{Name: "../schema/user.graphqls", Input: `type User {
    _id: ID!
    email: String
    token: String
    name: String
    username: String!
    bio: String
    country: [String]
    links: [String]
    awardsAndAchievements: [AwardsAndAchievements]
    phoneNumber: PhoneNumber
    profileImageUrl: String
    rating: Int
    ratingV2: UserRating
    statikCoins: Int
    badge: BadgeType
    countryCode: String
    isGuest: Boolean
    isBot: Boolean
    isShadowBanned: Boolean
    shadowBanStatus: UserShadowBanStatus
    suspiciousActivity: Boolean
    globalRank: Int
    previousGlobalRank: Int
    countryRank: Int
    previousCountryRank: Int
    stats: UserStats
    hasFixedRating: Boolean
    isSignup: Boolean
    userStreaks: UserStreaks
    timezone: String
    additional: UserAdditional
    league: LeagueInfo
    institutionId: ID
    lastReadFeedId: ID
    referralCode: String
    isReferred: Boolean
    isDeleted: Boolean
    accountStatus: UserAccountStatus
}

enum UserAccountStatus {
    ACTIVE
    INACTIVE
    BANNED
    DELETED
    SUSPENDED
}

enum LeagueType {
    MATIKAN
    RUBY
    DIAMOND
    GOLD
    SILVER
    BRONZE
}

enum UserShadowBanStatus {
    NONE
    PARTIAL
    FULL
}

enum WeeklyLeagueProgressState {
    PROMOTION
    DEMOTION
    NO_CHANGE
}

type LeagueInfo {
    league: LeagueType
    groupId: Int
    updatedAt: Time
    hasParticipated: Boolean
    coinsTillLastWeek: Int
    progressState: WeeklyLeagueProgressState
}

type UserRating {
    globalRating: Int
    flashAnzanRating: Int
    abilityDuelsRating: Int
    puzzleRating: Int
}

type UserAdditional {
    timeSpent: Int
    hasUnlockedAllGames: Boolean
}

type AwardsAndAchievements {
    imageUrl: String
    link: String
    title: String
    description: String
}

enum BadgeType {
    ROOKIE
    NOVICE
    AMATEUR
    EXPERT
    CANDIDATE_MASTER
    MASTER
    GRANDMASTER
    LEGENDARY_GRANDMASTER
    GOAT
}

enum UserActivityType {
    USER_IN_LOBBY
    IN_GAME
    IN_CONTEST
    SEARCHING_FOR_OPPONENT
    PLAYING_DC
    IN_DC_WAITING_ROOM
    VIEWING_DC_LEADERBOARD
    EXPLORING
    PRACTICING_NETS
    SELECTING_CONFIG
    FIXING_RATING
    ONBOARDING
    VIEWING_GAME_RESULT
    ON_CHAT_PAGE
}

type UserPublicDetails {
    _id: ID!
    name: String
    username: String
    profileImageUrl: String
    rating: Int
    badge: BadgeType
    countryCode: String
    isGuest: Boolean
    globalRank: Int
    previousGlobalRank: Int
    countryRank: Int
    previousCountryRank: Int
    stats: UserStats
    userStreaks: UserStreaks
    ratingV2: UserRating
    institutionId: ID
}

type UserStats {
    ngp: Int
    hr: Int
    followersCount: Int
    followingsCount: Int
    friendsCount: Int
    last10BotGames: [UserGame]
    games: [UserGame]
}

type UserGame {
    id: String
    sT: Time
}

type PhoneNumber {
    countryCode: String
    number: String
}

type LeaderboardConnection {
    edges: [LeaderboardEdge]
    pageInfo: PageInfo!
}

type UserLeaderboardPage {
    edges: [LeaderboardEdge]
    totalCount: Int!
}

type LeaderboardEdge {
    cursor: String!
    node: UserPublicDetails!
}

type PageInfo {
    hasNextPage: Boolean!
    endCursor: String
}

input UserInput {
    email: String!
    name: String
    password: String!
    confirm: String!
}

type DeviceTokenRegistrationResponse {
    success: Boolean!
    message: String!
}

type UserRatingFixtureSubmission {
    id: ID!
    userId: ID!
    submissions: [Int!]!
    userScore: Int!
    timeTaken: Int!
    createdAt: Time
    updatedAt: Time
    currentRating: Int!
    proposedRating: Int!
    userStance: UserStance!
}

enum UserStance {
    PENDING
    ACCEPTED
    REJECTED
}

enum UserOnlineStatus {
    ONLINE
    OFFLINE
}

input AwardsAndAchievementsInput {
    imageUrl: String
    link: String
    title: String
    description: String
}

input UpdateUserInput {
    name: String
    profileImageUrl: String
    countryCode: String
    timezone: String
    username: String
    bio: String
    country: [String]
    links: [String]
    awardsAndAchievements: [AwardsAndAchievementsInput]
    institutionId: ID
}

type SearchUserOutput {
    userPublicDetails: User
    isFollowing: Boolean
    friendshipStatus: FRIENDSHIP_STATUS
}

type UserDetailWithActivity {
    userInfo: UserPublicDetails!
    currActivity: UserActivityType!
}

type RatingFixtureOutput {
    newRating: Int
}

type JoinedWeeklyLeagueEvent {
    leagueInfo: LeagueInfo!
}

type OnlineUsersPage {
    users: [UserDetailWithActivity!]!
    pageNumber: Int!
    pageSize: Int!
    hasMore: Boolean
    totalResults: Int
}

enum StatikCoinLeaderboardType {
    DAILY
    MONTHLY
    WEEKLY
    ALL_TIME
}

type StatikCoinLeaderboardEntry {
    user: UserPublicDetails
    statikCoins: Int
    rank: Int
}

type WeeklyLeagueLeaderboardEntry {
    user: UserPublicDetails
    statikCoins: Int
    rank: Int
    progressState: WeeklyLeagueProgressState
}

type WeeklyLeagueLeaderboardPage {
    results: [WeeklyLeagueLeaderboardEntry!]!
    currentUserLeague: LeagueInfo
    pageNumber: Int!
    pageSize: Int!
    hasMore: Boolean
    totalResults: Int
}

type StatikCoinLeaderboardPage {
    results: [StatikCoinLeaderboardEntry!]!
    pageNumber: Int!
    pageSize: Int!
    hasMore: Boolean
    totalResults: Int
}

type MyInstituteUsersPage {
    results: [UserPublicDetails!]!
    pageNumber: Int!
    pageSize: Int!
    hasMore: Boolean
    totalResults: Int
}

# Apple SignIn
input AppleSignInFullNameInput {
    familyName: String
    givenName: String
    middleName: String
    namePrefix: String
    nameSuffix: String
    nickname: String
}

input AppleSignInInput {
    authorizationCode: String!
    email: String
    fullName: AppleSignInFullNameInput
    identityToken: String!
    realUserStatus: Int
    state: String
    user: String
}

type TopPlayerEntry {
    user: UserPublicDetails!
    rating: Int!
    rank: Int!
}

type TopPlayersLeaderboard {
    globalRating: [TopPlayerEntry]!
    memoryRating: [TopPlayerEntry]!
    abilityRating: [TopPlayerEntry]!
}

type UsersWeeklyStatikCoinsOutput {
    totalCoins: Int!
    dailyCoins: [Int!]!
}

extend type Query {
    login(email: String!, password: String!): User
    verifyToken(token: String!): User
    getUserById(userId: ID!): User @auth
    getCurrentUser: User @auth
    getUserByUsername(username: String): SearchUserOutput! @auth
    getUserLeagueGroupLeaderboard(
        page: Int = 1
        pageSize: Int = 30
    ): WeeklyLeagueLeaderboardPage @auth
    leaderboardV3(
        countryCode: String
        searchKey: String
        page: Int
        limit: Int
    ): UserLeaderboardPage @auth
    leaderboardNew(
        countryCode: String
        searchKey: String
        page: Int
        limit: Int
        ratingType: String
    ): UserLeaderboardPage @auth
    leaderboard(
        countryCode: String
        searchKey: String
        first: Int
        after: String
    ): LeaderboardConnection @auth
    getRatingFixtureQuestions: [Question!]! @auth
    getRatingFixtureSubmission: UserRatingFixtureSubmission! @auth
    isUsernameAvailable(username: String!): Boolean! @auth
    onlineUsers(page: Int!, pageSize: Int!): OnlineUsersPage! @auth
    statikCoinsLeaderboard(
        page: Int!
        pageSize: Int
        leaderboardType: StatikCoinLeaderboardType
    ): StatikCoinLeaderboardPage! @auth
    getTimeSpentByUser(date: String): Int! @auth @deprecated
    getGlobalTopPlayers: TopPlayersLeaderboard! @auth
    getFriendsLeaderboard(
        page: Int
        pageSize: Int
        ratingType: String
    ): UserLeaderboardPage @auth
    getFriendsTopPlayers: TopPlayersLeaderboard! @auth
    getUsersWeeklyStatikCoins: Int! @auth @deprecated
    getUsersWeeklyStatikCoinsV2(userId: ID!): UsersWeeklyStatikCoinsOutput @auth

    getUsersOfMyInstitute(page: Int, pageSize: Int): MyInstituteUsersPage @auth
    searchUsersInMyInstitute(
        searchKey: String
        page: Int
        pageSize: Int
    ): MyInstituteUsersPage @auth
}

extend type Mutation {
    createUser(userInput: UserInput): User @auth
    loginAsGuest(guestId: ID!): User
    signInWithApple(input: AppleSignInInput!): User!
    googleLogin(
        auth_code: String!
        token_type: String
        expires_in: String
        guestId: ID
    ): User
    legacyGoogleLogin(idToken: String!, guestId: ID): User
    updateUser(updateUserInput: UpdateUserInput): User @auth
    submitRatingFixtureResponses(
        submission: [Int]!
        timeTaken: Int!
    ): UserRatingFixtureSubmission! @auth
    updateRatingBasedOnFixtureResponse(userStance: UserStance!): User! @auth
    uploadProfilePicture(file: Upload!): File! @auth
    submitReferral(referralCode: String!): Boolean! @auth
    deleteUser: Boolean! @auth
}

union UserEvent =
    | RematchRequestOutput
    | BadgeAssignedEvent
    | SearchSubscriptionOutput
    | ChallengeOutput
    | GameCanceledOutput
    | RatingFixtureOutput
    | JoinedWeeklyLeagueEvent

type GameCanceledOutput {
    gameId: String
    creatorId: String
    opponentId: String
    status: ChallengeStatus
}

type RematchRequestOutput {
    gameId: String
    requestedBy: String
    status: String
    newGameId: String
    user: User
    waitingTime: Int
    gameType: String
}

type BadgeAssignedEvent {
    initialBadge: BadgeType
    newBadge: BadgeType
}

extend type Subscription {
    userEvents(userId: ID): UserEvent!
}
`, BuiltIn: false},
	{Name: "../schema/userResolution.graphqls", Input: `type UserResolution {
    id: ID!
    duration: Int!
    userId: ID!
    CreatedAt: Time
}

extend type Query {
    checkIfPledgeTaken: Boolean @auth
    getUserResolution: UserResolution @auth
}

extend type Mutation {
    takePledge(duration: Int): Boolean @auth
}
`, BuiltIn: false},
	{Name: "../schema/userSettings.graphqls", Input: `type UserSettings {
    _id: ID!
    userId: ID!
    playSound: Boolean!
    hapticFeedback: Boolean!
}

input UpdateSettingsInput {
    playSound: Boolean
    hapticFeedback: Boolean
}

extend type Query {
    getUserSettings: UserSettings @auth
}

extend type Mutation {
    updateUserSettings(settings: UpdateSettingsInput): UserSettings @auth
}
`, BuiltIn: false},
	{Name: "../schema/userStreak.graphqls", Input: `type UserStreaks {
    currentStreak: Int
    longestStreak: Int
    lastPlayedDate: Time
    lastSevenDays: [Boolean]
    streakHistory: [Time]
    streakFreezers: Int
}

type StreakHistory {
    _id: ID!
    streakHistoryObj: [StreakEntry]!
}

type StreakDay {
    date: Time!
    activity: Boolean!
    isShieldUsed: Boolean!
}

type StreakEntry {
    date: Time!
    isShieldUsed: Boolean!
}

type StreakStatusResponse {
    hasStreak: Boolean!
    streakFreezers: Int!
    missedDays: Int!
    canSaveStreak: Boolean!
    lostStreakCount: Int!
    lastSevenDays: [StreakDay!]
}

extend type Query {
    # TODO: should expect date
    getUserStreakHistoryByMonth(yearMonths: [String!]!): [StreakEntry] @auth
    checkUserStreakStatus: StreakStatusResponse! @auth
}

extend type Mutation {
    useStreakFreezer: Boolean! @auth
    getUpdatedUserStreaks: UserStreaks @auth
}
`, BuiltIn: false},
}
var parsedSchema = gqlparser.MustLoadSchema(sources...)
